# 📦 前端 Monorepo 架构优化方案

## 🎯 优化目标

1. **减少 node_modules 重复**: 从 1.5GB → 800MB (节省 47%)
2. **统一依赖版本**: 避免版本冲突和不一致
3. **共享通用代码**: API、工具函数、类型定义
4. **简化维护**: 统一的构建、测试、部署流程

---

## 🏗️ 重构后的目录结构

```text
frontend/
├── package.json                 # 根 workspace 配置
├── packages/
│   ├── shared/                  # 共享包 🔄
│   │   ├── api/                 # 统一的 API 模块
│   │   ├── components/          # 通用业务组件
│   │   ├── types/              # TypeScript 类型定义
│   │   ├── utils/              # 工具函数
│   │   ├── constants/          # 常量定义
│   │   └── package.json
│   │
│   ├── website/                 # 官网项目 🌐
│   │   ├── src/
│   │   │   ├── views/
│   │   │   │   ├── Home.vue           # 首页展示
│   │   │   │   ├── ProductIntro.vue   # 产品介绍
│   │   │   │   ├── Features.vue       # 功能特性
│   │   │   │   ├── CaseStudy.vue      # 案例展示
│   │   │   │   ├── Pricing.vue        # 价格方案
│   │   │   │   ├── Contact.vue        # 联系我们
│   │   │   │   └── assessment/        # Web评估模块
│   │   │   │       ├── ScaleList.vue      # 量表列表
│   │   │   │       ├── OnlineFill.vue     # 在线填写
│   │   │   │       ├── ResultPreview.vue  # 结果预览
│   │   │   │       ├── ReportDownload.vue # 报告下载
│   │   │   │       └── History.vue        # 历史记录
│   │   │   ├── components/        # 官网专用组件
│   │   │   └── store/            # 官网状态管理
│   │   └── package.json
│   │
│   ├── admin/                   # 管理后台 ⚙️
│   │   ├── src/
│   │   │   ├── views/            # 现有管理页面
│   │   │   ├── components/       # 管理后台组件
│   │   │   └── store/           # 管理后台状态
│   │   └── package.json
│   │
│   └── mobile/                  # 移动端 📱
│       ├── src/                 # 现有移动端代码
│       └── package.json
│
└── node_modules/               # 共享依赖 (只有一个)
```

---

## 📦 依赖优化策略

### 1. 共享核心依赖

```json
// 根 package.json - 共享依赖
{
  "workspaces": ["packages/*"],
  "devDependencies": {
    // 构建工具 - 全局共享
    "vite": "^5.4.19",
    "vue-tsc": "^2.0.6", 
    "typescript": "^5.4.0",
    "eslint": "^8.57.0",
    "prettier": "^3.2.5",
    
    // Vue 核心 - 全局共享
    "vue": "^3.4.25",
    "vue-router": "^4.3.2",
    "pinia": "^3.0.3",
    
    // 通用工具 - 全局共享
    "lodash-es": "^4.17.21",
    "dayjs": "^1.11.10",
    "axios": "^1.6.8"
  }
}
```

### 2. 项目特定依赖

```json
// packages/website/package.json
{
  "dependencies": {
    "@shared/api": "workspace:*",
    "@shared/components": "workspace:*",
    "@shared/types": "workspace:*",
    
    // 官网特定依赖
    "element-plus": "^2.6.3",
    "@vueuse/core": "^10.9.0",
    "echarts": "^5.5.0"
  }
}

// packages/admin/package.json  
{
  "dependencies": {
    "@shared/api": "workspace:*",
    "@shared/components": "workspace:*",
    "@shared/types": "workspace:*",
    
    // 管理后台特定依赖
    "element-plus": "^2.6.3",
    "tailwindcss": "^4.0.0",
    "@element-plus/icons-vue": "^2.3.1"
  }
}

// packages/mobile/package.json
{
  "dependencies": {
    "@shared/api": "workspace:*",
    "@shared/types": "workspace:*",
    
    // 移动端特定依赖
    "@dcloudio/uni-app": "3.0.0-4060620250520001",
    "@dcloudio/uni-ui": "^1.5.7",
    "pinia-plugin-persistedstate": "^4.3.0"
  }
}
```

---

## 🚀 官网功能扩展计划

### 1. 营销展示模块

#### 🏠 首页优化 (基于现有 HomeView.vue)
```vue
<template>
  <div class="homepage">
    <!-- Hero 区域 -->
    <HeroSection />
    
    <!-- 功能特性 (已有) -->
    <FeaturesSection />
    
    <!-- 产品演示 -->
    <ProductDemo />
    
    <!-- 客户案例 -->
    <CaseStudies />
    
    <!-- 立即体验 CTA -->
    <CallToAction />
  </div>
</template>
```

#### 📋 新增营销页面
```text
├── ProductIntro.vue     # 产品介绍
│   ├── 产品概述
│   ├── 核心价值
│   ├── 技术优势
│   └── 适用场景
│
├── Features.vue         # 功能特性  
│   ├── 评估功能
│   ├── 管理功能
│   ├── 分析功能
│   └── 集成功能
│
├── CaseStudy.vue       # 案例展示
│   ├── 养老院案例
│   ├── 社区服务案例
│   ├── 医疗机构案例
│   └── ROI 数据展示
│
├── Pricing.vue         # 价格方案
│   ├── 基础版
│   ├── 专业版  
│   ├── 企业版
│   └── 定制方案
│
└── Contact.vue         # 联系我们
    ├── 联系方式
    ├── 在线咨询
    ├── 预约演示
    └── 合作伙伴
```

### 2. Web 评估体验模块

#### 📝 在线评估功能
```text
├── assessment/
│   ├── ScaleList.vue          # 评估量表列表
│   │   ├── 能力评估量表
│   │   ├── 认知评估量表
│   │   ├── 心理评估量表
│   │   └── 风险评估量表
│   │
│   ├── OnlineFill.vue         # 在线填写评估
│   │   ├── 渐进式表单
│   │   ├── 实时保存
│   │   ├── 进度提示
│   │   └── 智能验证
│   │
│   ├── ResultPreview.vue      # 评估结果预览
│   │   ├── 分数展示
│   │   ├── 图表分析
│   │   ├── 建议方案
│   │   └── 对比分析
│   │
│   ├── ReportDownload.vue     # 报告下载
│   │   ├── PDF 报告
│   │   ├── Word 报告
│   │   ├── 数据导出
│   │   └── 邮件发送
│   │
│   └── History.vue           # 评估历史
│       ├── 历史记录
│       ├── 趋势分析
│       ├── 对比功能
│       └── 数据管理
```

---

## 📈 性能和开发体验提升

### 1. 构建优化
- **并行构建**: 多个项目同时构建
- **增量构建**: 只构建变更的包
- **共享缓存**: 构建缓存跨项目复用

### 2. 开发体验
- **统一启动**: 一键启动所有服务
- **热重载**: 共享代码变更实时更新
- **类型安全**: 跨包的完整 TypeScript 支持

### 3. 部署策略
- **独立部署**: 每个包可独立部署
- **版本管理**: 统一的版本号管理
- **CI/CD**: 自动化构建和部署

---

## 🛠️ 迁移实施计划

### Phase 1: 基础结构搭建 (1-2天)
1. 创建 Monorepo 结构
2. 配置 workspace
3. 提取共享依赖

### Phase 2: 共享包创建 (2-3天)  
1. 创建 @shared/api
2. 创建 @shared/types
3. 创建 @shared/components
4. 创建 @shared/utils

### Phase 3: 项目迁移 (3-4天)
1. 迁移 admin 项目
2. 迁移 mobile 项目  
3. 重构 website 项目

### Phase 4: 官网功能开发 (5-7天)
1. 营销页面开发
2. Web 评估模块开发
3. 性能优化
4. SEO 优化

---

## 📊 预期收益

| 指标 | 优化前 | 优化后 | 改善 |
|------|--------|--------|------|
| **依赖大小** | 1.5GB | 800MB | ⬇️ 47% |
| **安装时间** | 5-8分钟 | 3-4分钟 | ⬇️ 40% |
| **构建时间** | 15分钟 | 8分钟 | ⬇️ 47% |
| **版本冲突** | 经常 | 基本无 | ⬇️ 90% |
| **代码复用** | 30% | 70% | ⬆️ 133% |

---

## 🎯 结论

**推荐实施 Monorepo 架构**，理由：

1. **✅ 显著的性能提升**: 减少近 50% 的依赖大小和构建时间
2. **✅ 更好的开发体验**: 统一的工具链和更好的代码复用
3. **✅ 易于维护**: 版本统一，减少依赖冲突
4. **✅ 可扩展性**: 为后续功能扩展提供良好基础

**移动端依赖分开是必要的**，因为 uni-app 有其特殊的依赖需求，但可以通过 workspace 实现代码共享。