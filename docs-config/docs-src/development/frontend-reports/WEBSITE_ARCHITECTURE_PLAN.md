# 智慧养老评估平台 - 官网架构规划

## 🎯 项目定位

**根目录项目**：智慧养老评估平台官方网站 + Web量表填写系统

### 📊 功能模块规划

#### 1. 官网宣传模块

```text
src/views/
├── home/              # 首页
│   ├── HeroSection.vue      # 主视觉区域
│   ├── FeatureSection.vue   # 功能特性
│   ├── StatsSection.vue     # 数据统计
│   └── CTASection.vue       # 行动召唤
├── about/             # 关于我们
│   ├── CompanyInfo.vue      # 公司介绍
│   ├── TeamSection.vue      # 团队介绍
│   └── ContactInfo.vue      # 联系方式
├── products/          # 产品介绍
│   ├── ProductOverview.vue  # 产品概览
│   ├── FeatureDetails.vue   # 功能详情
│   └── TechSpecs.vue        # 技术规格
├── solutions/         # 解决方案
│   ├── NursingHome.vue      # 养老机构方案
│   ├── Community.vue        # 社区服务方案
│   └── Medical.vue          # 医疗机构方案
└── cases/             # 客户案例
    ├── CaseList.vue         # 案例列表
    └── CaseDetail.vue       # 案例详情
```

#### 2. Web量表填写模块

```text
src/views/assessment/
├── scale-list/        # 量表列表
│   ├── ScaleGrid.vue        # 量表网格展示
│   ├── ScaleCard.vue        # 量表卡片
│   └── ScaleFilter.vue      # 筛选组件
├── scale-fill/        # 量表填写
│   ├── ScaleForm.vue        # 量表表单
│   ├── QuestionItem.vue     # 题目组件
│   ├── ProgressBar.vue      # 进度条
│   └── SaveDraft.vue        # 保存草稿
├── result/            # 结果展示
│   ├── ScoreDisplay.vue     # 分数展示
│   ├── ReportPreview.vue    # 报告预览
│   └── ShareResult.vue      # 分享结果
└── history/           # 历史记录
    ├── AssessmentList.vue   # 评估列表
    └── AssessmentDetail.vue # 评估详情
```

#### 3. 用户系统模块

```text
src/views/user/
├── login/             # 登录注册
│   ├── LoginForm.vue        # 登录表单
│   ├── RegisterForm.vue     # 注册表单
│   └── ForgotPassword.vue   # 忘记密码
├── profile/           # 个人中心
│   ├── UserInfo.vue         # 个人信息
│   ├── AssessmentHistory.vue # 评估历史
│   └── Settings.vue         # 设置
└── dashboard/         # 用户仪表板
    ├── Overview.vue         # 概览
    ├── RecentActivity.vue   # 最近活动
    └── QuickActions.vue     # 快捷操作
```

### 🎨 设计风格建议

#### 1. 视觉设计
- **主色调**：医疗蓝 + 温暖橙（体现专业性和温馨感）
- **字体**：现代简洁，适老化考虑
- **布局**：响应式设计，移动端友好

#### 2. 用户体验
- **导航**：清晰的面包屑导航
- **表单**：分步骤填写，自动保存
- **反馈**：实时验证，友好的错误提示
- **无障碍**：支持键盘导航，屏幕阅读器

### 🔧 技术实现建议

#### 1. 路由规划
```javascript
const routes = [
  // 官网页面
  { path: '/', component: () => import('@/views/home/<USER>') },
  { path: '/about', component: () => import('@/views/about/AboutPage.vue') },
  { path: '/products', component: () => import('@/views/products/ProductPage.vue') },
  { path: '/solutions', component: () => import('@/views/solutions/SolutionPage.vue') },
  { path: '/cases', component: () => import('@/views/cases/CasePage.vue') },
  
  // 量表系统
  { path: '/assessment', component: () => import('@/views/assessment/AssessmentLayout.vue'),
    children: [
      { path: 'scales', component: () => import('@/views/assessment/scale-list/ScaleList.vue') },
      { path: 'fill/:id', component: () => import('@/views/assessment/scale-fill/ScaleFill.vue') },
      { path: 'result/:id', component: () => import('@/views/assessment/result/ResultPage.vue') },
      { path: 'history', component: () => import('@/views/assessment/history/HistoryPage.vue') }
    ]
  },
  
  // 用户系统
  { path: '/user', component: () => import('@/views/user/UserLayout.vue'),
    children: [
      { path: 'login', component: () => import('@/views/user/login/LoginPage.vue') },
      { path: 'register', component: () => import('@/views/user/login/RegisterPage.vue') },
      { path: 'profile', component: () => import('@/views/user/profile/ProfilePage.vue') },
      { path: 'dashboard', component: () => import('@/views/user/dashboard/DashboardPage.vue') }
    ]
  }
]
```

#### 2. 状态管理扩展
```javascript
// 量表相关状态
export const useAssessmentStore = defineStore('assessment', {
  state: () => ({
    currentScale: null,
    answers: {},
    progress: 0,
    draftSaved: false
  })
})

// 网站内容状态
export const useContentStore = defineStore('content', {
  state: () => ({
    siteConfig: {},
    announcements: [],
    featuredCases: []
  })
})
```

### 📱 与其他项目的协作

#### 1. 数据共享
- **共享组件**：使用 `/shared` 目录的通用组件
- **API接口**：与admin和uni-app共享后端API
- **用户系统**：统一的用户认证和权限管理

#### 2. 功能互补
- **官网** → **Admin**：引导管理员登录
- **官网** → **Mobile**：提供移动端下载链接
- **Web量表** → **Admin**：数据管理和分析
- **Web量表** → **Mobile**：移动端继续填写

### 🚀 开发优先级

#### Phase 1: 基础官网 (2-3周)
- [ ] 首页重构
- [ ] 产品介绍页面
- [ ] 基础用户系统
- [ ] 响应式布局

#### Phase 2: 量表系统 (3-4周)
- [ ] 量表列表和展示
- [ ] 在线填写功能
- [ ] 结果展示和分享
- [ ] 草稿保存功能

#### Phase 3: 高级功能 (2-3周)
- [ ] 客户案例展示
- [ ] 在线演示系统
- [ ] SEO优化
- [ ] 性能优化

### 💡 SEO和营销考虑

#### 1. SEO优化
- **Meta标签**：动态设置页面标题和描述
- **结构化数据**：添加JSON-LD标记
- **站点地图**：自动生成sitemap.xml
- **页面速度**：图片懒加载，代码分割

#### 2. 营销功能
- **联系表单**：潜在客户信息收集
- **在线客服**：集成客服系统
- **数据统计**：Google Analytics集成
- **A/B测试**：关键页面的转化优化

这个架构设计既保持了项目的独立性，又充分发挥了Web端的优势，是一个很好的规划！
