# 🧪 测试标准总览

## 🎯 测试体系概述

智慧养老评估平台建立了完整的测试体系，确保代码质量、功能正确性和系统稳定性。通过多层次的测试策略，保证产品质量和用户体验。

## 🏗️ 测试架构体系

### 测试金字塔
- **单元测试**: 代码级别的功能验证
- **集成测试**: 模块间的集成验证
- **系统测试**: 端到端的功能验证
- **验收测试**: 业务需求的验证

### 测试覆盖范围
- **功能测试**: 核心业务功能验证
- **性能测试**: 系统性能和压力测试
- **安全测试**: 安全漏洞和防护测试
- **兼容性测试**: 多环境兼容性验证

## 📚 测试文档

### 🔧 测试环境配置
- [TESTING_SETUP](./TESTING_SETUP.md) - 完整的测试环境搭建指南
- [CODECOV_SETUP_GUIDE](./CODECOV_SETUP_GUIDE.md) - 代码覆盖率工具配置指南
- [MAVEN_WARNINGS_FIX_REPORT](./MAVEN_WARNINGS_FIX_REPORT.md) - Maven构建警告修复报告

### 📊 测试执行报告
- [EXPECTED_TEST_REPORT](./EXPECTED_TEST_REPORT.md) - 预期测试结果报告
- [refactor_test_report](./refactor_test_report.md) - 测试重构报告
- [test_document](./test_document.md) - 测试执行文档

### 🧪 测试案例和样例
- [test-assessment-sample](./test-assessment-sample.md) - 评估功能测试样例
- [test-dev-start-m4-improvements](./test-dev-start-m4-improvements.md) - 开发启动脚本测试改进

## 🛠️ 单元测试规范

### JUnit 5测试框架
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    @DisplayName("用户登录成功测试")
    void testUserLoginSuccess() {
        // Given
        final String username = "testuser";
        final String password = "password123";
        final User mockUser = createMockUser(username);
        
        when(userRepository.findByUsername(username))
            .thenReturn(Optional.of(mockUser));
        
        // When
        final LoginResult result = userService.login(username, password);
        
        // Then
        assertThat(result)
            .isNotNull()
            .hasFieldOrPropertyWithValue("success", true)
            .hasFieldOrPropertyWithValue("username", username);
    }
}
```

### 测试命名规范
- **测试类**: `{ClassName}Test`
- **测试方法**: `test{MethodName}{Scenario}`
- **显示名称**: 使用`@DisplayName`提供中文描述

### 测试数据管理
```java
@TestConfiguration
public class TestDataFactory {
    
    public static User createMockUser(final String username) {
        return User.builder()
            .username(username)
            .email(username + "@test.com")
            .status(UserStatus.ACTIVE)
            .build();
    }
}
```

## 🔗 集成测试规范

### Spring Boot测试
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Transactional
class UserControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testCreateUser() {
        // Given
        final CreateUserRequest request = new CreateUserRequest();
        request.setUsername("newuser");
        request.setEmail("<EMAIL>");
        
        // When
        final ResponseEntity<UserResponse> response = restTemplate
            .postForEntity("/api/users", request, UserResponse.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().getUsername()).isEqualTo("newuser");
    }
}
```

### 数据库测试
```java
@DataJpaTest
class UserRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void testFindByUsername() {
        // Given
        final User user = createTestUser();
        entityManager.persistAndFlush(user);
        
        // When
        final Optional<User> found = userRepository.findByUsername("testuser");
        
        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getUsername()).isEqualTo("testuser");
    }
}
```

## 🎭 前端测试规范

### Vue组件测试
```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import LoginForm from '@/components/LoginForm.vue'

describe('LoginForm', () => {
  it('提交表单时发送正确的数据', async () => {
    const wrapper = mount(LoginForm)
    
    // 输入用户名和密码
    await wrapper.find('input[name="username"]').setValue('testuser')
    await wrapper.find('input[name="password"]').setValue('password123')
    
    // 提交表单
    await wrapper.find('form').trigger('submit')
    
    // 验证发送的数据
    expect(wrapper.emitted('submit')).toBeTruthy()
    expect(wrapper.emitted('submit')[0][0]).toEqual({
      username: 'testuser',
      password: 'password123'
    })
  })
})
```

### E2E测试
```typescript
import { test, expect } from '@playwright/test'

test('用户登录流程', async ({ page }) => {
  // 访问登录页面
  await page.goto('/login')
  
  // 填写登录表单
  await page.fill('input[name="username"]', 'testuser')
  await page.fill('input[name="password"]', 'password123')
  
  // 点击登录按钮
  await page.click('button[type="submit"]')
  
  // 验证登录成功
  await expect(page).toHaveURL('/dashboard')
  await expect(page.locator('.welcome-message')).toContainText('欢迎')
})
```

## 📊 代码覆盖率标准

### 覆盖率目标
- **整体覆盖率**: > 80%
- **核心业务逻辑**: > 90%
- **工具类和公共模块**: > 95%
- **新增代码**: > 85%

### JaCoCo配置
```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.7</version>
    <configuration>
        <rules>
            <rule>
                <element>BUNDLE</element>
                <limits>
                    <limit>
                        <counter>LINE</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>0.80</minimum>
                    </limit>
                </limits>
            </rule>
        </rules>
    </configuration>
</plugin>
```

### Codecov集成
```yaml
# codecov.yml
coverage:
  range: 70...90
  status:
    project:
      default:
        target: 80%
        threshold: 2%
    patch:
      default:
        target: 85%
```

## 🚀 测试自动化

### CI/CD集成
```yaml
name: Test Pipeline
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          
      - name: Run Backend Tests
        run: ./mvnw test
        
      - name: Run Frontend Tests
        run: |
          cd frontend
          npm test
          
      - name: Upload Coverage
        uses: codecov/codecov-action@v3
```

### 测试报告生成
```bash
# 生成测试报告
./mvnw surefire-report:report

# 生成覆盖率报告
./mvnw jacoco:report

# 查看报告
open target/site/surefire-report.html
open target/site/jacoco/index.html
```

## 🔧 测试工具链

### 后端测试工具
- **JUnit 5**: 单元测试框架
- **Mockito**: Mock对象框架
- **TestContainers**: 集成测试容器
- **WireMock**: HTTP服务Mock
- **JaCoCo**: 代码覆盖率工具

### 前端测试工具
- **Vitest**: 快速的单元测试框架
- **Vue Test Utils**: Vue组件测试工具
- **Playwright**: E2E测试框架
- **MSW**: API请求Mock
- **Istanbul**: 代码覆盖率统计

## 📈 性能测试

### JMeter性能测试
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan testname="Assessment Platform Performance Test">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
    </TestPlan>
  </hashTree>
</jmeterTestPlan>
```

### 性能基准
- **响应时间**: API响应 < 500ms
- **并发用户**: 支持1000并发用户
- **吞吐量**: > 1000 TPS
- **错误率**: < 0.1%

## 🔒 安全测试

### OWASP ZAP集成
```bash
# 启动ZAP守护进程
zap.sh -daemon -host 0.0.0.0 -port 8080

# 运行安全扫描
curl "http://localhost:8080/JSON/spider/action/scan/?url=http://localhost:8181"

# 生成安全报告
curl "http://localhost:8080/JSON/core/view/htmlreport/"
```

### 安全测试检查项
- **SQL注入**: 数据库注入攻击防护
- **XSS攻击**: 跨站脚本攻击防护
- **CSRF攻击**: 跨站请求伪造防护
- **认证绕过**: 身份认证安全检查

## 🧪 测试数据管理

### 测试数据策略
```java
@Sql("/test-data/users.sql")
@Sql(value = "/test-data/cleanup.sql", executionPhase = AFTER_TEST_METHOD)
class UserServiceIntegrationTest {
    // 测试方法
}
```

### 数据库测试环境
```yaml
# application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
```

## 🔍 测试调试

### 调试配置
```java
@ExtendWith(MockitoExtension.class)
@EnabledIf("java.lang.System.getProperty('debug.tests') != null")
class DebugTest {
    
    @Test
    void debugTestMethod() {
        // 设置断点进行调试
        final String result = methodUnderTest();
        System.out.println("Debug result: " + result);
    }
}
```

### 日志配置
```xml
<!-- logback-test.xml -->
<configuration>
    <logger name="com.assessment" level="DEBUG"/>
    <logger name="org.springframework.test" level="INFO"/>
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
```

## 📚 最佳实践

### 测试设计原则
1. **独立性**: 测试用例之间相互独立
2. **可重复**: 测试结果可重复验证
3. **快速反馈**: 测试执行速度要快
4. **可维护**: 测试代码易于维护

### 测试命名规范
```java
// 好的测试方法命名
@Test
@DisplayName("当用户名为空时应该抛出IllegalArgumentException")
void shouldThrowIllegalArgumentExceptionWhenUsernameIsEmpty() {
    // 测试实现
}
```

## 📞 相关资源

- [Checkstyle规范](../checkstyle/) - 代码规范检查
- [安全规范](../security/) - 安全测试规范
- [代码审查](../code-review.md) - 代码审查流程
- [CI/CD流程](../../operations/ci-cd/) - 持续集成测试

---

*最后更新：2025-07-01*