# 🔒 安全规范总览

## 🎯 安全体系概述

智慧养老评估平台建立了完整的安全防护体系，从代码安全、数据安全到系统安全，全面保护用户数据和系统稳定性。

## 🏗️ 安全架构体系

### 多层安全防护
- **应用层安全**: 代码安全和业务逻辑安全
- **数据层安全**: 数据加密和访问控制
- **网络层安全**: 传输加密和网络防护
- **基础设施安全**: 服务器和容器安全

### 安全合规要求
- **数据保护**: 个人信息保护法(PIPL)合规
- **行业标准**: 医疗行业数据安全标准
- **国际标准**: ISO 27001信息安全管理
- **等级保护**: 国家信息安全等级保护要求

## 📚 安全文档

### 🔧 安全修复报告
- [SECURITY_FIXES](./SECURITY_FIXES.md) - 安全漏洞修复报告，包含已识别和修复的安全问题
- [security-fixes-needed](./security-fixes-needed.md) - 待修复的安全问题清单

### 📄 版权和合规
- [COPYRIGHT-IMPLEMENTATION](./COPYRIGHT-IMPLEMENTATION.md) - 版权声明实施方案

## 🛡️ 代码安全规范

### 输入验证
```java
@RestController
@Validated
public class UserController {
    
    @PostMapping("/users")
    public ResponseEntity<User> createUser(
            @Valid @RequestBody final CreateUserRequest request) {
        
        // 输入参数验证
        if (!ValidationUtils.isValidEmail(request.getEmail())) {
            throw new InvalidInputException("邮箱格式不正确");
        }
        
        // 防止XSS攻击
        final String sanitizedName = HtmlUtils.htmlEscape(request.getName());
        
        return ResponseEntity.ok(userService.createUser(request));
    }
}
```

### SQL注入防护
```java
@Repository
public class UserRepository {
    
    // ✅ 使用参数化查询
    @Query("SELECT u FROM User u WHERE u.username = :username")
    Optional<User> findByUsername(@Param("username") String username);
    
    // ❌ 避免字符串拼接
    // @Query("SELECT u FROM User u WHERE u.username = '" + username + "'")
}
```

### 敏感数据处理
```java
@Entity
public class User {
    
    // 密码字段不序列化
    @JsonIgnore
    @Column(name = "password_hash")
    private String passwordHash;
    
    // 敏感字段加密存储
    @Convert(converter = EncryptionConverter.class)
    @Column(name = "id_card")
    private String idCard;
}
```

## 🔐 认证与授权安全

### JWT Token安全
```java
@Component
public class JwtTokenProvider {
    
    // 使用强密钥
    @Value("${jwt.secret}")
    private String jwtSecret;
    
    // Token过期时间控制
    @Value("${jwt.expiration:3600}")
    private int jwtExpirationInSeconds;
    
    public String generateToken(final UserDetails userDetails) {
        final Date expiryDate = new Date(System.currentTimeMillis() + 
            jwtExpirationInSeconds * 1000L);
            
        return Jwts.builder()
            .setSubject(userDetails.getUsername())
            .setIssuedAt(new Date())
            .setExpiration(expiryDate)
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
    }
}
```

### 权限控制
```java
@RestController
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {
    
    @GetMapping("/users")
    @PreAuthorize("hasPermission('USER', 'READ')")
    public List<User> getUsers() {
        return userService.getAllUsers();
    }
    
    @PostMapping("/users")
    @PreAuthorize("hasPermission('USER', 'CREATE')")
    public User createUser(@RequestBody final CreateUserRequest request) {
        return userService.createUser(request);
    }
}
```

### 会话安全
```yaml
# application.yml
server:
  servlet:
    session:
      cookie:
        secure: true           # HTTPS only
        http-only: true        # 防止XSS
        same-site: strict      # 防止CSRF
        max-age: 1800          # 30分钟过期
```

## 🗄️ 数据安全规范

### 数据加密
```java
@Component
public class EncryptionService {
    
    private final AESUtil aesUtil;
    
    // 敏感数据加密
    public String encrypt(final String plainText) {
        return aesUtil.encrypt(plainText, getEncryptionKey());
    }
    
    // 敏感数据解密
    public String decrypt(final String encryptedText) {
        return aesUtil.decrypt(encryptedText, getEncryptionKey());
    }
    
    // 密钥管理
    private String getEncryptionKey() {
        return System.getenv("ENCRYPTION_KEY");
    }
}
```

### 数据脱敏
```java
@Component
public class DataMaskingService {
    
    // 手机号脱敏
    public String maskPhone(final String phone) {
        if (phone == null || phone.length() < 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
    
    // 身份证号脱敏
    public String maskIdCard(final String idCard) {
        if (idCard == null || idCard.length() < 18) {
            return idCard;
        }
        return idCard.substring(0, 6) + "********" + idCard.substring(14);
    }
}
```

### 审计日志
```java
@Component
@Slf4j
public class AuditLogger {
    
    @EventListener
    public void handleLoginEvent(final LoginEvent event) {
        log.info("用户登录: username={}, ip={}, timestamp={}", 
            event.getUsername(), 
            event.getIpAddress(), 
            event.getTimestamp());
    }
    
    @EventListener
    public void handleDataAccessEvent(final DataAccessEvent event) {
        log.info("数据访问: user={}, table={}, operation={}, timestamp={}", 
            event.getUsername(), 
            event.getTableName(), 
            event.getOperation(), 
            event.getTimestamp());
    }
}
```

## 🌐 网络安全配置

### HTTPS配置
```yaml
# application.yml
server:
  port: 8443
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
    key-alias: assessment
```

### CORS安全配置
```java
@Configuration
public class CorsConfig {
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        final CorsConfiguration configuration = new CorsConfiguration();
        
        // 限制允许的源
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "https://*.assessment.com",
            "https://localhost:*"
        ));
        
        // 限制允许的方法
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS"
        ));
        
        // 限制允许的头部
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "X-Requested-With"
        ));
        
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        
        final UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
```

### 安全头部配置
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(final HttpSecurity http) throws Exception {
        return http
            .headers(headers -> headers
                .frameOptions().deny()                    // 防止点击劫持
                .contentTypeOptions().and()               // 防止MIME类型嗅探
                .httpStrictTransportSecurity(hstsConfig -> hstsConfig
                    .maxAgeInSeconds(31536000)            // HSTS一年
                    .includeSubdomains(true)
                    .preload(true)
                )
                .and()
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .maximumSessions(1)                       // 限制并发会话
                .maxSessionsPreventsLogin(false)
            )
            .build();
    }
}
```

## 🔍 安全扫描与监控

### 依赖安全扫描
```xml
<!-- Maven dependency check -->
<plugin>
    <groupId>org.owasp</groupId>
    <artifactId>dependency-check-maven</artifactId>
    <version>8.4.0</version>
    <configuration>
        <failBuildOnCVSS>7</failBuildOnCVSS>
        <suppressionFiles>
            <suppressionFile>owasp-suppressions.xml</suppressionFile>
        </suppressionFiles>
    </configuration>
</plugin>
```

### 代码安全扫描
```xml
<!-- SpotBugs安全检查 -->
<plugin>
    <groupId>com.github.spotbugs</groupId>
    <artifactId>spotbugs-maven-plugin</artifactId>
    <version>4.7.3.0</version>
    <configuration>
        <includeFilterFile>spotbugs-security-include.xml</includeFilterFile>
        <plugins>
            <plugin>
                <groupId>com.h3xstream.findsecbugs</groupId>
                <artifactId>findsecbugs-plugin</artifactId>
                <version>1.12.0</version>
            </plugin>
        </plugins>
    </configuration>
</plugin>
```

### 运行时安全监控
```java
@Component
@Slf4j
public class SecurityMonitor {
    
    private final MeterRegistry meterRegistry;
    private final Counter failedLoginCounter;
    private final Counter suspiciousActivityCounter;
    
    @EventListener
    public void handleFailedLogin(final FailedLoginEvent event) {
        failedLoginCounter.increment();
        
        if (isIPSuspicious(event.getIpAddress())) {
            suspiciousActivityCounter.increment();
            log.warn("可疑IP登录失败: ip={}, attempts={}", 
                event.getIpAddress(), 
                getFailedAttempts(event.getIpAddress()));
        }
    }
}
```

## 🏥 医疗数据安全

### HIPAA合规
```java
@Entity
@Table(name = "patient_health_info")
public class HealthInfo {
    
    // PHI数据标记
    @Column(name = "medical_record", columnDefinition = "TEXT")
    @Convert(converter = PHIEncryptionConverter.class)
    private String medicalRecord;
    
    // 访问日志
    @CreationTimestamp
    private LocalDateTime accessedAt;
    
    @Column(name = "accessed_by")
    private String accessedBy;
}
```

### 数据最小化原则
```java
@Service
public class HealthDataService {
    
    // 只返回必要的字段
    @PreAuthorize("hasPermission(#patientId, 'HEALTH_DATA', 'READ')")
    public HealthSummary getHealthSummary(final Long patientId, 
                                         final String requesterRole) {
        final HealthInfo fullData = healthRepository.findByPatientId(patientId);
        
        // 根据角色返回不同级别的数据
        return switch (requesterRole) {
            case "DOCTOR" -> mapToFullSummary(fullData);
            case "NURSE" -> mapToBasicSummary(fullData);
            case "ADMIN" -> mapToAdminSummary(fullData);
            default -> throw new AccessDeniedException("无权限访问健康数据");
        };
    }
}
```

## 📊 安全指标监控

### 安全KPI
- **登录成功率**: > 98%
- **异常登录检测**: < 0.1%
- **数据泄露事件**: 0次
- **安全扫描通过率**: 100%

### 安全事件响应
```java
@Component
public class SecurityIncidentHandler {
    
    @Async
    @EventListener
    public void handleSecurityIncident(final SecurityIncidentEvent event) {
        // 记录安全事件
        securityLogService.logIncident(event);
        
        // 发送告警通知
        if (event.getSeverity() == Severity.HIGH) {
            alertService.sendSecurityAlert(event);
        }
        
        // 自动响应措施
        if (event.getType() == IncidentType.BRUTE_FORCE_ATTACK) {
            ipBlockingService.blockIP(event.getSourceIP());
        }
    }
}
```

## 🔧 安全开发工具

### IDE安全插件
- **SonarLint**: 实时代码安全检查
- **SpotBugs**: 静态代码安全分析
- **OWASP ZAP**: 动态应用安全测试

### CI/CD安全集成
```yaml
# .github/workflows/security.yml
name: Security Scan
on: [push, pull_request]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: OWASP Dependency Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'assessment-platform'
          path: '.'
          format: 'ALL'
          
      - name: Upload Results
        uses: actions/upload-artifact@v3
        with:
          name: dependency-check-report
          path: reports/
```

## 📚 安全培训与意识

### 开发团队安全培训
1. **OWASP Top 10**: 常见Web安全漏洞
2. **安全编码**: 安全编码最佳实践
3. **数据保护**: 个人信息保护法律法规
4. **事件响应**: 安全事件应急响应流程

### 安全编码检查清单
- [ ] 输入验证和输出编码
- [ ] 身份认证和会话管理
- [ ] 访问控制实施
- [ ] 加密数据保护
- [ ] 错误处理和日志记录
- [ ] 数据验证和完整性
- [ ] 安全配置管理

## 📞 相关资源

- [Checkstyle规范](../checkstyle/) - 代码规范包含安全要求
- [测试标准](../testing/) - 安全测试规范
- [认证系统](../../features/authentication/) - 认证安全实现
- [部署安全](../../operations/) - 部署环境安全配置

---

*最后更新：2025-07-01*