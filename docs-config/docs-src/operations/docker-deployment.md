# 🐳 Docker部署指南

## 🎯 部署概述

智慧养老评估平台采用Docker容器化部署方案，支持开发、测试、生产环境的统一部署流程。本文档详细介绍如何使用Docker进行平台部署。

## 🏗️ 部署架构

### 容器架构图

```
┌─────────────────────────────────────────────────────────┐
│                     Nginx (反向代理)                      │
├─────────────────────────────────────────────────────────┤
│  Frontend (Vue.js)  │  Backend (Spring Boot)  │  Docs   │
│  - Admin Portal     │  - REST API              │  Portal │
│  - User Website     │  - WebSocket             │         │
│  - Mobile H5        │  - 文件上传               │         │
├─────────────────────────────────────────────────────────┤
│  PostgreSQL 15     │  Redis 7.x     │  LM Studio      │
│  - 主数据库         │  - 缓存服务     │  - AI推理服务    │
│  - 数据持久化       │  - 会话存储     │                │
└─────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
Assessment/
├── docker-compose.yml           # 开发环境编排
├── docker-compose.prod.yml      # 生产环境编排
├── docker-compose.test.yml      # 测试环境编排
├── Dockerfile.backend           # 后端镜像构建
├── Dockerfile.frontend          # 前端镜像构建
├── nginx/
│   ├── nginx.conf              # Nginx配置
│   └── ssl/                    # SSL证书
├── scripts/
│   ├── deploy.sh               # 部署脚本
│   ├── backup.sh               # 备份脚本
│   └── restore.sh              # 恢复脚本
└── .env                        # 环境变量配置
```

## 🚀 快速部署

### 1. 环境准备

**系统要求:**
- Docker Engine 20.10+
- Docker Compose 2.0+
- 最小内存: 4GB
- 最小磁盘: 20GB

**安装Docker:**
```bash
# CentOS/RHEL
sudo yum install -y docker docker-compose
sudo systemctl start docker
sudo systemctl enable docker

# Ubuntu/Debian
sudo apt update
sudo apt install -y docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker

# 验证安装
docker --version
docker-compose --version
```

### 2. 环境配置

创建环境变量文件:
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

**环境变量示例:**
```bash
# =================================
# 应用配置
# =================================
APP_ENV=production
APP_NAME=智慧养老评估平台
APP_VERSION=1.0.0

# =================================
# 数据库配置
# =================================
POSTGRES_DB=assessment_db
POSTGRES_USER=assessment_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# =================================
# Redis配置
# =================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# =================================
# 后端服务配置
# =================================
BACKEND_PORT=8080
JWT_SECRET=your_jwt_secret_key_here
FILE_UPLOAD_PATH=/app/uploads

# =================================
# 前端服务配置
# =================================
FRONTEND_PORT=3000
API_BASE_URL=http://localhost:8080/api

# =================================
# LM Studio配置
# =================================
LM_STUDIO_HOST=lm-studio
LM_STUDIO_PORT=1234
LM_STUDIO_MODEL=llama-3.1-8b-instruct

# =================================
# Nginx配置
# =================================
NGINX_PORT=80
NGINX_SSL_PORT=443

# =================================
# 文档门户配置
# =================================
DOCS_PORT=3005

# =================================
# 监控配置
# =================================
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
```

### 3. 一键部署

**开发环境部署:**
```bash
# 启动开发环境
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

**生产环境部署:**
```bash
# 构建并启动生产环境
docker-compose -f docker-compose.prod.yml up -d --build

# 或使用部署脚本
./scripts/deploy.sh production
```

## 📋 Docker Compose配置

### 开发环境配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: assessment-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/db/migration:/docker-entrypoint-initdb.d
    networks:
      - assessment-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: assessment-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - assessment-network
    restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: assessment-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *******************************/${POSTGRES_DB}
      SPRING_DATASOURCE_USERNAME: ${POSTGRES_USER}
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      LM_STUDIO_BASE_URL: http://lm-studio:${LM_STUDIO_PORT}
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - assessment-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 - 管理后台
  frontend-admin:
    build:
      context: ./frontend
      dockerfile: Dockerfile.admin
    container_name: assessment-frontend-admin
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api
      VITE_APP_TITLE: 智慧养老评估平台 - 管理后台
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - assessment-network
    restart: unless-stopped

  # 前端服务 - 用户网站
  frontend-website:
    build:
      context: ./frontend
      dockerfile: Dockerfile.website
    container_name: assessment-frontend-website
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api
      VITE_APP_TITLE: 智慧养老评估平台
    ports:
      - "3001:3000"
    depends_on:
      - backend
    networks:
      - assessment-network
    restart: unless-stopped

  # 文档门户
  docs:
    build:
      context: ./docs-config
      dockerfile: Dockerfile
    container_name: assessment-docs
    ports:
      - "3005:3005"
    volumes:
      - ./docs:/app/docs:ro
    networks:
      - assessment-network
    restart: unless-stopped

  # LM Studio AI服务
  lm-studio:
    image: lmstudio/server:latest
    container_name: assessment-lm-studio
    environment:
      LM_STUDIO_MODEL: ${LM_STUDIO_MODEL}
      LM_STUDIO_PORT: ${LM_STUDIO_PORT}
    ports:
      - "1234:1234"
    volumes:
      - lm_studio_models:/app/models
    networks:
      - assessment-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: assessment-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend-admin
      - frontend-website
      - docs
    networks:
      - assessment-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  backend_uploads:
  backend_logs:
  lm_studio_models:
  nginx_logs:

networks:
  assessment-network:
    driver: bridge
```

### 生产环境配置

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: assessment-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - /data/postgres:/var/lib/postgresql/data
      - ./backup:/backup
    networks:
      - assessment-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  redis:
    image: redis:7-alpine
    container_name: assessment-redis-prod
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - /data/redis:/data
    networks:
      - assessment-prod-network
    restart: unless-stopped

  backend:
    image: assessment/backend:${APP_VERSION}
    container_name: assessment-backend-prod
    environment:
      SPRING_PROFILES_ACTIVE: production
      SPRING_DATASOURCE_URL: *******************************/${POSTGRES_DB}
      SPRING_DATASOURCE_USERNAME: ${POSTGRES_USER}
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      JAVA_OPTS: "-Xms1g -Xmx2g -XX:+UseG1GC"
    volumes:
      - /data/uploads:/app/uploads
      - /data/logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - assessment-prod-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  nginx:
    image: nginx:alpine
    container_name: assessment-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - /var/log/nginx:/var/log/nginx
    depends_on:
      - backend
    networks:
      - assessment-prod-network
    restart: unless-stopped

networks:
  assessment-prod-network:
    driver: bridge
```

## 🐳 Dockerfile配置

### 后端Dockerfile

```dockerfile
# backend/Dockerfile
FROM openjdk:21-jdk-slim as builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# Maven构建
RUN apt-get update && apt-get install -y maven
RUN mvn clean package -DskipTests

FROM openjdk:21-jre-slim

# 安装必要工具
RUN apt-get update && \
    apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制JAR文件
COPY --from=builder /app/target/*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs /app/uploads

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 前端Dockerfile

```dockerfile
# frontend/Dockerfile.admin
FROM node:18-alpine as builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY turbo.json ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 构建管理后台
RUN npm run build:admin

FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/apps/admin/dist /usr/share/nginx/html

# 复制Nginx配置
COPY nginx/frontend.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

## ⚙️ Nginx配置

### 开发环境Nginx配置

```nginx
# nginx/nginx.conf
user nginx;
worker_processes auto;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 负载均衡配置
    upstream backend {
        server backend:8080;
    }

    upstream frontend-admin {
        server frontend-admin:3000;
    }

    upstream frontend-website {
        server frontend-website:3000;
    }

    upstream docs {
        server docs:3005;
    }

    # 主配置
    server {
        listen 80;
        server_name localhost;

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # API代理
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 管理后台
        location /admin/ {
            proxy_pass http://frontend-admin/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 用户网站
        location / {
            proxy_pass http://frontend-website/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 文档门户
        location /docs/ {
            proxy_pass http://docs/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}
```

## 🚀 部署脚本

### 自动化部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# 配置变量
ENVIRONMENT=${1:-development}
PROJECT_NAME="assessment"
BACKUP_DIR="/backup"
LOG_FILE="/var/log/deploy.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

log_error() {
    echo "[ERROR] [$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE >&2
}

# 环境检查
check_environment() {
    log "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    # 检查环境文件
    if [ ! -f .env ]; then
        log_error "环境文件.env不存在"
        exit 1
    fi
    
    log "环境检查通过"
}

# 备份函数
backup_data() {
    log "开始数据备份..."
    
    BACKUP_DATE=$(date '+%Y%m%d_%H%M%S')
    BACKUP_PATH="$BACKUP_DIR/${PROJECT_NAME}_backup_$BACKUP_DATE"
    
    mkdir -p $BACKUP_PATH
    
    # 备份数据库
    docker-compose exec -T postgres pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > $BACKUP_PATH/database.sql
    
    # 备份Redis
    docker-compose exec -T redis redis-cli --rdb /data/dump.rdb
    docker cp assessment-redis:/data/dump.rdb $BACKUP_PATH/redis_dump.rdb
    
    # 备份上传文件
    if [ -d "/data/uploads" ]; then
        tar -czf $BACKUP_PATH/uploads.tar.gz -C /data uploads
    fi
    
    log "备份完成: $BACKUP_PATH"
    echo $BACKUP_PATH > .last_backup
}

# 部署函数
deploy() {
    log "开始部署环境: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        "development")
            COMPOSE_FILE="docker-compose.yml"
            ;;
        "production")
            COMPOSE_FILE="docker-compose.prod.yml"
            backup_data
            ;;
        "test")
            COMPOSE_FILE="docker-compose.test.yml"
            ;;
        *)
            log_error "未知环境: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    # 拉取最新镜像
    log "拉取最新镜像..."
    docker-compose -f $COMPOSE_FILE pull
    
    # 构建服务
    log "构建服务..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    # 停止旧服务
    log "停止旧服务..."
    docker-compose -f $COMPOSE_FILE down
    
    # 启动新服务
    log "启动新服务..."
    docker-compose -f $COMPOSE_FILE up -d
    
    # 等待服务启动
    log "等待服务启动..."
    sleep 30
    
    # 健康检查
    health_check
    
    log "部署完成!"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    # 检查后端服务
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log "后端服务健康检查通过"
            break
        fi
        
        log "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "后端服务健康检查失败"
        exit 1
    fi
    
    # 检查前端服务
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log "前端服务健康检查通过"
    else
        log_error "前端服务健康检查失败"
    fi
    
    # 检查数据库连接
    if docker-compose exec -T postgres pg_isready -U ${POSTGRES_USER} > /dev/null 2>&1; then
        log "数据库连接检查通过"
    else
        log_error "数据库连接检查失败"
    fi
}

# 回滚函数
rollback() {
    log "开始回滚..."
    
    if [ ! -f .last_backup ]; then
        log_error "没有找到备份记录"
        exit 1
    fi
    
    LAST_BACKUP=$(cat .last_backup)
    
    if [ ! -d "$LAST_BACKUP" ]; then
        log_error "备份目录不存在: $LAST_BACKUP"
        exit 1
    fi
    
    # 停止当前服务
    docker-compose down
    
    # 恢复数据库
    if [ -f "$LAST_BACKUP/database.sql" ]; then
        log "恢复数据库..."
        docker-compose up -d postgres
        sleep 10
        docker-compose exec -T postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} < $LAST_BACKUP/database.sql
    fi
    
    # 恢复Redis
    if [ -f "$LAST_BACKUP/redis_dump.rdb" ]; then
        log "恢复Redis..."
        docker cp $LAST_BACKUP/redis_dump.rdb assessment-redis:/data/dump.rdb
        docker-compose restart redis
    fi
    
    # 恢复上传文件
    if [ -f "$LAST_BACKUP/uploads.tar.gz" ]; then
        log "恢复上传文件..."
        tar -xzf $LAST_BACKUP/uploads.tar.gz -C /data
    fi
    
    log "回滚完成"
}

# 主函数
main() {
    case ${2:-deploy} in
        "deploy")
            check_environment
            deploy
            ;;
        "rollback")
            rollback
            ;;
        "health")
            health_check
            ;;
        *)
            echo "用法: $0 <environment> [deploy|rollback|health]"
            echo "环境: development|production|test"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
```

## 📊 监控和日志

### 日志收集配置

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  # ELK Stack for logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    container_name: assessment-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - assessment-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    container_name: assessment-logstash
    volumes:
      - ./logstash/config:/usr/share/logstash/pipeline
      - backend_logs:/var/log/backend:ro
      - nginx_logs:/var/log/nginx:ro
    depends_on:
      - elasticsearch
    networks:
      - assessment-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    container_name: assessment-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - assessment-network

volumes:
  elasticsearch_data:
```

### Prometheus监控配置

```yaml
# prometheus/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'assessment-backend'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/actuator/prometheus'

  - job_name: 'assessment-postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'assessment-redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'assessment-nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
```

## 🔧 运维操作

### 常用Docker命令

```bash
# 查看所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
docker-compose logs -f --tail=100 postgres

# 进入容器
docker-compose exec backend bash
docker-compose exec postgres psql -U assessment_user -d assessment_db

# 重启服务
docker-compose restart backend
docker-compose restart nginx

# 更新单个服务
docker-compose up -d --no-deps backend

# 扩容服务
docker-compose up -d --scale backend=3

# 清理未使用的镜像和容器
docker system prune -f
docker image prune -f
```

### 数据库维护

```bash
# 数据库备份
docker-compose exec postgres pg_dump -U assessment_user assessment_db > backup_$(date +%Y%m%d).sql

# 数据库恢复
docker-compose exec -T postgres psql -U assessment_user -d assessment_db < backup.sql

# 查看数据库连接数
docker-compose exec postgres psql -U assessment_user -d assessment_db -c "SELECT count(*) FROM pg_stat_activity;"
```

## 🚨 故障排查

### 常见问题及解决方案

**1. 后端服务启动失败**
```bash
# 检查日志
docker-compose logs backend

# 常见原因及解决方案
# - 数据库连接失败: 检查数据库是否启动，连接参数是否正确
# - 端口冲突: 修改docker-compose.yml中的端口映射
# - 内存不足: 增加Docker内存限制或优化JVM参数
```

**2. 数据库连接问题**
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U assessment_user

# 检查数据库日志
docker-compose logs postgres

# 重启数据库
docker-compose restart postgres
```

**3. 前端访问404**
```bash
# 检查Nginx配置
docker-compose exec nginx nginx -t

# 重新加载Nginx配置
docker-compose exec nginx nginx -s reload

# 检查前端构建
docker-compose logs frontend-admin
```

**4. Redis连接问题**
```bash
# 测试Redis连接
docker-compose exec redis redis-cli ping

# 检查Redis内存使用
docker-compose exec redis redis-cli info memory
```

## 📞 相关资源

- [运维总览](./index.md) - 运维部署概述
- [监控配置](./monitoring.md) - 系统监控设置
- [故障排查](./troubleshooting.md) - 故障诊断指南
- [CI/CD流程](./ci-cd/) - 持续集成部署

---

*最后更新：2025-07-01*