# 🧪 自动化测试指南

## 🎯 自动化测试概述

智慧养老评估平台采用全面的自动化测试体系，包括单元测试、集成测试、端到端测试、性能测试和安全测试，确保代码质量和系统稳定性。

## 🏗️ 测试架构

### 测试金字塔架构

```
                    🔺 E2E Tests (端到端测试)
                   /   UI Tests, API Tests
                  /    User Journey Tests
                 /     Performance Tests
                /      Security Tests
               /
              🔺 Integration Tests (集成测试)
             /   Component Integration
            /    Database Integration  
           /     External Service Tests
          /      Contract Tests
         /
        🔺 Unit Tests (单元测试)
       /   Service Layer Tests
      /    Repository Tests
     /     Utility Function Tests
    /      Component Tests
   /       Pure Function Tests
  /
 🔺 Static Analysis (静态分析)
    Code Quality, Security Scan
    Dependency Check, Lint
```

### 测试覆盖率目标

- **单元测试覆盖率**: ≥80%
- **集成测试覆盖率**: ≥70%
- **E2E测试覆盖率**: ≥60%
- **关键路径覆盖率**: 100%

## 🔧 后端测试框架

### 测试技术栈

- **JUnit 5**: 单元测试框架
- **Mockito**: Mock框架
- **TestContainers**: 集成测试容器化
- **WireMock**: HTTP服务模拟
- **RestAssured**: API测试
- **JMeter**: 性能测试

### 单元测试配置

```java
// backend/src/test/java/com/assessment/BaseUnitTest.java
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public abstract class BaseUnitTest {
    
    @BeforeEach
    void setUp() {
        // 通用测试设置
    }
    
    @AfterEach
    void tearDown() {
        // 清理测试数据
    }
}

// Service层测试示例
@ExtendWith(MockitoExtension.class)
class UserServiceTest extends BaseUnitTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private EmailService emailService;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    @DisplayName("创建用户 - 成功场景")
    void createUser_Success() {
        // Given
        final CreateUserRequest request = CreateUserRequest.builder()
            .username("testuser")
            .email("<EMAIL>")
            .password("Password123!")
            .build();
            
        final String encodedPassword = "encoded_password";
        final User savedUser = User.builder()
            .id(1L)
            .username(request.getUsername())
            .email(request.getEmail())
            .password(encodedPassword)
            .build();
            
        when(userRepository.existsByUsername(request.getUsername())).thenReturn(false);
        when(userRepository.existsByEmail(request.getEmail())).thenReturn(false);
        when(passwordEncoder.encode(request.getPassword())).thenReturn(encodedPassword);
        when(userRepository.save(any(User.class))).thenReturn(savedUser);
        
        // When
        final User result = userService.createUser(request);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getUsername()).isEqualTo(request.getUsername());
        assertThat(result.getEmail()).isEqualTo(request.getEmail());
        
        verify(userRepository).existsByUsername(request.getUsername());
        verify(userRepository).existsByEmail(request.getEmail());
        verify(passwordEncoder).encode(request.getPassword());
        verify(userRepository).save(any(User.class));
        verify(emailService).sendWelcomeEmail(result);
    }
    
    @Test
    @DisplayName("创建用户 - 用户名已存在")
    void createUser_UsernameExists_ThrowsException() {
        // Given
        final CreateUserRequest request = CreateUserRequest.builder()
            .username("existinguser")
            .email("<EMAIL>")
            .password("Password123!")
            .build();
            
        when(userRepository.existsByUsername(request.getUsername())).thenReturn(true);
        
        // When & Then
        assertThatThrownBy(() -> userService.createUser(request))
            .isInstanceOf(DuplicateUsernameException.class)
            .hasMessage("用户名已存在: existinguser");
            
        verify(userRepository).existsByUsername(request.getUsername());
        verify(userRepository, never()).save(any(User.class));
        verify(emailService, never()).sendWelcomeEmail(any(User.class));
    }
    
    @Test
    @DisplayName("获取用户分页列表")
    void getUserPage_Success() {
        // Given
        final PageRequest pageRequest = PageRequest.of(0, 10);
        final List<User> users = Arrays.asList(
            User.builder().id(1L).username("user1").build(),
            User.builder().id(2L).username("user2").build()
        );
        final Page<User> userPage = new PageImpl<>(users, pageRequest, 2);
        
        when(userRepository.findAll(pageRequest)).thenReturn(userPage);
        
        // When
        final Page<UserDTO> result = userService.getUserPage(pageRequest);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getContent().get(0).getUsername()).isEqualTo("user1");
        
        verify(userRepository).findAll(pageRequest);
    }
}
```

### 集成测试配置

```java
// backend/src/test/java/com/assessment/BaseIntegrationTest.java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
@ActiveProfiles("integration")
@Transactional
@Rollback
public abstract class BaseIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
        .withDatabaseName("assessment_test")
        .withUsername("test")
        .withPassword("test");
        
    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
        .withExposedPorts(6379);
    
    @DynamicPropertySource
    static void configureProperties(final DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.redis.host", redis::getHost);
        registry.add("spring.redis.port", redis::getFirstMappedPort);
    }
    
    @Autowired
    protected TestRestTemplate restTemplate;
    
    @Autowired
    protected TestEntityManager entityManager;
    
    @LocalServerPort
    protected int port;
    
    protected String getBaseUrl() {
        return "http://localhost:" + port;
    }
}

// Repository层集成测试
@DataJpaTest
@Testcontainers
class UserRepositoryIntegrationTest extends BaseIntegrationTest {
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    @DisplayName("根据用户名查找用户")
    void findByUsername_Success() {
        // Given
        final User user = User.builder()
            .username("testuser")
            .email("<EMAIL>")
            .password("password")
            .organizationId(1L)
            .build();
        entityManager.persistAndFlush(user);
        
        // When
        final Optional<User> result = userRepository.findByUsername("testuser");
        
        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getUsername()).isEqualTo("testuser");
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
    }
    
    @Test
    @DisplayName("查找组织内活跃用户")
    void findActiveUsersByOrganizationId_Success() {
        // Given
        final Long organizationId = 1L;
        final User activeUser = User.builder()
            .username("activeuser")
            .email("<EMAIL>")
            .password("password")
            .organizationId(organizationId)
            .isActive(true)
            .build();
            
        final User inactiveUser = User.builder()
            .username("inactiveuser")
            .email("<EMAIL>")
            .password("password")
            .organizationId(organizationId)
            .isActive(false)
            .build();
            
        entityManager.persistAndFlush(activeUser);
        entityManager.persistAndFlush(inactiveUser);
        
        // When
        final List<User> result = userRepository.findActiveUsersByOrganizationId(organizationId);
        
        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getUsername()).isEqualTo("activeuser");
        assertThat(result.get(0).getIsActive()).isTrue();
    }
}

// API集成测试
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class UserControllerIntegrationTest extends BaseIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    private String authToken;
    
    @BeforeEach
    void setUp() {
        // 创建测试用户并获取认证token
        authToken = createTestUserAndGetToken();
    }
    
    @Test
    @DisplayName("创建用户API - 成功")
    void createUser_Success() {
        // Given
        final CreateUserRequest request = CreateUserRequest.builder()
            .username("newuser")
            .email("<EMAIL>")
            .password("Password123!")
            .organizationId(1L)
            .build();
            
        final HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(authToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        final HttpEntity<CreateUserRequest> entity = new HttpEntity<>(request, headers);
        
        // When
        final ResponseEntity<UserDTO> response = restTemplate.exchange(
            getBaseUrl() + "/api/users",
            HttpMethod.POST,
            entity,
            UserDTO.class
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getUsername()).isEqualTo("newuser");
        assertThat(response.getBody().getEmail()).isEqualTo("<EMAIL>");
    }
    
    @Test
    @DisplayName("获取用户列表API - 分页")
    void getUserList_WithPagination_Success() {
        // Given
        createTestUsers(15); // 创建15个测试用户
        
        final HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(authToken);
        
        final HttpEntity<Void> entity = new HttpEntity<>(headers);
        
        // When
        final ResponseEntity<PagedResponse<UserDTO>> response = restTemplate.exchange(
            getBaseUrl() + "/api/users?page=0&size=10",
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<PagedResponse<UserDTO>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getContent()).hasSize(10);
        assertThat(response.getBody().getTotalElements()).isGreaterThanOrEqualTo(15);
    }
    
    private String createTestUserAndGetToken() {
        // 实现创建测试用户并返回JWT token的逻辑
        return "test.jwt.token";
    }
    
    private void createTestUsers(final int count) {
        for (int i = 0; i < count; i++) {
            final CreateUserRequest request = CreateUserRequest.builder()
                .username("testuser" + i)
                .email("testuser" + i + "@example.com")
                .password("Password123!")
                .organizationId(1L)
                .build();
            userService.createUser(request);
        }
    }
}
```

### 外部服务Mock测试

```java
// 使用WireMock模拟外部服务
@SpringBootTest
class ExternalServiceIntegrationTest {
    
    @RegisterExtension
    static WireMockExtension wireMock = WireMockExtension.newInstance()
        .options(wireMockConfig().port(8089))
        .build();
    
    @Autowired
    private LMStudioService lmStudioService;
    
    @Test
    @DisplayName("AI评估服务调用 - 成功")
    void callAIAssessment_Success() {
        // Given
        final String assessmentData = "评估数据";
        final String expectedResponse = "{\"result\":\"评估结果\",\"confidence\":0.95}";
        
        wireMock.stubFor(post(urlEqualTo("/api/v1/assessment"))
            .withHeader("Content-Type", equalTo("application/json"))
            .withRequestBody(containing(assessmentData))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("Content-Type", "application/json")
                .withBody(expectedResponse)));
        
        // When
        final AIAssessmentResult result = lmStudioService.performAssessment(assessmentData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getResult()).isEqualTo("评估结果");
        assertThat(result.getConfidence()).isEqualTo(0.95);
        
        wireMock.verify(postRequestedFor(urlEqualTo("/api/v1/assessment")));
    }
    
    @Test
    @DisplayName("AI评估服务调用 - 超时重试")
    void callAIAssessment_TimeoutRetry() {
        // Given
        final String assessmentData = "评估数据";
        
        // 第一次调用超时，第二次成功
        wireMock.stubFor(post(urlEqualTo("/api/v1/assessment"))
            .inScenario("Timeout Scenario")
            .whenScenarioStateIs(STARTED)
            .willReturn(aResponse()
                .withFixedDelay(5000)
                .withStatus(500))
            .willSetStateTo("Second Call"));
            
        wireMock.stubFor(post(urlEqualTo("/api/v1/assessment"))
            .inScenario("Timeout Scenario")
            .whenScenarioStateIs("Second Call")
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("Content-Type", "application/json")
                .withBody("{\"result\":\"重试成功\",\"confidence\":0.90}")));
        
        // When
        final AIAssessmentResult result = lmStudioService.performAssessment(assessmentData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getResult()).isEqualTo("重试成功");
        
        wireMock.verify(2, postRequestedFor(urlEqualTo("/api/v1/assessment")));
    }
}
```

## 🎨 前端测试框架

### 前端测试技术栈

- **Vitest**: 单元测试框架
- **Vue Test Utils**: Vue组件测试
- **Playwright**: E2E测试
- **MSW**: API Mock
- **Testing Library**: 用户行为测试

### Vue组件单元测试

```typescript
// frontend/src/components/__tests__/UserForm.test.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import UserForm from '../UserForm.vue'
import { useUserStore } from '@/stores/modules/user'

describe('UserForm', () => {
  const createWrapper = (props = {}) => {
    return mount(UserForm, {
      props: {
        mode: 'create',
        ...props
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })
  }

  it('渲染表单字段', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.find('[data-testid="username-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="email-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="password-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="submit-button"]').exists()).toBe(true)
  })

  it('表单验证 - 必填字段', async () => {
    const wrapper = createWrapper()
    
    // 提交空表单
    await wrapper.find('[data-testid="submit-button"]').trigger('click')
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('[data-testid="username-error"]').text()).toContain('用户名不能为空')
    expect(wrapper.find('[data-testid="email-error"]').text()).toContain('邮箱不能为空')
    expect(wrapper.find('[data-testid="password-error"]').text()).toContain('密码不能为空')
  })

  it('表单验证 - 邮箱格式', async () => {
    const wrapper = createWrapper()
    
    await wrapper.find('[data-testid="email-input"]').setValue('invalid-email')
    await wrapper.find('[data-testid="email-input"]').trigger('blur')
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('[data-testid="email-error"]').text()).toContain('邮箱格式不正确')
  })

  it('表单提交 - 成功', async () => {
    const wrapper = createWrapper()
    const userStore = useUserStore()
    
    // 填写表单
    await wrapper.find('[data-testid="username-input"]').setValue('testuser')
    await wrapper.find('[data-testid="email-input"]').setValue('<EMAIL>')
    await wrapper.find('[data-testid="password-input"]').setValue('Password123!')
    
    // 提交表单
    await wrapper.find('[data-testid="submit-button"]').trigger('click')
    await wrapper.vm.$nextTick()
    
    expect(userStore.createUser).toHaveBeenCalledWith({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!'
    })
  })

  it('编辑模式 - 加载用户数据', async () => {
    const userData = {
      id: 1,
      username: 'existinguser',
      email: '<EMAIL>'
    }
    
    const wrapper = createWrapper({
      mode: 'edit',
      userData
    })
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('[data-testid="username-input"]').element.value).toBe('existinguser')
    expect(wrapper.find('[data-testid="email-input"]').element.value).toBe('<EMAIL>')
    expect(wrapper.find('[data-testid="password-input"]').exists()).toBe(false) // 编辑模式不显示密码字段
  })
})
```

### API服务测试

```typescript
// frontend/src/services/__tests__/userService.test.ts
import { describe, it, expect, beforeAll, afterEach, afterAll } from 'vitest'
import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { userService } from '../userService'

const server = setupServer(
  rest.get('/api/users', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || '0'
    const size = req.url.searchParams.get('size') || '10'
    
    return res(
      ctx.status(200),
      ctx.json({
        content: [
          { id: 1, username: 'user1', email: '<EMAIL>' },
          { id: 2, username: 'user2', email: '<EMAIL>' }
        ],
        totalElements: 2,
        totalPages: 1,
        number: parseInt(page),
        size: parseInt(size)
      })
    )
  }),

  rest.post('/api/users', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: 3,
        ...req.body
      })
    )
  }),

  rest.get('/api/users/:id', (req, res, ctx) => {
    const { id } = req.params
    return res(
      ctx.status(200),
      ctx.json({
        id: parseInt(id),
        username: `user${id}`,
        email: `user${id}@example.com`
      })
    )
  })
)

beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

describe('UserService', () => {
  it('获取用户列表', async () => {
    const result = await userService.getUsers({ page: 0, size: 10 })
    
    expect(result.content).toHaveLength(2)
    expect(result.content[0].username).toBe('user1')
    expect(result.totalElements).toBe(2)
  })

  it('创建用户', async () => {
    const userData = {
      username: 'newuser',
      email: '<EMAIL>',
      password: 'Password123!'
    }
    
    const result = await userService.createUser(userData)
    
    expect(result.id).toBe(3)
    expect(result.username).toBe('newuser')
    expect(result.email).toBe('<EMAIL>')
  })

  it('获取单个用户', async () => {
    const result = await userService.getUser(1)
    
    expect(result.id).toBe(1)
    expect(result.username).toBe('user1')
    expect(result.email).toBe('<EMAIL>')
  })

  it('处理API错误', async () => {
    server.use(
      rest.get('/api/users/999', (req, res, ctx) => {
        return res(
          ctx.status(404),
          ctx.json({ message: '用户不存在' })
        )
      })
    )
    
    await expect(userService.getUser(999)).rejects.toThrow('用户不存在')
  })
})
```

### Pinia Store测试

```typescript
// frontend/src/stores/__tests__/userStore.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useUserStore } from '../modules/user'
import { userService } from '@/services/userService'

// Mock userService
vi.mock('@/services/userService', () => ({
  userService: {
    getUsers: vi.fn(),
    createUser: vi.fn(),
    updateUser: vi.fn(),
    deleteUser: vi.fn()
  }
}))

describe('UserStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('初始状态正确', () => {
    const store = useUserStore()
    
    expect(store.users).toEqual([])
    expect(store.currentUser).toBeNull()
    expect(store.loading).toBe(false)
    expect(store.totalCount).toBe(0)
  })

  it('fetchUsers - 成功', async () => {
    const mockResponse = {
      content: [
        { id: 1, username: 'user1', email: '<EMAIL>' },
        { id: 2, username: 'user2', email: '<EMAIL>' }
      ],
      totalElements: 2
    }
    
    vi.mocked(userService.getUsers).mockResolvedValue(mockResponse)
    
    const store = useUserStore()
    await store.fetchUsers({ page: 0, size: 10 })
    
    expect(store.users).toEqual(mockResponse.content)
    expect(store.totalCount).toBe(2)
    expect(store.loading).toBe(false)
    expect(userService.getUsers).toHaveBeenCalledWith({ page: 0, size: 10 })
  })

  it('createUser - 成功', async () => {
    const newUser = { id: 3, username: 'newuser', email: '<EMAIL>' }
    vi.mocked(userService.createUser).mockResolvedValue(newUser)
    
    const store = useUserStore()
    const userData = { username: 'newuser', email: '<EMAIL>', password: 'pass' }
    
    await store.createUser(userData)
    
    expect(store.users).toContain(newUser)
    expect(userService.createUser).toHaveBeenCalledWith(userData)
  })

  it('deleteUser - 成功', async () => {
    const store = useUserStore()
    store.users = [
      { id: 1, username: 'user1', email: '<EMAIL>' },
      { id: 2, username: 'user2', email: '<EMAIL>' }
    ]
    
    vi.mocked(userService.deleteUser).mockResolvedValue(undefined)
    
    await store.deleteUser(1)
    
    expect(store.users).toHaveLength(1)
    expect(store.users[0].id).toBe(2)
    expect(userService.deleteUser).toHaveBeenCalledWith(1)
  })

  it('错误处理', async () => {
    const error = new Error('网络错误')
    vi.mocked(userService.getUsers).mockRejectedValue(error)
    
    const store = useUserStore()
    
    await expect(store.fetchUsers()).rejects.toThrow('网络错误')
    expect(store.loading).toBe(false)
  })
})
```

## 🎭 端到端测试

### Playwright E2E测试配置

```typescript
// frontend/tests/e2e/playwright.config.ts
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    },
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] }
    }
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI
  }
})
```

### 用户流程E2E测试

```typescript
// frontend/tests/e2e/user-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('用户管理', () => {
  test.beforeEach(async ({ page }) => {
    // 登录管理员账户
    await page.goto('/login')
    await page.fill('[data-testid="username"]', 'admin')
    await page.fill('[data-testid="password"]', 'admin123')
    await page.click('[data-testid="login-button"]')
    
    // 等待跳转到管理后台
    await expect(page).toHaveURL('/admin/dashboard')
    
    // 导航到用户管理页面
    await page.click('[data-testid="user-management-menu"]')
    await expect(page).toHaveURL('/admin/users')
  })

  test('创建新用户', async ({ page }) => {
    // 点击创建用户按钮
    await page.click('[data-testid="create-user-button"]')
    
    // 填写用户信息
    await page.fill('[data-testid="username"]', 'testuser')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'Password123!')
    await page.selectOption('[data-testid="role"]', 'USER')
    
    // 提交表单
    await page.click('[data-testid="submit-button"]')
    
    // 验证成功消息
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-message"]')).toContainText('用户创建成功')
    
    // 验证用户出现在列表中
    await expect(page.locator('[data-testid="user-list"]')).toContainText('testuser')
    await expect(page.locator('[data-testid="user-list"]')).toContainText('<EMAIL>')
  })

  test('搜索用户', async ({ page }) => {
    // 在搜索框中输入关键词
    await page.fill('[data-testid="search-input"]', 'admin')
    await page.press('[data-testid="search-input"]', 'Enter')
    
    // 等待搜索结果
    await page.waitForSelector('[data-testid="user-list"] tbody tr')
    
    // 验证搜索结果
    const rows = page.locator('[data-testid="user-list"] tbody tr')
    await expect(rows).toHaveCount(1)
    await expect(rows.first()).toContainText('admin')
  })

  test('编辑用户信息', async ({ page }) => {
    // 点击第一个用户的编辑按钮
    await page.click('[data-testid="user-list"] tbody tr:first-child [data-testid="edit-button"]')
    
    // 修改邮箱
    await page.fill('[data-testid="email"]', '<EMAIL>')
    
    // 保存修改
    await page.click('[data-testid="save-button"]')
    
    // 验证修改成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="user-list"]')).toContainText('<EMAIL>')
  })

  test('删除用户', async ({ page }) => {
    // 点击删除按钮
    await page.click('[data-testid="user-list"] tbody tr:first-child [data-testid="delete-button"]')
    
    // 确认删除
    await page.click('[data-testid="confirm-delete"]')
    
    // 验证删除成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-message"]')).toContainText('用户删除成功')
  })

  test('用户列表分页', async ({ page }) => {
    // 验证分页控件存在
    await expect(page.locator('[data-testid="pagination"]')).toBeVisible()
    
    // 点击下一页
    await page.click('[data-testid="next-page"]')
    
    // 验证URL包含页码参数
    await expect(page).toHaveURL(/page=2/)
    
    // 验证页面内容更新
    await expect(page.locator('[data-testid="current-page"]')).toContainText('2')
  })
})
```

### 移动端响应式测试

```typescript
// frontend/tests/e2e/mobile-responsive.spec.ts
import { test, expect } from '@playwright/test'

test.describe('移动端响应式', () => {
  test('移动端用户界面', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('/')
    
    // 验证移动端导航菜单
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="desktop-navigation"]')).not.toBeVisible()
    
    // 点击移动菜单
    await page.click('[data-testid="mobile-menu-button"]')
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
    
    // 验证移动端表单布局
    await page.goto('/assessment')
    const form = page.locator('[data-testid="assessment-form"]')
    await expect(form).toHaveCSS('flex-direction', 'column')
  })

  test('平板端适配', async ({ page }) => {
    // 设置平板端视口
    await page.setViewportSize({ width: 768, height: 1024 })
    
    await page.goto('/admin/dashboard')
    
    // 验证侧边栏在平板端的表现
    const sidebar = page.locator('[data-testid="sidebar"]')
    await expect(sidebar).toBeVisible()
    
    // 验证卡片网格布局
    const cardGrid = page.locator('[data-testid="dashboard-cards"]')
    await expect(cardGrid).toHaveCSS('grid-template-columns', /repeat\(2,/)
  })
})
```

## 🏋️ 性能测试

### JMeter性能测试

```xml
<!-- backend/src/test/jmeter/api-performance-test.jmx -->
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Assessment API Performance Test">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments">
          <elementProp name="baseUrl" elementType="Argument">
            <stringProp name="Argument.name">baseUrl</stringProp>
            <stringProp name="Argument.value">http://localhost:8080</stringProp>
          </elementProp>
          <elementProp name="users" elementType="Argument">
            <stringProp name="Argument.name">users</stringProp>
            <stringProp name="Argument.value">100</stringProp>
          </elementProp>
          <elementProp name="rampup" elementType="Argument">
            <stringProp name="Argument.name">rampup</stringProp>
            <stringProp name="Argument.value">300</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
    </TestPlan>
    
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="API Load Test">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">10</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">${users}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${rampup}</stringProp>
      </ThreadGroup>
      
      <hashTree>
        <!-- 登录请求 -->
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Login">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{"username":"testuser","password":"testpass"}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">${baseUrl}</stringProp>
          <stringProp name="HTTPSampler.path">/api/auth/login</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
        </HTTPSamplerProxy>
        
        <!-- 获取用户列表 -->
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Get Users">
          <stringProp name="HTTPSampler.domain">${baseUrl}</stringProp>
          <stringProp name="HTTPSampler.path">/api/users</stringProp>
          <stringProp name="HTTPSampler.method">GET</stringProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
        </HTTPSamplerProxy>
        
        <!-- 创建评估 -->
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Create Assessment">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <stringProp name="Argument.value">{"userId":1,"assessmentType":"BASIC","data":"test data"}</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">${baseUrl}</stringProp>
          <stringProp name="HTTPSampler.path">/api/assessments</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
        </HTTPSamplerProxy>
        
        <!-- 响应时间断言 -->
        <DurationAssertion guiclass="DurationAssertionGui" testclass="DurationAssertion" testname="Response Time < 2000ms">
          <stringProp name="DurationAssertion.duration">2000</stringProp>
        </DurationAssertion>
        
        <!-- 结果收集器 -->
        <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report"/>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree"/>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

### 前端性能测试

```typescript
// frontend/tests/performance/lighthouse.config.js
module.exports = {
  ci: {
    collect: {
      numberOfRuns: 3,
      startServerCommand: 'npm run preview',
      url: [
        'http://localhost:4173/',
        'http://localhost:4173/login',
        'http://localhost:4173/admin/dashboard',
        'http://localhost:4173/assessment'
      ]
    },
    assert: {
      assertions: {
        'categories:performance': ['error', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.8 }],
        'categories:seo': ['error', { minScore: 0.8 }],
        'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 3000 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }]
      }
    },
    upload: {
      target: 'temporary-public-storage'
    }
  }
}
```

## 📊 测试报告和覆盖率

### 测试配置文件

```xml
<!-- backend/pom.xml - 测试相关配置 -->
<plugin>
  <groupId>org.jacoco</groupId>
  <artifactId>jacoco-maven-plugin</artifactId>
  <version>0.8.8</version>
  <executions>
    <execution>
      <goals>
        <goal>prepare-agent</goal>
      </goals>
    </execution>
    <execution>
      <id>report</id>
      <phase>test</phase>
      <goals>
        <goal>report</goal>
      </goals>
    </execution>
    <execution>
      <id>check</id>
      <goals>
        <goal>check</goal>
      </goals>
      <configuration>
        <rules>
          <rule>
            <element>BUNDLE</element>
            <limits>
              <limit>
                <counter>INSTRUCTION</counter>
                <value>COVEREDRATIO</value>
                <minimum>0.80</minimum>
              </limit>
            </limits>
          </rule>
        </rules>
      </configuration>
    </execution>
  </executions>
</plugin>

<plugin>
  <groupId>org.apache.maven.plugins</groupId>
  <artifactId>maven-surefire-plugin</artifactId>
  <version>3.0.0-M9</version>
  <configuration>
    <includes>
      <include>**/*Test.java</include>
      <include>**/*Tests.java</include>
    </includes>
    <excludes>
      <exclude>**/*IntegrationTest.java</exclude>
    </excludes>
  </configuration>
</plugin>

<plugin>
  <groupId>org.apache.maven.plugins</groupId>
  <artifactId>maven-failsafe-plugin</artifactId>
  <version>3.0.0-M9</version>
  <configuration>
    <includes>
      <include>**/*IntegrationTest.java</include>
    </includes>
  </configuration>
  <executions>
    <execution>
      <goals>
        <goal>integration-test</goal>
        <goal>verify</goal>
      </goals>
    </execution>
  </executions>
</plugin>
```

### 前端测试配置

```typescript
// frontend/vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      },
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.test.ts',
        '**/*.spec.ts'
      ]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  }
})
```

### 测试报告生成脚本

```bash
#!/bin/bash
# scripts/generate-test-reports.sh

set -e

echo "🧪 生成测试报告..."

# 创建报告目录
mkdir -p reports/{backend,frontend,e2e}

# 后端测试报告
echo "📊 生成后端测试报告..."
cd backend
./mvnw clean test jacoco:report
cp -r target/site/jacoco/* ../reports/backend/
cp -r target/surefire-reports/* ../reports/backend/

# 前端测试报告
echo "📊 生成前端测试报告..."
cd ../frontend
npm run test:coverage
cp -r coverage/* ../reports/frontend/

# E2E测试报告
echo "📊 运行E2E测试..."
npm run test:e2e
cp -r test-results/* ../reports/e2e/
cp -r playwright-report/* ../reports/e2e/

# 性能测试报告
echo "📊 生成性能测试报告..."
npm run test:lighthouse
cp -r lighthouse-results/* ../reports/e2e/

# 生成汇总报告
echo "📊 生成汇总报告..."
cd ..
cat > reports/index.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>智慧养老评估平台 - 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .warning { background-color: #fff3cd; }
        .error { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>智慧养老评估平台 - 测试报告</h1>
    <div class="section">
        <h2>📊 测试概览</h2>
        <ul>
            <li><a href="backend/index.html">后端测试报告</a></li>
            <li><a href="frontend/index.html">前端测试报告</a></li>
            <li><a href="e2e/index.html">E2E测试报告</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>📈 覆盖率统计</h2>
        <p>后端代码覆盖率: <span id="backend-coverage">加载中...</span></p>
        <p>前端代码覆盖率: <span id="frontend-coverage">加载中...</span></p>
    </div>
    
    <div class="section">
        <h2>🎯 质量指标</h2>
        <p>单元测试通过率: <span id="unit-test-rate">加载中...</span></p>
        <p>集成测试通过率: <span id="integration-test-rate">加载中...</span></p>
        <p>E2E测试通过率: <span id="e2e-test-rate">加载中...</span></p>
    </div>
</body>
</html>
EOF

echo "✅ 测试报告生成完成！"
echo "📋 报告位置: reports/index.html"
```

## 🔧 持续集成中的测试

### GitHub Actions测试流程

```yaml
# .github/workflows/test-pipeline.yml
name: Test Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-suite: [backend, frontend-admin, frontend-website, frontend-mobile]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: 运行后端单元测试
        if: matrix.test-suite == 'backend'
        run: |
          cd backend
          ./mvnw test
          
      - name: 运行前端单元测试
        if: startsWith(matrix.test-suite, 'frontend')
        run: |
          cd frontend
          npm ci
          npm run test:unit:${{ matrix.test-suite }}
          
      - name: 上传测试结果
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.test-suite }}-test-results
          path: |
            backend/target/surefire-reports/
            frontend/test-results/

  integration-tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: assessment_test
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v4
      - name: 运行集成测试
        run: |
          cd backend
          ./mvnw integration-test

  e2e-tests:
    name: E2E测试
    runs-on: ubuntu-latest
    needs: integration-tests
    
    steps:
      - uses: actions/checkout@v4
      - name: 安装Playwright
        run: |
          cd frontend
          npm ci
          npx playwright install --with-deps
          
      - name: 运行E2E测试
        run: |
          cd frontend
          npm run test:e2e
          
      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-report
          path: |
            frontend/test-results/
            frontend/playwright-report/

  performance-tests:
    name: 性能测试
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      - name: 运行性能测试
        run: |
          cd frontend
          npm run test:lighthouse
```

## 📞 相关资源

- [CI/CD总览](./index.md) - 持续集成部署概述
- [GitHub Actions](./github-actions.md) - CI/CD流程配置
- [部署流水线](./deployment-pipeline.md) - 部署流程设计
- [代码质量](../../development/quality/) - 代码质量保证

---

*最后更新：2025-07-01*