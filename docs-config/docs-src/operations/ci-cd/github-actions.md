# 🔄 GitHub Actions CI/CD配置

## 🎯 GitHub Actions概述

智慧养老评估平台使用GitHub Actions作为持续集成和持续部署(CI/CD)的核心工具，实现代码质量检查、自动化测试、构建部署等全流程自动化。

## 🏗️ CI/CD流程架构

### 流水线架构图

```
┌─────────────────────────────────────────────────────────┐
│                     代码提交触发                          │
│  Push to main    │  Pull Request   │  Release Tag      │
├─────────────────────────────────────────────────────────┤
│                     代码质量检查                          │
│  Checkstyle      │  ESLint         │  SonarQube       │
│  Security Scan   │  Dependency     │  Code Coverage   │
├─────────────────────────────────────────────────────────┤
│                     自动化测试                           │
│  Unit Tests      │  Integration    │  E2E Tests       │
│  Backend Tests   │  Frontend Tests │  API Tests       │
├─────────────────────────────────────────────────────────┤
│                     构建和打包                           │
│  Maven Build     │  npm Build      │  Docker Build    │
│  JAR Package     │  Dist Files     │  Image Push      │
├─────────────────────────────────────────────────────────┤
│                     部署和发布                           │
│  Development     │  Staging        │  Production      │
│  Auto Deploy     │  Manual Approve │  Release Deploy  │
└─────────────────────────────────────────────────────────┘
```

## 📁 工作流文件结构

```
.github/
├── workflows/
│   ├── ci.yml                    # 持续集成主流程
│   ├── backend-build.yml         # 后端构建流程
│   ├── frontend-build.yml        # 前端构建流程
│   ├── deploy-dev.yml           # 开发环境部署
│   ├── deploy-staging.yml       # 测试环境部署
│   ├── deploy-prod.yml          # 生产环境部署
│   ├── security-scan.yml        # 安全扫描
│   └── release.yml              # 版本发布
├── actions/
│   ├── setup-java/              # 自定义Java环境action
│   ├── setup-node/              # 自定义Node环境action
│   └── docker-build/            # 自定义Docker构建action
└── ISSUE_TEMPLATE/              # Issue模板
```

## 🔄 主CI流程配置

### 持续集成主流程

```yaml
# .github/workflows/ci.yml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点执行
    - cron: '0 2 * * *'

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '18'
  MAVEN_OPTS: '-Dmaven.repo.local=.m2/repository -Xmx2048m'

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # SonarQube需要完整历史

      - name: 设置Java环境
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: maven

      - name: 设置Node.js环境
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: Maven依赖缓存
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}

      - name: 后端Checkstyle检查
        run: |
          cd backend
          ./mvnw checkstyle:check

      - name: 前端ESLint检查
        run: |
          cd frontend
          npm ci
          npm run lint

      - name: 安全依赖检查
        run: |
          cd backend
          ./mvnw org.owasp:dependency-check-maven:check

      - name: SonarQube分析
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          cd backend
          ./mvnw sonar:sonar \
            -Dsonar.projectKey=assessment-platform \
            -Dsonar.organization=assessment-team \
            -Dsonar.host.url=https://sonarcloud.io

      - name: 上传Checkstyle报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: checkstyle-report
          path: backend/target/checkstyle-result.xml

  # 后端测试
  backend-test:
    name: 后端测试
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: assessment_test
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Java环境
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: maven

      - name: 等待数据库就绪
        run: |
          until pg_isready -h localhost -p 5432 -U test; do
            echo "Waiting for postgres..."
            sleep 1
          done

      - name: 运行单元测试
        run: |
          cd backend
          ./mvnw test -Dspring.profiles.active=test

      - name: 运行集成测试
        run: |
          cd backend
          ./mvnw integration-test -Dspring.profiles.active=test

      - name: 生成测试报告
        run: |
          cd backend
          ./mvnw jacoco:report

      - name: 上传测试覆盖率到Codecov
        uses: codecov/codecov-action@v3
        with:
          file: backend/target/site/jacoco/jacoco.xml
          flags: backend

      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: backend-test-reports
          path: |
            backend/target/surefire-reports/
            backend/target/failsafe-reports/
            backend/target/site/jacoco/

  # 前端测试
  frontend-test:
    name: 前端测试
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [admin, website, mobile]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Node.js环境
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: 安装依赖
        run: |
          cd frontend
          npm ci

      - name: 运行单元测试
        run: |
          cd frontend
          npm run test:${{ matrix.app }}

      - name: 运行E2E测试
        run: |
          cd frontend
          npm run test:e2e:${{ matrix.app }}

      - name: 生成覆盖率报告
        run: |
          cd frontend
          npm run coverage:${{ matrix.app }}

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        with:
          file: frontend/coverage/lcov.info
          flags: frontend-${{ matrix.app }}

      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: frontend-${{ matrix.app }}-test-reports
          path: |
            frontend/test-results/
            frontend/coverage/

  # 构建验证
  build-verification:
    name: 构建验证
    runs-on: ubuntu-latest
    needs: [code-quality, backend-test, frontend-test]
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Java环境
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: maven

      - name: 设置Node.js环境
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: 构建后端
        run: |
          cd backend
          ./mvnw clean compile package -DskipTests

      - name: 构建前端
        run: |
          cd frontend
          npm ci
          npm run build:all

      - name: 验证构建产物
        run: |
          # 检查后端JAR文件
          test -f backend/target/*.jar
          
          # 检查前端构建产物
          test -d frontend/apps/admin/dist
          test -d frontend/apps/website/dist
          test -d frontend/apps/mobile/dist

      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            backend/target/*.jar
            frontend/apps/*/dist/
          retention-days: 7

  # 通知状态
  notify:
    name: 通知CI状态
    runs-on: ubuntu-latest
    needs: [build-verification]
    if: always()
    steps:
      - name: 发送成功通知
        if: needs.build-verification.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          fields: repo,message,commit,author,action,eventName,ref,workflow
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

      - name: 发送失败通知
        if: needs.build-verification.result == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          fields: repo,message,commit,author,action,eventName,ref,workflow
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
```

## 🏗️ 后端构建流程

### 后端专用构建流程

```yaml
# .github/workflows/backend-build.yml
name: Backend Build & Deploy

on:
  push:
    branches: [ main ]
    paths:
      - 'backend/**'
      - '.github/workflows/backend-build.yml'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: assessment/backend

jobs:
  build:
    name: 构建后端应用
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Java环境
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
          cache: maven

      - name: 提取版本信息
        id: meta
        run: |
          VERSION=$(cd backend && ./mvnw help:evaluate -Dexpression=project.version -q -DforceStdout)
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

      - name: Maven构建
        run: |
          cd backend
          ./mvnw clean package -DskipTests \
            -Dmaven.compiler.debug=false \
            -Dmaven.compiler.debuglevel=none

      - name: 运行测试
        run: |
          cd backend
          ./mvnw test

      - name: 登录Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 构建Docker镜像
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          labels: |
            org.opencontainers.image.title=Assessment Backend
            org.opencontainers.image.description=智慧养老评估平台后端服务
            org.opencontainers.image.source=https://github.com/${{ github.repository }}
            org.opencontainers.image.version=${{ steps.meta.outputs.version }}
            org.opencontainers.image.created=${{ github.event.head_commit.timestamp }}
            org.opencontainers.image.revision=${{ github.sha }}

      - name: 安全扫描
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 上传安全扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  deploy-dev:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: development

    steps:
      - name: 部署到开发环境
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DEV_HOST }}
          username: ${{ secrets.DEV_USER }}
          key: ${{ secrets.DEV_SSH_KEY }}
          script: |
            cd /opt/assessment
            docker-compose pull backend
            docker-compose up -d backend
            
            # 健康检查
            timeout 60s bash -c 'until curl -f http://localhost:8080/actuator/health; do sleep 5; done'
            echo "Backend deployed successfully"

      - name: 通知部署结果
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author
          text: |
            Backend deployed to development environment
            Version: ${{ steps.meta.outputs.version }}
            Commit: ${{ github.sha }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
```

## 🎨 前端构建流程

### 前端专用构建流程

```yaml
# .github/workflows/frontend-build.yml
name: Frontend Build & Deploy

on:
  push:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - '.github/workflows/frontend-build.yml'

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io

jobs:
  build:
    name: 构建前端应用
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [admin, website, mobile]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Node.js环境
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: 安装依赖
        run: |
          cd frontend
          npm ci

      - name: 类型检查
        run: |
          cd frontend
          npm run type-check:${{ matrix.app }}

      - name: 构建应用
        run: |
          cd frontend
          npm run build:${{ matrix.app }}
        env:
          VITE_API_BASE_URL: ${{ secrets.API_BASE_URL }}
          VITE_APP_VERSION: ${{ github.sha }}

      - name: 构建分析
        run: |
          cd frontend
          npm run analyze:${{ matrix.app }}

      - name: 登录Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 构建Docker镜像
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/apps/${{ matrix.app }}/Dockerfile
          push: true
          tags: |
            ${{ env.REGISTRY }}/assessment/${{ matrix.app }}:latest
            ${{ env.REGISTRY }}/assessment/${{ matrix.app }}:${{ github.sha }}

      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.app }}-dist
          path: frontend/apps/${{ matrix.app }}/dist/

      - name: 上传构建分析
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.app }}-analysis
          path: frontend/apps/${{ matrix.app }}/dist-analysis/

  lighthouse:
    name: Lighthouse性能测试
    runs-on: ubuntu-latest
    needs: build
    strategy:
      matrix:
        app: [admin, website]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 下载构建产物
        uses: actions/download-artifact@v3
        with:
          name: ${{ matrix.app }}-dist
          path: frontend/apps/${{ matrix.app }}/dist/

      - name: 启动静态服务器
        run: |
          npx serve -s frontend/apps/${{ matrix.app }}/dist -l 3000 &
          sleep 5

      - name: 运行Lighthouse
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: ./frontend/.lighthouserc.js
          uploadDir: ./lighthouse-reports
          temporaryPublicStorage: true

      - name: 上传Lighthouse报告
        uses: actions/upload-artifact@v3
        with:
          name: lighthouse-${{ matrix.app }}-report
          path: lighthouse-reports/

  deploy-cdn:
    name: 部署到CDN
    runs-on: ubuntu-latest
    needs: [build, lighthouse]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: 下载所有构建产物
        uses: actions/download-artifact@v3

      - name: 部署管理后台到CDN
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: 同步到S3
        run: |
          aws s3 sync admin-dist/ s3://${{ secrets.S3_BUCKET_ADMIN }}/ --delete
          aws s3 sync website-dist/ s3://${{ secrets.S3_BUCKET_WEBSITE }}/ --delete

      - name: 创建CloudFront失效
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.CLOUDFRONT_ADMIN_ID }} \
            --paths "/*"
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.CLOUDFRONT_WEBSITE_ID }} \
            --paths "/*"
```

## 🚀 部署流程

### 生产环境部署

```yaml
# .github/workflows/deploy-prod.yml
name: Production Deployment

on:
  release:
    types: [published]

env:
  REGISTRY: ghcr.io

jobs:
  deploy:
    name: 生产环境部署
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 提取版本信息
        id: meta
        run: |
          echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

      - name: 验证镜像存在
        run: |
          docker manifest inspect ${{ env.REGISTRY }}/assessment/backend:${{ steps.meta.outputs.version }}
          docker manifest inspect ${{ env.REGISTRY }}/assessment/admin:${{ steps.meta.outputs.version }}
          docker manifest inspect ${{ env.REGISTRY }}/assessment/website:${{ steps.meta.outputs.version }}

      - name: 部署到生产环境
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /opt/assessment
            
            # 备份当前版本
            docker-compose -f docker-compose.prod.yml down
            ./scripts/backup.sh
            
            # 更新环境变量
            export APP_VERSION=${{ steps.meta.outputs.version }}
            
            # 拉取新镜像
            docker-compose -f docker-compose.prod.yml pull
            
            # 启动服务
            docker-compose -f docker-compose.prod.yml up -d
            
            # 健康检查
            ./scripts/health-check.sh
            
            echo "Production deployment completed successfully"

      - name: 发送部署通知
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author
          text: |
            🚀 Production deployment completed!
            Version: ${{ steps.meta.outputs.version }}
            Status: ${{ job.status }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

      - name: 创建部署记录
        if: success()
        uses: peter-evans/create-issue-from-file@v4
        with:
          title: "生产环境部署记录 - ${{ steps.meta.outputs.version }}"
          content-filepath: .github/templates/deployment-record.md
          labels: deployment,production
```

## 🔒 安全扫描流程

### 安全扫描配置

```yaml
# .github/workflows/security-scan.yml
name: Security Scan

on:
  schedule:
    # 每周一凌晨执行
    - cron: '0 2 * * 1'
  workflow_dispatch:

jobs:
  dependency-scan:
    name: 依赖安全扫描
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Java环境
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
          cache: maven

      - name: 后端依赖扫描
        run: |
          cd backend
          ./mvnw org.owasp:dependency-check-maven:check \
            -DnvdApiKey=${{ secrets.NVD_API_KEY }} \
            -DfailBuildOnCVSS=7

      - name: 设置Node.js环境
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: 前端依赖扫描
        run: |
          cd frontend
          npm audit --audit-level high
          npx audit-ci --config audit-ci.json

      - name: 上传扫描报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            backend/target/dependency-check-report.html
            frontend/audit-results.json

  code-scan:
    name: 代码安全扫描
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: CodeQL分析
        uses: github/codeql-action/init@v2
        with:
          languages: java, javascript

      - name: 自动构建
        uses: github/codeql-action/autobuild@v2

      - name: 执行CodeQL分析
        uses: github/codeql-action/analyze@v2

      - name: Semgrep扫描
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten

  container-scan:
    name: 容器安全扫描
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 构建镜像
        run: |
          docker build -t assessment/backend:scan ./backend
          docker build -t assessment/admin:scan ./frontend/apps/admin

      - name: Trivy漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'assessment/backend:scan'
          format: 'sarif'
          output: 'trivy-backend.sarif'

      - name: 上传Trivy结果
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-backend.sarif'

      - name: Anchore容器扫描
        uses: anchore/scan-action@v3
        with:
          image: 'assessment/backend:scan'
          fail-build: true
          severity-cutoff: high
```

## 📊 自定义Actions

### Java环境设置Action

```yaml
# .github/actions/setup-java/action.yml
name: 'Setup Java Environment'
description: '设置Java开发环境'

inputs:
  java-version:
    description: 'Java版本'
    required: false
    default: '21'
  maven-cache:
    description: '是否启用Maven缓存'
    required: false
    default: 'true'

runs:
  using: 'composite'
  steps:
    - name: 设置Java
      uses: actions/setup-java@v4
      with:
        java-version: ${{ inputs.java-version }}
        distribution: 'temurin'
        cache: ${{ inputs.maven-cache == 'true' && 'maven' || '' }}

    - name: 验证Java安装
      shell: bash
      run: |
        java -version
        javac -version
        echo "JAVA_HOME=$JAVA_HOME" >> $GITHUB_ENV

    - name: 配置Maven设置
      shell: bash
      run: |
        mkdir -p ~/.m2
        cat > ~/.m2/settings.xml << EOF
        <?xml version="1.0" encoding="UTF-8"?>
        <settings>
          <servers>
            <server>
              <id>github</id>
              <username>\${env.GITHUB_ACTOR}</username>
              <password>\${env.GITHUB_TOKEN}</password>
            </server>
          </servers>
        </settings>
        EOF
```

### Docker构建Action

```yaml
# .github/actions/docker-build/action.yml
name: 'Docker Build and Push'
description: '构建并推送Docker镜像'

inputs:
  image-name:
    description: '镜像名称'
    required: true
  context:
    description: '构建上下文'
    required: true
  dockerfile:
    description: 'Dockerfile路径'
    required: false
    default: 'Dockerfile'
  push:
    description: '是否推送镜像'
    required: false
    default: 'false'
  tags:
    description: '镜像标签'
    required: false
  registry:
    description: '镜像仓库'
    required: false
    default: 'ghcr.io'

outputs:
  image:
    description: '完整镜像名称'
    value: ${{ steps.meta.outputs.image }}
  digest:
    description: '镜像摘要'
    value: ${{ steps.build.outputs.digest }}

runs:
  using: 'composite'
  steps:
    - name: 提取镜像元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ inputs.registry }}/${{ inputs.image-name }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix=sha-

    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 登录镜像仓库
      if: inputs.push == 'true'
      uses: docker/login-action@v3
      with:
        registry: ${{ inputs.registry }}
        username: ${{ github.actor }}
        password: ${{ github.token }}

    - name: 构建并推送镜像
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ${{ inputs.context }}
        file: ${{ inputs.context }}/${{ inputs.dockerfile }}
        push: ${{ inputs.push }}
        tags: ${{ inputs.tags || steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: 镜像安全扫描
      if: inputs.push == 'true'
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ steps.meta.outputs.image }}
        format: 'table'
```

## 🔧 环境配置

### GitHub Secrets配置

```bash
# 基础配置
GITHUB_TOKEN                 # GitHub访问令牌
SONAR_TOKEN                  # SonarQube令牌
SLACK_WEBHOOK               # Slack通知Webhook

# 部署配置
DEV_HOST                    # 开发环境主机
DEV_USER                    # 开发环境用户
DEV_SSH_KEY                 # 开发环境SSH密钥
PROD_HOST                   # 生产环境主机
PROD_USER                   # 生产环境用户
PROD_SSH_KEY               # 生产环境SSH密钥

# 云服务配置
AWS_ACCESS_KEY_ID          # AWS访问密钥
AWS_SECRET_ACCESS_KEY      # AWS秘密密钥
S3_BUCKET_ADMIN           # S3存储桶(管理后台)
S3_BUCKET_WEBSITE         # S3存储桶(用户网站)
CLOUDFRONT_ADMIN_ID       # CloudFront分发ID(管理后台)
CLOUDFRONT_WEBSITE_ID     # CloudFront分发ID(用户网站)

# 安全扫描配置
NVD_API_KEY               # NVD漏洞数据库API密钥
SNYK_TOKEN                # Snyk安全扫描令牌

# 应用配置
API_BASE_URL              # API基础URL
DATABASE_URL              # 数据库连接URL
REDIS_URL                 # Redis连接URL
```

### 环境保护规则

```yaml
# 生产环境保护配置
environments:
  production:
    protection_rules:
      - type: required_reviewers
        required_reviewers:
          - admin-team
          - senior-developers
      - type: wait_timer
        minutes: 30
      - type: branch_policy
        required_branches:
          - main
    deployment_branch_policy:
      protected_branches: true
      custom_branch_policies: false
```

## 📊 监控和报告

### 流水线状态监控

```yaml
# .github/workflows/pipeline-monitor.yml
name: Pipeline Monitor

on:
  workflow_run:
    workflows: ["CI Pipeline", "Backend Build & Deploy", "Frontend Build & Deploy"]
    types: [completed]

jobs:
  monitor:
    runs-on: ubuntu-latest
    steps:
      - name: 获取工作流状态
        uses: actions/github-script@v7
        with:
          script: |
            const workflow = context.payload.workflow_run;
            const status = workflow.conclusion;
            const duration = new Date(workflow.updated_at) - new Date(workflow.created_at);
            
            // 发送监控数据到监控系统
            await github.rest.repos.createDispatchEvent({
              owner: context.repo.owner,
              repo: context.repo.repo,
              event_type: 'pipeline_metrics',
              client_payload: {
                workflow_name: workflow.name,
                status: status,
                duration: duration,
                run_id: workflow.id,
                branch: workflow.head_branch
              }
            });

      - name: 更新状态页面
        if: failure()
        uses: maxkomarychev/oction-create-issue@v0.7.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          title: "CI/CD Pipeline Failure Alert"
          body: |
            ## 🚨 Pipeline失败告警
            
            **工作流**: ${{ github.event.workflow_run.name }}
            **分支**: ${{ github.event.workflow_run.head_branch }}
            **提交**: ${{ github.event.workflow_run.head_sha }}
            **失败时间**: ${{ github.event.workflow_run.updated_at }}
            
            请及时检查并修复问题。
          labels: bug,ci-cd,urgent
```

## 📞 相关资源

- [CI/CD总览](./index.md) - 持续集成部署概述
- [自动化测试](./automated-testing.md) - 测试自动化指南
- [部署流水线](./deployment-pipeline.md) - 部署流程设计
- [Docker部署](../docker-deployment.md) - 容器化部署

---

*最后更新：2025-07-01*