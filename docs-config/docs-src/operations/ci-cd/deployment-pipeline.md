# 🚀 部署流水线设计

## 🎯 部署流水线概述

智慧养老评估平台采用现代化的部署流水线，实现从代码提交到生产部署的全自动化流程，确保部署的可靠性、可追溯性和零停机时间。

## 🏗️ 流水线架构

### 部署流程图

```
┌─────────────────────────────────────────────────────────────────┐
│                         源码管理                                  │
│   GitHub Repository   │   Branch Strategy   │   Tag Management   │
├─────────────────────────────────────────────────────────────────┤
│                        构建阶段                                   │
│   Code Quality Check  │   Security Scan     │   Unit Tests       │
│   Build Artifacts     │   Package Docker    │   Image Registry   │
├─────────────────────────────────────────────────────────────────┤
│                        部署阶段                                   │
│   Development Env     │   Staging Env       │   Production Env   │
│   Auto Deploy         │   Manual Approval   │   Blue-Green       │
├─────────────────────────────────────────────────────────────────┤
│                       监控验证                                    │
│   Health Check        │   Smoke Tests       │   Rollback Ready   │
│   Performance Monitor │   Alert System     │   Deployment Log   │
└─────────────────────────────────────────────────────────────────┘
```

### 环境策略

**开发环境 (Development)**
- 自动部署：main分支推送触发
- 数据库：独立测试数据库
- 监控：基础健康检查
- 访问：内部开发团队

**测试环境 (Staging)**
- 手动触发：通过GitHub Actions手动部署
- 数据库：生产数据的脱敏副本
- 监控：完整监控体系
- 访问：QA团队和产品团队

**生产环境 (Production)**
- 发布部署：仅限tag版本
- 数据库：生产数据库集群
- 监控：7x24小时监控
- 访问：最终用户

## 🔄 分支策略

### GitFlow工作流

```
main (生产分支)
├── develop (开发分支)
│   ├── feature/user-management (功能分支)
│   ├── feature/assessment-system (功能分支)
│   └── hotfix/security-patch (热修复分支)
├── release/v1.2.0 (发布分支)
└── hotfix/critical-bug (紧急修复分支)
```

### 分支保护规则

```yaml
# GitHub分支保护配置
branches:
  main:
    protection:
      required_status_checks:
        strict: true
        contexts:
          - "ci/backend-tests"
          - "ci/frontend-tests"
          - "ci/security-scan"
          - "ci/build-verification"
      enforce_admins: true
      required_pull_request_reviews:
        required_approving_review_count: 2
        dismiss_stale_reviews: true
        require_code_owner_reviews: true
        restrictions:
          users: []
          teams: ["senior-developers", "tech-leads"]
      restrictions:
        users: []
        teams: ["admins"]
  
  develop:
    protection:
      required_status_checks:
        strict: true
        contexts:
          - "ci/backend-tests"
          - "ci/frontend-tests"
      required_pull_request_reviews:
        required_approving_review_count: 1
```

## 🏗️ 构建流水线

### Maven构建配置

```xml
<!-- backend/pom.xml - 构建配置 -->
<profiles>
  <profile>
    <id>development</id>
    <activation>
      <activeByDefault>true</activeByDefault>
    </activation>
    <properties>
      <spring.profiles.active>development</spring.profiles.active>
      <maven.test.skip>false</maven.test.skip>
    </properties>
  </profile>
  
  <profile>
    <id>staging</id>
    <properties>
      <spring.profiles.active>staging</spring.profiles.active>
      <maven.test.skip>false</maven.test.skip>
    </properties>
  </profile>
  
  <profile>
    <id>production</id>
    <properties>
      <spring.profiles.active>production</spring.profiles.active>
      <maven.test.skip>true</maven.test.skip>
      <maven.compiler.debug>false</maven.compiler.debug>
      <maven.compiler.optimize>true</maven.compiler.optimize>
    </properties>
  </profile>
</profiles>

<build>
  <plugins>
    <plugin>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-maven-plugin</artifactId>
      <configuration>
        <layers>
          <enabled>true</enabled>
        </layers>
        <image>
          <name>ghcr.io/assessment/backend:${project.version}</name>
          <env>
            <BP_JVM_VERSION>21</BP_JVM_VERSION>
            <BPE_DELIM_JAVA_TOOL_OPTIONS> </BPE_DELIM_JAVA_TOOL_OPTIONS>
            <BPE_APPEND_JAVA_TOOL_OPTIONS>-XX:+UseContainerSupport -XX:+UseG1GC</BPE_APPEND_JAVA_TOOL_OPTIONS>
          </env>
        </image>
      </configuration>
    </plugin>
    
    <!-- 版本管理插件 -->
    <plugin>
      <groupId>org.codehaus.mojo</groupId>
      <artifactId>versions-maven-plugin</artifactId>
      <version>2.15.0</version>
      <configuration>
        <generateBackupPoms>false</generateBackupPoms>
      </configuration>
    </plugin>
    
    <!-- 构建信息插件 -->
    <plugin>
      <groupId>pl.project13.maven</groupId>
      <artifactId>git-commit-id-plugin</artifactId>
      <version>4.9.10</version>
      <executions>
        <execution>
          <goals>
            <goal>revision</goal>
          </goals>
        </execution>
      </executions>
      <configuration>
        <verbose>true</verbose>
        <dateFormat>yyyy-MM-dd'T'HH:mm:ssZ</dateFormat>
        <generateGitPropertiesFile>true</generateGitPropertiesFile>
        <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties</generateGitPropertiesFilename>
      </configuration>
    </plugin>
  </plugins>
</build>
```

### 前端构建配置

```typescript
// frontend/turbo.json - Monorepo构建配置
{
  "$schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", ".next/**", "!.next/cache/**"],
      "env": [
        "NODE_ENV",
        "VITE_API_BASE_URL",
        "VITE_APP_VERSION",
        "VITE_ENVIRONMENT"
      ]
    },
    "build:prod": {
      "dependsOn": ["^build:prod"],
      "outputs": ["dist/**"],
      "env": [
        "NODE_ENV",
        "VITE_API_BASE_URL",
        "VITE_APP_VERSION",
        "VITE_CDN_URL"
      ]
    },
    "test": {
      "dependsOn": ["^test"],
      "outputs": ["coverage/**"],
      "inputs": ["src/**/*.{ts,tsx,vue}", "tests/**/*.{ts,tsx}"]
    },
    "test:e2e": {
      "dependsOn": ["build"],
      "outputs": ["test-results/**", "playwright-report/**"]
    },
    "lint": {
      "outputs": []
    },
    "type-check": {
      "outputs": []
    }
  }
}
```

```json
// frontend/package.json - 构建脚本
{
  "scripts": {
    "build": "turbo run build",
    "build:prod": "turbo run build:prod",
    "build:admin": "cd apps/admin && npm run build",
    "build:website": "cd apps/website && npm run build",
    "build:mobile": "cd apps/mobile && npm run build",
    "test": "turbo run test",
    "test:coverage": "turbo run test -- --coverage",
    "test:e2e": "turbo run test:e2e",
    "lint": "turbo run lint",
    "type-check": "turbo run type-check",
    "preview": "turbo run preview",
    "clean": "turbo run clean && rm -rf node_modules"
  }
}
```

## 🐳 Docker化部署

### 多阶段构建Dockerfile

```dockerfile
# backend/Dockerfile
FROM eclipse-temurin:21-jdk-alpine AS builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# 下载依赖（利用Docker缓存）
RUN ./mvnw dependency:go-offline -B

# 构建应用
RUN ./mvnw clean package -DskipTests

# 运行时镜像
FROM eclipse-temurin:21-jre-alpine

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 安装必要工具
RUN apk add --no-cache curl dumb-init

WORKDIR /app

# 复制构建产物
COPY --from=builder /app/target/*.jar app.jar

# 创建目录并设置权限
RUN mkdir -p /app/logs /app/uploads && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 使用dumb-init处理信号
ENTRYPOINT ["dumb-init", "--"]
CMD ["java", \
     "-XX:+UseContainerSupport", \
     "-XX:+UseG1GC", \
     "-XX:MaxRAMPercentage=75", \
     "-Djava.security.egd=file:/dev/./urandom", \
     "-jar", "app.jar"]
```

```dockerfile
# frontend/apps/admin/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY turbo.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:admin

# 生产环境镜像
FROM nginx:alpine

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 复制构建产物
COPY --from=builder /app/apps/admin/dist /usr/share/nginx/html

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose部署配置

```yaml
# docker-compose.deploy.yml
version: '3.8'

services:
  backend:
    image: ghcr.io/assessment/backend:${APP_VERSION}
    environment:
      SPRING_PROFILES_ACTIVE: ${ENVIRONMENT}
      SPRING_DATASOURCE_URL: *******************************/${POSTGRES_DB}
      SPRING_DATASOURCE_USERNAME: ${POSTGRES_USER}
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD}
      JAVA_OPTS: >-
        -Xms${BACKEND_MIN_MEMORY:-512m}
        -Xmx${BACKEND_MAX_MEMORY:-1g}
        -XX:+UseG1GC
        -XX:+UseContainerSupport
        -XX:MaxRAMPercentage=75
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    networks:
      - app-network
    deploy:
      replicas: ${BACKEND_REPLICAS:-2}
      resources:
        limits:
          memory: ${BACKEND_MAX_MEMORY:-1g}
        reservations:
          memory: ${BACKEND_MIN_MEMORY:-512m}
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        order: start-first
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  frontend-admin:
    image: ghcr.io/assessment/admin:${APP_VERSION}
    environment:
      API_BASE_URL: ${API_BASE_URL}
      ENVIRONMENT: ${ENVIRONMENT}
    networks:
      - app-network
    deploy:
      replicas: ${FRONTEND_REPLICAS:-2}
      resources:
        limits:
          memory: 128m
        reservations:
          memory: 64m
      restart_policy:
        condition: on-failure
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first

  nginx:
    image: nginx:alpine
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.${ENVIRONMENT}.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend-admin
    networks:
      - app-network
    deploy:
      restart_policy:
        condition: on-failure

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backup:/backup
    networks:
      - app-network
    deploy:
      restart_policy:
        condition: on-failure

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - app-network
    deploy:
      restart_policy:
        condition: on-failure

volumes:
  backend_logs:
  backend_uploads:
  nginx_logs:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: overlay
    attachable: true
```

## 🚀 部署策略

### 蓝绿部署

```bash
#!/bin/bash
# scripts/blue-green-deploy.sh

set -e

ENVIRONMENT=${1:-production}
VERSION=${2:-latest}
CURRENT_COLOR=$(cat /opt/assessment/.current-color 2>/dev/null || echo "blue")
NEW_COLOR="green"

if [ "$CURRENT_COLOR" = "green" ]; then
    NEW_COLOR="blue"
fi

echo "🚀 开始蓝绿部署..."
echo "📊 当前环境: $CURRENT_COLOR"
echo "🎯 目标环境: $NEW_COLOR"
echo "📦 部署版本: $VERSION"

# 部署新环境
deploy_new_environment() {
    echo "📦 部署新环境 $NEW_COLOR..."
    
    # 创建新环境配置
    export COMPOSE_PROJECT_NAME="assessment-$NEW_COLOR"
    export APP_VERSION=$VERSION
    export ENVIRONMENT=$ENVIRONMENT
    export HTTP_PORT=$((NEW_COLOR == "blue" ? 8080 : 8081))
    
    # 启动新环境
    docker-compose -f docker-compose.deploy.yml up -d
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    timeout 300s bash -c '
        while ! curl -f http://localhost:'$HTTP_PORT'/actuator/health >/dev/null 2>&1; do
            echo "等待后端服务启动..."
            sleep 5
        done
    '
    
    echo "✅ 新环境 $NEW_COLOR 部署完成"
}

# 健康检查
health_check() {
    echo "🏥 执行健康检查..."
    
    local health_url="http://localhost:$HTTP_PORT/actuator/health"
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$health_url" >/dev/null 2>&1; then
            echo "✅ 健康检查通过"
            return 0
        fi
        
        echo "⏳ 健康检查失败，重试 $attempt/$max_attempts..."
        sleep 10
        ((attempt++))
    done
    
    echo "❌ 健康检查失败"
    return 1
}

# 烟雾测试
smoke_test() {
    echo "🚬 执行烟雾测试..."
    
    local base_url="http://localhost:$HTTP_PORT"
    
    # 测试主要API端点
    local endpoints=(
        "/actuator/health"
        "/api/users"
        "/api/auth/status"
    )
    
    for endpoint in "${endpoints[@]}"; do
        echo "测试端点: $endpoint"
        if ! curl -f "$base_url$endpoint" >/dev/null 2>&1; then
            echo "❌ 端点测试失败: $endpoint"
            return 1
        fi
    done
    
    echo "✅ 烟雾测试通过"
}

# 切换流量
switch_traffic() {
    echo "🔄 切换流量到 $NEW_COLOR 环境..."
    
    # 更新Nginx配置
    sed -i "s/assessment-$CURRENT_COLOR/assessment-$NEW_COLOR/g" /etc/nginx/sites-enabled/assessment
    
    # 重新加载Nginx
    nginx -s reload
    
    # 更新当前颜色标记
    echo "$NEW_COLOR" > /opt/assessment/.current-color
    
    echo "✅ 流量切换完成"
}

# 清理旧环境
cleanup_old_environment() {
    echo "🧹 清理旧环境 $CURRENT_COLOR..."
    
    # 等待一段时间确保流量切换完成
    sleep 30
    
    # 停止旧环境
    export COMPOSE_PROJECT_NAME="assessment-$CURRENT_COLOR"
    docker-compose -f docker-compose.deploy.yml down
    
    echo "✅ 旧环境清理完成"
}

# 回滚操作
rollback() {
    echo "🔙 执行回滚操作..."
    
    # 切换回旧环境
    sed -i "s/assessment-$NEW_COLOR/assessment-$CURRENT_COLOR/g" /etc/nginx/sites-enabled/assessment
    nginx -s reload
    echo "$CURRENT_COLOR" > /opt/assessment/.current-color
    
    # 停止新环境
    export COMPOSE_PROJECT_NAME="assessment-$NEW_COLOR"
    docker-compose -f docker-compose.deploy.yml down
    
    echo "❌ 部署失败，已回滚到 $CURRENT_COLOR 环境"
    exit 1
}

# 主部署流程
main() {
    trap rollback ERR
    
    deploy_new_environment
    
    if ! health_check; then
        rollback
    fi
    
    if ! smoke_test; then
        rollback
    fi
    
    switch_traffic
    cleanup_old_environment
    
    echo "🎉 蓝绿部署成功完成！"
    echo "📊 当前环境: $NEW_COLOR"
    echo "📦 运行版本: $VERSION"
}

# 执行部署
main "$@"
```

### 金丝雀部署

```bash
#!/bin/bash
# scripts/canary-deploy.sh

set -e

ENVIRONMENT=${1:-production}
VERSION=${2:-latest}
CANARY_PERCENTAGE=${3:-10}

echo "🐦 开始金丝雀部署..."
echo "📦 部署版本: $VERSION"
echo "📊 金丝雀流量比例: $CANARY_PERCENTAGE%"

# 部署金丝雀环境
deploy_canary() {
    echo "📦 部署金丝雀环境..."
    
    export COMPOSE_PROJECT_NAME="assessment-canary"
    export APP_VERSION=$VERSION
    export ENVIRONMENT=$ENVIRONMENT
    export HTTP_PORT=8082
    
    docker-compose -f docker-compose.deploy.yml up -d
    
    # 等待服务启动
    timeout 300s bash -c '
        while ! curl -f http://localhost:8082/actuator/health >/dev/null 2>&1; do
            echo "等待金丝雀服务启动..."
            sleep 5
        done
    '
    
    echo "✅ 金丝雀环境部署完成"
}

# 配置流量分割
configure_traffic_split() {
    echo "🔀 配置流量分割 ($CANARY_PERCENTAGE% -> 金丝雀)..."
    
    # 更新Nginx配置实现流量分割
    cat > /etc/nginx/conf.d/canary.conf << EOF
upstream backend_stable {
    server localhost:8080 weight=$((100 - CANARY_PERCENTAGE));
}

upstream backend_canary {
    server localhost:8082 weight=$CANARY_PERCENTAGE;
}

upstream backend_mixed {
    server localhost:8080 weight=$((100 - CANARY_PERCENTAGE));
    server localhost:8082 weight=$CANARY_PERCENTAGE;
}

server {
    listen 80;
    server_name api.assessment.com;
    
    location /api/ {
        proxy_pass http://backend_mixed;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
}
EOF
    
    nginx -s reload
    echo "✅ 流量分割配置完成"
}

# 监控金丝雀指标
monitor_canary() {
    echo "📊 监控金丝雀指标..."
    
    local monitoring_duration=${4:-600} # 默认监控10分钟
    local start_time=$(date +%s)
    local error_threshold=5 # 错误率阈值5%
    
    while [ $(($(date +%s) - start_time)) -lt $monitoring_duration ]; do
        # 获取金丝雀环境指标
        local canary_errors=$(curl -s http://localhost:8082/actuator/metrics/http.server.requests | jq -r '.measurements[] | select(.statistic=="COUNT" and .tags[] | select(.key=="status" and (.value | startswith("4") or startswith("5")))) | .value')
        local canary_total=$(curl -s http://localhost:8082/actuator/metrics/http.server.requests | jq -r '.measurements[] | select(.statistic=="COUNT") | .value')
        
        if [ "$canary_total" -gt 0 ]; then
            local canary_error_rate=$(echo "scale=2; $canary_errors * 100 / $canary_total" | bc)
            echo "📈 金丝雀错误率: $canary_error_rate%"
            
            if (( $(echo "$canary_error_rate > $error_threshold" | bc -l) )); then
                echo "❌ 金丝雀错误率超过阈值，执行回滚"
                rollback_canary
                return 1
            fi
        fi
        
        sleep 30
    done
    
    echo "✅ 金丝雀监控通过"
}

# 推广金丝雀
promote_canary() {
    echo "⬆️ 推广金丝雀到100%流量..."
    
    # 逐步增加金丝雀流量
    local steps=(25 50 75 100)
    
    for step in "${steps[@]}"; do
        echo "📈 增加金丝雀流量到 $step%..."
        
        # 更新Nginx权重配置
        sed -i "s/weight=$CANARY_PERCENTAGE/weight=$step/g" /etc/nginx/conf.d/canary.conf
        sed -i "s/weight=$((100 - CANARY_PERCENTAGE))/weight=$((100 - step))/g" /etc/nginx/conf.d/canary.conf
        nginx -s reload
        
        # 等待并监控
        sleep 60
        
        # 简单健康检查
        if ! curl -f http://localhost:8082/actuator/health >/dev/null 2>&1; then
            echo "❌ 金丝雀健康检查失败"
            rollback_canary
            return 1
        fi
        
        CANARY_PERCENTAGE=$step
    done
    
    echo "✅ 金丝雀推广完成"
}

# 完成部署
finalize_deployment() {
    echo "🎯 完成部署..."
    
    # 停止旧的stable环境
    export COMPOSE_PROJECT_NAME="assessment-stable"
    docker-compose -f docker-compose.deploy.yml down
    
    # 将金丝雀重命名为stable
    export COMPOSE_PROJECT_NAME="assessment-canary"
    docker-compose -f docker-compose.deploy.yml down
    
    export COMPOSE_PROJECT_NAME="assessment-stable"
    export HTTP_PORT=8080
    docker-compose -f docker-compose.deploy.yml up -d
    
    # 恢复正常Nginx配置
    cat > /etc/nginx/conf.d/default.conf << EOF
upstream backend {
    server localhost:8080;
}

server {
    listen 80;
    server_name api.assessment.com;
    
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
}
EOF
    
    nginx -s reload
    echo "✅ 部署完成"
}

# 回滚金丝雀
rollback_canary() {
    echo "🔙 回滚金丝雀部署..."
    
    # 停止金丝雀环境
    export COMPOSE_PROJECT_NAME="assessment-canary"
    docker-compose -f docker-compose.deploy.yml down
    
    # 恢复100%流量到stable环境
    rm -f /etc/nginx/conf.d/canary.conf
    nginx -s reload
    
    echo "❌ 金丝雀部署失败，已回滚"
    exit 1
}

# 主部署流程
main() {
    trap rollback_canary ERR
    
    deploy_canary
    configure_traffic_split
    
    if ! monitor_canary; then
        exit 1
    fi
    
    promote_canary
    finalize_deployment
    
    echo "🎉 金丝雀部署成功完成！"
    echo "📦 运行版本: $VERSION"
}

# 执行部署
main "$@"
```

## 📊 部署监控

### 部署状态监控

```yaml
# .github/workflows/deployment-monitor.yml
name: Deployment Monitor

on:
  deployment_status:

jobs:
  monitor:
    runs-on: ubuntu-latest
    if: github.event.deployment_status.state == 'success'
    
    steps:
      - name: 等待服务稳定
        run: sleep 120
        
      - name: 健康检查
        run: |
          HEALTH_URL="${{ github.event.deployment.payload.web_url }}/actuator/health"
          
          for i in {1..10}; do
            if curl -f "$HEALTH_URL"; then
              echo "✅ 健康检查通过"
              exit 0
            fi
            echo "⏳ 等待服务启动... ($i/10)"
            sleep 30
          done
          
          echo "❌ 健康检查失败"
          exit 1
          
      - name: 性能基准测试
        run: |
          # 使用artillery进行简单的性能测试
          npx artillery quick \
            --count 50 \
            --num 10 \
            "${{ github.event.deployment.payload.web_url }}/api/health"
            
      - name: 发送部署通知
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,took
          text: |
            🚀 部署完成
            环境: ${{ github.event.deployment.environment }}
            版本: ${{ github.event.deployment.ref }}
            状态: ${{ github.event.deployment_status.state }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
```

### Prometheus部署指标

```yaml
# monitoring/deployment-metrics.yml
groups:
  - name: deployment_metrics
    rules:
      - record: deployment:duration_seconds
        expr: |
          (
            time() - 
            on() kube_deployment_status_condition{condition="Progressing",status="True"} * 
            on() kube_deployment_status_condition_last_transition_time
          )
        
      - record: deployment:success_rate
        expr: |
          (
            sum(rate(deployment_status_total{status="success"}[1h])) /
            sum(rate(deployment_status_total[1h]))
          ) * 100
          
      - record: deployment:rollback_rate
        expr: |
          (
            sum(rate(deployment_status_total{status="rollback"}[1h])) /
            sum(rate(deployment_status_total[1h]))
          ) * 100

  - name: deployment_alerts
    rules:
      - alert: DeploymentTakingTooLong
        expr: deployment:duration_seconds > 600
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "部署时间过长"
          description: "部署已经进行了 {{ $value }} 秒"
          
      - alert: HighDeploymentFailureRate
        expr: deployment:success_rate < 90
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "部署成功率过低"
          description: "过去1小时部署成功率为 {{ $value }}%"
          
      - alert: FrequentRollbacks
        expr: deployment:rollback_rate > 20
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "回滚频率过高"
          description: "过去1小时回滚率为 {{ $value }}%"
```

## 🔄 回滚策略

### 自动回滚脚本

```bash
#!/bin/bash
# scripts/auto-rollback.sh

set -e

ENVIRONMENT=${1:-production}
HEALTH_CHECK_URL=${2:-"http://localhost:8080/actuator/health"}
MAX_UNHEALTHY_DURATION=${3:-300} # 5分钟

echo "🔍 开始监控部署健康状态..."
echo "🌐 健康检查URL: $HEALTH_CHECK_URL"
echo "⏰ 最大不健康持续时间: ${MAX_UNHEALTHY_DURATION}秒"

# 获取当前部署版本
get_current_version() {
    curl -s "$HEALTH_CHECK_URL" | jq -r '.details.version // "unknown"'
}

# 获取上一个版本
get_previous_version() {
    # 从部署历史记录中获取上一个版本
    tail -2 /opt/assessment/deployment-history.log | head -1 | cut -d' ' -f2
}

# 健康检查
health_check() {
    local response=$(curl -s -w "%{http_code}" "$HEALTH_CHECK_URL" -o /dev/null)
    [ "$response" = "200" ]
}

# 执行回滚
perform_rollback() {
    local previous_version=$(get_previous_version)
    
    if [ -z "$previous_version" ] || [ "$previous_version" = "unknown" ]; then
        echo "❌ 无法获取上一个版本信息"
        return 1
    fi
    
    echo "🔙 执行自动回滚到版本: $previous_version"
    
    # 记录回滚事件
    echo "$(date): ROLLBACK $previous_version (auto)" >> /opt/assessment/deployment-history.log
    
    # 执行回滚部署
    export APP_VERSION=$previous_version
    export ENVIRONMENT=$ENVIRONMENT
    
    case $ENVIRONMENT in
        "production")
            ./blue-green-deploy.sh production $previous_version
            ;;
        "staging")
            docker-compose -f docker-compose.deploy.yml up -d
            ;;
        *)
            echo "❌ 不支持的环境: $ENVIRONMENT"
            return 1
            ;;
    esac
    
    echo "✅ 自动回滚完成"
}

# 发送告警通知
send_alert() {
    local message=$1
    local severity=${2:-"warning"}
    
    # Slack通知
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"[$severity] $message\"}" \
        "$SLACK_WEBHOOK_URL"
    
    # 邮件通知
    echo "$message" | mail -s "[$severity] Deployment Alert" <EMAIL>
}

# 主监控循环
monitor_deployment() {
    local unhealthy_start_time=""
    local current_version=$(get_current_version)
    
    echo "📊 当前版本: $current_version"
    
    while true; do
        if health_check; then
            if [ -n "$unhealthy_start_time" ]; then
                echo "✅ 服务恢复健康"
                send_alert "服务已恢复健康 - 版本: $current_version" "info"
                unhealthy_start_time=""
            fi
        else
            if [ -z "$unhealthy_start_time" ]; then
                unhealthy_start_time=$(date +%s)
                echo "⚠️ 检测到服务不健康"
                send_alert "检测到服务不健康 - 版本: $current_version" "warning"
            else
                local unhealthy_duration=$(($(date +%s) - unhealthy_start_time))
                echo "❌ 服务不健康持续时间: ${unhealthy_duration}秒"
                
                if [ $unhealthy_duration -ge $MAX_UNHEALTHY_DURATION ]; then
                    echo "🚨 触发自动回滚条件"
                    send_alert "服务不健康超过${MAX_UNHEALTHY_DURATION}秒，触发自动回滚" "critical"
                    
                    if perform_rollback; then
                        send_alert "自动回滚执行成功" "info"
                        break
                    else
                        send_alert "自动回滚执行失败，需要人工介入" "critical"
                        exit 1
                    fi
                fi
            fi
        fi
        
        sleep 30
    done
}

# 执行监控
monitor_deployment
```

## 📝 部署文档化

### 部署清单模板

```markdown
# 部署清单 - v${VERSION}

## 📋 部署前检查

- [ ] 代码已合并到main分支
- [ ] 所有CI检查通过
- [ ] 已创建Release Tag
- [ ] 数据库迁移脚本已准备
- [ ] 配置文件已更新
- [ ] 回滚计划已制定

## 🚀 部署步骤

### 1. 准备阶段
- [ ] 通知相关团队即将部署
- [ ] 确认生产环境状态正常
- [ ] 备份当前数据库
- [ ] 验证镜像可用性

### 2. 部署执行
- [ ] 执行数据库迁移
- [ ] 部署新版本应用
- [ ] 验证服务启动正常
- [ ] 执行烟雾测试

### 3. 部署验证
- [ ] 功能测试验证
- [ ] 性能指标检查
- [ ] 监控告警确认
- [ ] 用户反馈收集

### 4. 后续操作
- [ ] 更新部署文档
- [ ] 清理旧版本资源
- [ ] 团队部署通知
- [ ] 部署总结记录

## 🔄 回滚计划

**触发条件:**
- 健康检查失败超过5分钟
- 错误率超过5%
- 关键功能不可用

**回滚步骤:**
1. 执行自动回滚脚本
2. 验证回滚成功
3. 通知相关团队
4. 分析问题原因

## 📞 联系信息

- **部署负责人:** ${DEPLOYER_NAME}
- **技术负责人:** ${TECH_LEAD}
- **紧急联系:** ${EMERGENCY_CONTACT}

## 📊 部署结果

- **开始时间:** ${START_TIME}
- **结束时间:** ${END_TIME}
- **部署状态:** ${STATUS}
- **回滚次数:** ${ROLLBACK_COUNT}
```

## 📞 相关资源

- [CI/CD总览](./index.md) - 持续集成部署概述
- [GitHub Actions](./github-actions.md) - CI/CD流程配置
- [自动化测试](./automated-testing.md) - 测试自动化指南
- [Docker部署](../docker-deployment.md) - 容器化部署
- [监控配置](../monitoring.md) - 部署监控设置

---

*最后更新：2025-07-01*