# 📊 监控配置指南

## 🎯 监控概述

智慧养老评估平台采用全方位监控体系，覆盖应用性能、基础设施、业务指标和用户体验等多个维度，确保系统稳定运行和及时发现问题。

## 🏗️ 监控架构

### 监控架构图

```
┌─────────────────────────────────────────────────────────┐
│                   监控数据展示层                          │
│  Grafana Dashboard  │  Kibana Logs  │  Alert Manager    │
├─────────────────────────────────────────────────────────┤
│                   监控数据处理层                          │
│  Prometheus        │  Elasticsearch │  Logstash        │
├─────────────────────────────────────────────────────────┤
│                   监控数据收集层                          │
│  Node Exporter    │  Redis Exporter │  Postgres Exporter│
│  JVM Metrics      │  Custom Metrics │  Nginx Logs      │
├─────────────────────────────────────────────────────────┤
│                   应用程序层                             │
│  Spring Boot      │  Vue.js Frontend │  PostgreSQL     │
│  Redis Cache      │  Nginx Proxy     │  LM Studio      │
└─────────────────────────────────────────────────────────┘
```

### 监控指标体系

**基础设施监控:**
- CPU使用率、内存使用率、磁盘空间
- 网络流量、连接数、响应时间
- 容器资源使用情况

**应用性能监控:**
- JVM堆内存、垃圾回收、线程池
- HTTP请求量、响应时间、错误率
- 数据库连接池、SQL查询性能
- Redis缓存命中率、操作延迟

**业务指标监控:**
- 用户登录次数、评估完成数量
- API调用量、功能使用统计
- 文件上传成功率、AI评估准确率

## 🔧 Prometheus配置

### Prometheus主配置

```yaml
# prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'assessment-platform'
    environment: 'production'

rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Spring Boot应用监控
  - job_name: 'assessment-backend'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s
    scrape_timeout: 5s

  # PostgreSQL数据库监控
  - job_name: 'assessment-postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 10s

  # Redis缓存监控
  - job_name: 'assessment-redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 10s

  # Nginx代理监控
  - job_name: 'assessment-nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s

  # 系统资源监控
  - job_name: 'assessment-node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # LM Studio AI服务监控
  - job_name: 'assessment-lm-studio'
    static_configs:
      - targets: ['lm-studio:1234']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 前端性能监控(通过自定义指标)
  - job_name: 'assessment-frontend'
    static_configs:
      - targets: ['frontend-metrics:9091']
    scrape_interval: 30s
```

### Docker Compose监控服务

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  # Prometheus时序数据库
  prometheus:
    image: prom/prometheus:v2.40.0
    container_name: assessment-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    networks:
      - assessment-network
    restart: unless-stopped

  # Grafana可视化面板
  grafana:
    image: grafana/grafana:9.3.0
    container_name: assessment-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SECURITY_ALLOW_EMBEDDING=true
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - assessment-network
    restart: unless-stopped

  # AlertManager告警管理
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: assessment-alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager:/etc/alertmanager
      - alertmanager_data:/alertmanager
    networks:
      - assessment-network
    restart: unless-stopped

  # Node Exporter系统监控
  node-exporter:
    image: prom/node-exporter:v1.5.0
    container_name: assessment-node-exporter
    command:
      - '--path.rootfs=/host'
    ports:
      - "9100:9100"
    volumes:
      - /:/host:ro,rslave
    networks:
      - assessment-network
    restart: unless-stopped

  # PostgreSQL Exporter
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.12.0
    container_name: assessment-postgres-exporter
    environment:
      DATA_SOURCE_NAME: "postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}?sslmode=disable"
    ports:
      - "9187:9187"
    depends_on:
      - postgres
    networks:
      - assessment-network
    restart: unless-stopped

  # Redis Exporter
  redis-exporter:
    image: oliver006/redis_exporter:v1.45.0
    container_name: assessment-redis-exporter
    environment:
      REDIS_ADDR: "redis://redis:6379"
      REDIS_PASSWORD: "${REDIS_PASSWORD}"
    ports:
      - "9121:9121"
    depends_on:
      - redis
    networks:
      - assessment-network
    restart: unless-stopped

  # Nginx Exporter
  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:0.10.0
    container_name: assessment-nginx-exporter
    command:
      - -nginx.scrape-uri=http://nginx:8080/stub_status
    ports:
      - "9113:9113"
    depends_on:
      - nginx
    networks:
      - assessment-network
    restart: unless-stopped

volumes:
  prometheus_data:
  grafana_data:
  alertmanager_data:

networks:
  assessment-network:
    external: true
```

## 📊 Grafana仪表板

### 系统资源监控仪表板

```json
{
  "dashboard": {
    "id": null,
    "title": "智慧养老评估平台 - 系统监控",
    "tags": ["assessment", "system"],
    "timezone": "browser",
    "panels": [
      {
        "title": "CPU使用率",
        "type": "stat",
        "targets": [
          {
            "expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU使用率"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                { "color": "green", "value": 0 },
                { "color": "yellow", "value": 70 },
                { "color": "red", "value": 90 }
              ]
            }
          }
        }
      },
      {
        "title": "内存使用率",
        "type": "stat",
        "targets": [
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "内存使用率"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                { "color": "green", "value": 0 },
                { "color": "yellow", "value": 80 },
                { "color": "red", "value": 95 }
              ]
            }
          }
        }
      },
      {
        "title": "磁盘使用率",
        "type": "stat",
        "targets": [
          {
            "expr": "(1 - (node_filesystem_avail_bytes{mountpoint=\"/\"} / node_filesystem_size_bytes{mountpoint=\"/\"})) * 100",
            "legendFormat": "磁盘使用率"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                { "color": "green", "value": 0 },
                { "color": "yellow", "value": 80 },
                { "color": "red", "value": 95 }
              ]
            }
          }
        }
      },
      {
        "title": "网络流量",
        "type": "graph",
        "targets": [
          {
            "expr": "irate(node_network_receive_bytes_total{device!=\"lo\"}[5m])",
            "legendFormat": "接收 - {{device}}"
          },
          {
            "expr": "irate(node_network_transmit_bytes_total{device!=\"lo\"}[5m])",
            "legendFormat": "发送 - {{device}}"
          }
        ],
        "yAxes": [
          {
            "unit": "binBps",
            "min": 0
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

### 应用性能监控仪表板

```json
{
  "dashboard": {
    "title": "智慧养老评估平台 - 应用性能",
    "panels": [
      {
        "title": "HTTP请求量",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_server_requests_seconds_count[5m])",
            "legendFormat": "{{method}} {{uri}}"
          }
        ]
      },
      {
        "title": "HTTP响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_server_requests_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "JVM堆内存使用",
        "type": "graph",
        "targets": [
          {
            "expr": "jvm_memory_used_bytes{area=\"heap\"}",
            "legendFormat": "已使用堆内存"
          },
          {
            "expr": "jvm_memory_max_bytes{area=\"heap\"}",
            "legendFormat": "最大堆内存"
          }
        ]
      },
      {
        "title": "数据库连接池",
        "type": "graph",
        "targets": [
          {
            "expr": "hikaricp_connections_active",
            "legendFormat": "活跃连接"
          },
          {
            "expr": "hikaricp_connections_idle",
            "legendFormat": "空闲连接"
          },
          {
            "expr": "hikaricp_connections_max",
            "legendFormat": "最大连接"
          }
        ]
      },
      {
        "title": "Redis缓存性能",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(redis_commands_processed_total[5m])",
            "legendFormat": "命令执行率"
          },
          {
            "expr": "redis_connected_clients",
            "legendFormat": "连接客户端数"
          }
        ]
      }
    ]
  }
}
```

## 🚨 告警配置

### Prometheus告警规则

```yaml
# prometheus/alert_rules.yml
groups:
  - name: assessment_platform_alerts
    rules:
      # 系统资源告警
      - alert: HighCPUUsage
        expr: 100 - (avg(irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率已超过90%，当前值为 {{ $value }}%"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 95
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率已超过95%，当前值为 {{ $value }}%"

      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"})) * 100 > 90
        for: 10m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘使用率已超过90%，当前值为 {{ $value }}%"

      # 应用服务告警
      - alert: ApplicationDown
        expr: up{job="assessment-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "后端服务不可用"
          description: "后端服务已停止响应超过1分钟"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "响应时间过长"
          description: "95%的请求响应时间超过2秒，当前值为 {{ $value }}秒"

      - alert: HighErrorRate
        expr: rate(http_server_requests_seconds_count{status=~"5.."}[5m]) / rate(http_server_requests_seconds_count[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "错误率过高"
          description: "HTTP 5xx错误率超过5%，当前值为 {{ $value }}%"

      # 数据库告警
      - alert: DatabaseDown
        expr: up{job="assessment-postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "数据库服务不可用"
          description: "PostgreSQL数据库已停止响应超过1分钟"

      - alert: DatabaseConnectionPoolHigh
        expr: hikaricp_connections_active / hikaricp_connections_max > 0.8
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "数据库连接池使用率过高"
          description: "数据库连接池使用率超过80%，当前值为 {{ $value }}%"

      - alert: SlowQuery
        expr: pg_stat_activity_max_tx_duration{state="active"} > 300
        for: 2m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "数据库慢查询"
          description: "存在执行时间超过5分钟的SQL查询"

      # Redis告警
      - alert: RedisDown
        expr: up{job="assessment-redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis服务不可用"
          description: "Redis缓存服务已停止响应超过1分钟"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过90%，当前值为 {{ $value }}%"

      # JVM告警
      - alert: JVMMemoryHigh
        expr: jvm_memory_used_bytes{area="heap"} / jvm_memory_max_bytes{area="heap"} > 0.9
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "JVM堆内存使用率过高"
          description: "JVM堆内存使用率超过90%，当前值为 {{ $value }}%"

      - alert: GCTimeHigh
        expr: increase(jvm_gc_pause_seconds_sum[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "垃圾回收时间过长"
          description: "5分钟内垃圾回收总时间超过10秒"
```

### AlertManager配置

```yaml
# alertmanager/alertmanager.yml
global:
  smtp_smarthost: 'smtp.qq.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your_email_password'

route:
  group_by: ['alertname', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://assessment-webhook:8080/alerts'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] 智慧养老评估平台告警'
        body: |
          告警级别: {{ .GroupLabels.severity }}
          告警服务: {{ .GroupLabels.service }}
          告警详情:
          {{ range .Alerts }}
          - {{ .Annotations.summary }}
            {{ .Annotations.description }}
          {{ end }}
    webhook_configs:
      - url: 'http://assessment-webhook:8080/alerts/critical'
    wechat_configs:
      - api_url: 'https://qyapi.weixin.qq.com/cgi-bin/'
        corp_id: 'your_corp_id'
        agent_id: 'your_agent_id'
        api_secret: 'your_api_secret'
        to_user: '@all'
        message: |
          【严重告警】智慧养老评估平台
          服务: {{ .GroupLabels.service }}
          {{ range .Alerts }}
          {{ .Annotations.summary }}
          {{ end }}

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] 智慧养老评估平台告警'
        body: |
          告警级别: {{ .GroupLabels.severity }}
          告警服务: {{ .GroupLabels.service }}
          {{ range .Alerts }}
          - {{ .Annotations.summary }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service']
```

## 📊 日志监控

### ELK Stack配置

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    container_name: assessment-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - assessment-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    container_name: assessment-logstash
    volumes:
      - ./logstash/config:/usr/share/logstash/pipeline
      - ./logstash/patterns:/usr/share/logstash/patterns
      - backend_logs:/var/log/backend:ro
      - nginx_logs:/var/log/nginx:ro
    environment:
      - "LS_JAVA_OPTS=-Xmx512m -Xms512m"
    depends_on:
      - elasticsearch
    networks:
      - assessment-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    container_name: assessment-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - assessment-network

volumes:
  elasticsearch_data:
```

### Logstash配置

```ruby
# logstash/config/logstash.conf
input {
  # 后端应用日志
  file {
    path => "/var/log/backend/*.log"
    start_position => "beginning"
    codec => multiline {
      pattern => "^\d{4}-\d{2}-\d{2}"
      negate => true
      what => "previous"
    }
    tags => ["backend", "application"]
  }

  # Nginx访问日志
  file {
    path => "/var/log/nginx/access.log"
    start_position => "beginning"
    tags => ["nginx", "access"]
  }

  # Nginx错误日志
  file {
    path => "/var/log/nginx/error.log"
    start_position => "beginning"
    tags => ["nginx", "error"]
  }
}

filter {
  # 处理后端应用日志
  if "backend" in [tags] {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{DATA:thread} %{DATA:logger} - %{GREEDYDATA:message}"
      }
    }
    
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS" ]
    }
    
    if [level] == "ERROR" or [level] == "WARN" {
      mutate {
        add_tag => ["alert"]
      }
    }
  }

  # 处理Nginx访问日志
  if "nginx" in [tags] and "access" in [tags] {
    grok {
      match => { 
        "message" => '%{IPORHOST:remote_addr} - %{DATA:remote_user} \[%{HTTPDATE:time_local}\] "%{WORD:method} %{DATA:request} HTTP/%{NUMBER:http_version}" %{INT:status} %{INT:body_bytes_sent} "%{DATA:http_referer}" "%{DATA:http_user_agent}"'
      }
    }
    
    date {
      match => [ "time_local", "dd/MMM/yyyy:HH:mm:ss Z" ]
    }
    
    mutate {
      convert => {
        "status" => "integer"
        "body_bytes_sent" => "integer"
      }
    }
    
    if [status] >= 400 {
      mutate {
        add_tag => ["error"]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "assessment-logs-%{+YYYY.MM.dd}"
  }
  
  # 错误日志输出到告警
  if "alert" in [tags] or "error" in [tags] {
    http {
      url => "http://assessment-webhook:8080/logs/alert"
      http_method => "post"
      format => "json"
    }
  }
}
```

## 📱 自定义监控指标

### Spring Boot自定义指标

```java
@Component
public class CustomMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter userLoginCounter;
    private final Counter assessmentCompletedCounter;
    private final Timer assessmentProcessingTimer;
    private final Gauge activeUsersGauge;
    
    public CustomMetrics(final MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // 用户登录计数器
        this.userLoginCounter = Counter.builder("user_login_total")
            .description("用户登录总次数")
            .tag("application", "assessment")
            .register(meterRegistry);
            
        // 评估完成计数器
        this.assessmentCompletedCounter = Counter.builder("assessment_completed_total")
            .description("评估完成总次数")
            .tag("application", "assessment")
            .register(meterRegistry);
            
        // 评估处理时间计时器
        this.assessmentProcessingTimer = Timer.builder("assessment_processing_duration")
            .description("评估处理耗时")
            .tag("application", "assessment")
            .register(meterRegistry);
            
        // 活跃用户数量计量器
        this.activeUsersGauge = Gauge.builder("active_users_count")
            .description("当前活跃用户数量")
            .tag("application", "assessment")
            .register(meterRegistry, this, CustomMetrics::getActiveUsersCount);
    }
    
    public void recordUserLogin(final String userType) {
        userLoginCounter.increment(
            Tags.of("user_type", userType)
        );
    }
    
    public void recordAssessmentCompleted(final String assessmentType, final boolean success) {
        assessmentCompletedCounter.increment(
            Tags.of(
                "assessment_type", assessmentType,
                "success", String.valueOf(success)
            )
        );
    }
    
    public Timer.Sample startAssessmentProcessingTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void recordAssessmentProcessingTime(final Timer.Sample sample, final String assessmentType) {
        sample.stop(Timer.builder("assessment_processing_duration")
            .tag("assessment_type", assessmentType)
            .register(meterRegistry));
    }
    
    private double getActiveUsersCount() {
        // 从Redis或数据库获取活跃用户数量
        return sessionService.getActiveUserCount();
    }
}
```

### 前端性能监控

```typescript
// frontend/src/utils/metrics.ts
interface PerformanceMetrics {
  pageLoadTime: number
  apiResponseTime: number
  errorCount: number
  userAction: string
}

class MetricsCollector {
  private baseUrl = '/api/metrics'
  
  // 页面加载时间监控
  measurePageLoad() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const loadTime = navigation.loadEventEnd - navigation.fetchStart
      
      this.sendMetric({
        name: 'page_load_time',
        value: loadTime,
        tags: {
          page: window.location.pathname,
          browser: navigator.userAgent
        }
      })
    })
  }
  
  // API响应时间监控
  measureApiCall(url: string, startTime: number, endTime: number, status: number) {
    const responseTime = endTime - startTime
    
    this.sendMetric({
      name: 'api_response_time',
      value: responseTime,
      tags: {
        endpoint: url,
        status: status.toString(),
        method: 'GET' // 根据实际请求方法设置
      }
    })
  }
  
  // 错误监控
  recordError(error: Error, context: string) {
    this.sendMetric({
      name: 'frontend_error',
      value: 1,
      tags: {
        error_type: error.name,
        context: context,
        message: error.message.substring(0, 100)
      }
    })
  }
  
  // 用户行为监控
  recordUserAction(action: string, component: string) {
    this.sendMetric({
      name: 'user_action',
      value: 1,
      tags: {
        action: action,
        component: component,
        page: window.location.pathname
      }
    })
  }
  
  private async sendMetric(metric: any) {
    try {
      await fetch(`${this.baseUrl}/custom`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metric)
      })
    } catch (error) {
      console.warn('Failed to send metric:', error)
    }
  }
}

export const metricsCollector = new MetricsCollector()
```

## 📊 监控数据分析

### 性能分析脚本

```python
# scripts/performance_analysis.py
import requests
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

class PerformanceAnalyzer:
    def __init__(self, prometheus_url="http://localhost:9090"):
        self.prometheus_url = prometheus_url
    
    def query_prometheus(self, query, start_time, end_time):
        """查询Prometheus数据"""
        params = {
            'query': query,
            'start': start_time.isoformat(),
            'end': end_time.isoformat(),
            'step': '1m'
        }
        
        response = requests.get(f"{self.prometheus_url}/api/v1/query_range", params=params)
        return response.json()
    
    def analyze_response_time_trend(self, days=7):
        """分析响应时间趋势"""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        query = 'histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m]))'
        data = self.query_prometheus(query, start_time, end_time)
        
        if data['status'] == 'success':
            # 处理数据并生成图表
            self.plot_metric_trend(data, "API响应时间趋势(95th percentile)", "响应时间(秒)")
    
    def analyze_error_rate(self, days=7):
        """分析错误率趋势"""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        query = '''
        rate(http_server_requests_seconds_count{status=~"5.."}[5m]) / 
        rate(http_server_requests_seconds_count[5m]) * 100
        '''
        
        data = self.query_prometheus(query, start_time, end_time)
        
        if data['status'] == 'success':
            self.plot_metric_trend(data, "错误率趋势", "错误率(%)")
    
    def generate_daily_report(self):
        """生成日报"""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)
        
        metrics = {
            'avg_response_time': 'avg(rate(http_server_requests_seconds_sum[5m]) / rate(http_server_requests_seconds_count[5m]))',
            'total_requests': 'sum(increase(http_server_requests_seconds_count[24h]))',
            'error_rate': 'sum(rate(http_server_requests_seconds_count{status=~"5.."}[24h])) / sum(rate(http_server_requests_seconds_count[24h])) * 100',
            'cpu_usage': 'avg(100 - (avg(irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100))',
            'memory_usage': 'avg((1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100)'
        }
        
        report = {}
        for metric_name, query in metrics.items():
            data = self.query_prometheus(query, start_time, end_time)
            if data['status'] == 'success' and data['data']['result']:
                report[metric_name] = float(data['data']['result'][0]['value'][1])
        
        return report
    
    def plot_metric_trend(self, data, title, ylabel):
        """绘制指标趋势图"""
        plt.figure(figsize=(12, 6))
        
        for result in data['data']['result']:
            timestamps = [datetime.fromtimestamp(float(t)) for t, _ in result['values']]
            values = [float(v) for _, v in result['values']]
            
            plt.plot(timestamps, values, label=result['metric'].get('instance', 'default'))
        
        plt.title(title)
        plt.xlabel('时间')
        plt.ylabel(ylabel)
        plt.legend()
        plt.grid(True)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

if __name__ == "__main__":
    analyzer = PerformanceAnalyzer()
    
    # 生成日报
    daily_report = analyzer.generate_daily_report()
    print("每日性能报告:")
    for metric, value in daily_report.items():
        print(f"  {metric}: {value:.2f}")
    
    # 分析趋势
    analyzer.analyze_response_time_trend()
    analyzer.analyze_error_rate()
```

## 📞 相关资源

- [运维总览](./index.md) - 运维部署概述
- [Docker部署](./docker-deployment.md) - 容器化部署指南
- [故障排查](./troubleshooting.md) - 故障诊断指南
- [CI/CD流程](./ci-cd/) - 持续集成部署

---

*最后更新：2025-07-01*