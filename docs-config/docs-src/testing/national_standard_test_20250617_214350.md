# 国标文档数据库设计测试结果

**测试时间**: 2025-06-17 21:43:50
**LM Studio地址**: http://192.168.1.231:1234
**模型**: deepseek-r1-0528-qwen3-8b-mlx@8bit
**处理时间**: 285.4秒
**测试文档**: 老年人能力评估报告（GB/T42195-2022）
**文档长度**: 21248 字符
**响应长度**: 6689 字符

---

## AI生成结果

<think>
嗯，看来用户需要我分析一份老年人能力评估系统的文档，并据此设计PostgreSQL数据库结构。这个系统看起来相当专业，是用于老年人能力评估的，并且遵循国家标准。

首先需要确定文档类型：这是一个评估量表系统，附录B包含详细的评分表格。设计时需要考虑其结构化特性。

文档中包含了多个附录，主要涵盖基本信息、健康问题和能力评估等内容。根据设计要求，我需要识别出关键实体。

从附录A中可以看出基本信息表、疾病诊断与用药情况等内容。这些都会成为数据库中的独立实体或属性。

关于数据建模方式，这种评估量表系统需要处理评估结果、患者信息和能力等级等核心数据。应该采用标准的数据库规范化设计，但也要考虑性能优化。

根据文档内容，我需要识别出几个主要实体：被评估者、评估基本信息、能力维度评分等。每个部分都需要对应到数据库表结构中。

对于字段设计，需要将评估编号、基本信息中的姓名、性别等都转化为数据库表的列。同时要考虑自增长ID和时间戳字段。

数据类型选择上，大多数是文本或数字。要注意数值型的应使用integer而非numeric，字符串要使用varchar。

约束条件设计很重要，比如某些字段不能为空。外键关系也需要考虑，例如评估基本信息表和能力维度评分表之间的关联。

索引设计方面，经常查询的字段如评估编号、创建时间等需要考虑。复合索引可能对日期范围查询有帮助。

还需要设计一些表来记录评估结果和变更信息，如能力等级变更依据等。这能满足实际业务需求。

最后需要确保所有标识符都采用snake_case命名，并在必要时添加注释说明字段用途。

总的来说，这是一个医疗健康领域的专业系统设计，在考虑完整性的同时也要兼顾性能优化。
</think>
```markdown
### 第一部分：文档分析

**文档类型识别**
- **评估量表系统**  
  文档包含详细的评分细则和多级评分标准（如自理能力、基础运动能力等），属于医疗健康领域的专业评估量表。
- **数据建模方式**  
  设计多张关联表：`评估基本信息表`、`能力维度评分表`等，支持嵌套评分结构和多级指标体系。
- **业务背景**  
  国家标准（GB/T42195-2022）下的老年人能力评估系统，用于医疗机构或养老机构对老年人生活能力、健康状态等进行综合评定。

**关键实体识别**
- `被评估者`：基本信息、身体指标、健康史
- `评估记录表`：评估编号、评估日期、评估原因等元数据
- `疾病诊断与用药情况`：多选结构，需支持枚举关系存储
- `能力维度评分表`：自理能力、基础运动能力等5大类评分项
- `评估结果表`：总分与等级（能完好、轻度失能等）

---

### 第二部分：PostgreSQL数据库设计

#### **表结构设计**

```sql
-- 1. 基本信息表
CREATE TABLE elderly_info (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT NOT NULL UNIQUE, -- 评估编号 (如：E20231001001)
    full_name TEXT NOT NULL,
    gender TEXT CHECK (gender IN ('男', '女')),
    date_of_birth DATE NOT NULL,
    height NUMERIC(5,2),          -- 单位：cm
    weight NUMERIC(7,2),          -- 单位：kg
    ethnicity TEXT NOT NULL CHECK (ethnicity IN ('汉族', '少数民族')),
    religion TEXT,               -- 可为空
    id_card_number VARCHAR(18) UNIQUE,
    education_level TEXT CHECK (education_level IN ('文盲', '小学', '初中', '高中技校中专及以上', '大学专科及以上', '不详')), -- 参考表A.2.9
    living_status TEXT[] CHECK (array_length(living_status,1) <= 8), -- 多选：参考表A.2.10
    marital_status TEXT CHECK (marital_status IN ('未婚', '已婚', '丧偶', '离婚', '未说明')),
    medical_fund TEXT[] CHECK (array_length(medical_fund,1) <= 8),    -- 多选：参考表A.2.12
    economic_source TEXT[] CHECK (array_length(economic_source,1) <= 6), -- 多选：参考表A.2.13
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. 护理风险事件表
CREATE TABLE care_risk_events (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT REFERENCES elderly_info(record_id) ON DELETE CASCADE NOT VALIDATION RELY,
    fall_count SMALLINT CHECK (fall_count >= 0),
    wandering_count SMALLINT CHECK (wander_count >= 0),   -- 更正拼写错误
    choking_count SMALLINT CHECK (choking_count >= 0),
    self_harm_count SMALLINT CHECK (self_harm_count >= 0), -- 自杀/自伤
    other_risk TEXT,                                   -- 其他风险事件描述
    recorded_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. 疾病诊断表
CREATE TABLE disease_diagnosis (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT REFERENCES elderly_info(record_id) ON DELETE CASCADE NOT VALIDATION RELY,
    disease_name TEXT,                                 -- 多选疾病描述
    icd_code TEXT UNIQUE NOT NULL CHECK (icd_code ~ '^\d[0-9A-Z]{3}\d{1,2}$'), -- ICD-10编码格式
    recorded_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. 药物使用情况表
CREATE TABLE medication_use (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT REFERENCES elderly_info(record_id) ON DELETE CASCADE NOT VALIDATION RELY,
    drug_name TEXT NOT NULL,
    dosage_method TEXT NOT NULL,                      -- 服药方式
    dose NUMERIC(7,2),                              -- 剂量 (单位视药品而定)
    frequency TEXT NOT NULL CHECK (frequency ~ '^\d+\/?\d*$'), -- 频率：如 1/日、每2小时等
    recorded_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. 能力维度评分表
CREATE TABLE capability_rating (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT REFERENCES elderly_info(record_id) ON DELETE CASCADE NOT VALIDATION RELY,
    rating_dimension TEXT CHECK (rating_dimension IN ('自理', '基础运动', '精神状态', '感知觉', '社会参与')),
    category_score SMALLINT CHECK (category_score BETWEEN 0 AND 4), -- 分类得分
    total_points SMALLINT CHECK (total_points >= 0),           -- 维度总分
    recorded_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. 能力总评表
CREATE TABLE capability_summary (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT REFERENCES elderly_info(record_id) ON DELETE CASCADE NOT VALIDATION RELY,
    total_score SMALLINT CHECK (total_score BETWEEN 0 AND 100),
    capability_level TEXT CHECK (
        capability_level IN ('能力完好', '轻度受损', '中度受损', '重度受损', '完全丧失')
    ),
    assessed_at TIMESTAMPTZ DEFAULT NOW()
);

-- 7. 评估元数据表
CREATE TABLE evaluation_metadata (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT REFERENCES elderly_info(record_id) ON DELETE CASCADE NOT VALIDATION RELY,
    assessment_date DATE NOT NULL,                     -- 评估日期
    reason TEXT CHECK (reason IN ('初评', '复评', '变更')), -- 评估原因
    info_provider TEXT NOT NULL,                      -- 信息提供者姓名（参考表A.3）
    info_provider_relation TEXT CHECK (
        relation IN ('本人', '配偶', '子女', '其他亲属', '雇佣照顾者', '村居居民委员会工作人员', '其他')
    ),
    contact_name TEXT,
    contact_phone VARCHAR(20),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. 能力等级变更记录
CREATE TABLE capability_level_change (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT REFERENCES elderly_info(record_id) ON DELETE CASCADE NOT VALIDATION RELY,
    change_reason TEXT,                                -- 变更原因描述
    new_level TEXT CHECK (
        capability_level IN ('能力完好', '轻度受损', '中度受损', '重度受损', '完全丧失')
    ),                                                -- 变更后等级
    recorded_at TIMESTAMPTZ DEFAULT NOW()
);

-- 9. 日志表 (可选)
CREATE TABLE audit_log (
    id BIGSERIAL PRIMARY KEY,
    record_id TEXT REFERENCES elderly_info(record_id) ON DELETE CASCADE NOT VALIDATION RELY, -- 可为空
    action TEXT CHECK (action IN ('create', 'update', 'delete')),
    performed_by TEXT NOT NULL,                        -- 操作用户
    executed_at TIMESTAMPTZ DEFAULT NOW(),
    description TEXT NOT NULL
);
```

---

#### **索引设计**
```sql
-- 主键索引（默认自动创建）

-- 常用单列查询索引
CREATE INDEX idx_elderly_record_id ON elderly_info(record_id);
CREATE INDEX idx_capability_summary_record_id ON capability_summary(record_id);

-- 复合索引（适用于范围查询或多条件组合）
CREATE INDEX idx_evaluation_metadata_record_id_date ON evaluation_metadata(record_id, assessment_date);

-- 全文索引（用于问题描述等自由文本）
CREATE INDEX idx_medication_name_fts ON medication_use USING GIN (to_tsvector('chinese', drug_name));
```

---

### 第三部分：功能说明与扩展建议

#### **表间关系**
- `elderly_info` 为核心被评估者信息。
- 其他表通过 `record_id` 关联到主记录。
- 多对多字段（如疾病诊断、医疗基金）用关联表实现。

#### **性能优化**
- 冷热数据分离：高频访问字段（如 `record_id`、`assessment_date`）建立索引。
- 分区表设计：按评估日期对 `evaluation_metadata`、`log_table` 等分区存储。
- 数据归档：定期将年份旧数据迁移至历史库。

#### **扩展建议**
- 接入医疗设备接口，自动同步生命体征数据。
- 支持PDF报告导出及第三方系统API对接（如电子病历系统）。
- 结合AI分析，自动识别异常评分项提供预警。

--- 

此设计严格遵循文档结构，并添加了实际系统开发中必要的扩展字段和关联关系，确保满足未来业务需求。