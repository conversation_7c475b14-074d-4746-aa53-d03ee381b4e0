# dev-start-m4.sh 脚本问题修复报告

## 发现的问题及修复

### 1. Maven Wrapper 问题
**问题**: 脚本清理了 `.mvn/wrapper/maven-wrapper.jar` 文件，导致 Maven Wrapper 无法正常工作
```
错误: 找不到或无法加载主类 org.apache.maven.wrapper.MavenWrapperMain
```

**修复**:
- 注释掉删除 Maven Wrapper jar 文件的代码
- 添加 Maven Wrapper 检查和自动下载逻辑
- 如果 Maven Wrapper 不可用，自动回退到系统 Maven

### 2. grep 命令参数错误
**问题**: 在数据库统计部分使用了错误的 grep 参数 `---`
```
grep: unrecognized option `---'
```

**修复**:
- 将 `grep -v "---"` 修改为 `grep -v "\-\-\-"`
- 使用转义字符避免参数解析错误

### 3. Node.js 残留进程处理
**问题**: 检测到 Node.js 残留进程但没有自动清理

**修复**:
- 在启动前检查阶段自动清理 Node.js 残留进程
- 添加清理确认信息

## 修复后的改进

### 1. 更智能的 Maven 处理
```bash
# 检查Maven Wrapper是否存在
if [ ! -f ".mvn/wrapper/maven-wrapper.jar" ]; then
    echo -e "${BLUE}🔄 下载Maven Wrapper...${NC}"
    if command -v mvn >/dev/null 2>&1; then
        mvn -N io.takari:maven:wrapper
    else
        echo -e "${YELLOW}⚠️ Maven Wrapper不存在且系统未安装Maven，尝试使用系统Maven...${NC}"
    fi
fi

# 智能选择编译工具
if [ -f ".mvn/wrapper/maven-wrapper.jar" ]; then
    ./mvnw clean compile package -DskipTests -U --no-transfer-progress
else
    mvn clean compile package -DskipTests -U --no-transfer-progress
fi
```

### 2. 自动进程清理
```bash
if [ "$NODE_PROCS" -gt 0 ]; then
    echo -e "${YELLOW}   ⚠️ 发现 $NODE_PROCS 个Node.js残留进程，正在清理...${NC}"
    pkill -f "vite" 2>/dev/null || true
    pkill -f "webpack" 2>/dev/null || true
    pkill -f "dev-server" 2>/dev/null || true
    sleep 2
    echo -e "${GREEN}   ✅ Node.js残留进程已清理${NC}"
fi
```

### 3. 更安全的缓存清理策略
- 保留 Maven Wrapper jar 文件，避免重复下载
- 只清理编译产物和项目特定缓存
- 保持工具链的完整性

## 测试建议

1. **重新运行脚本**:
   ```bash
   ./scripts/dev-start-m4.sh
   ```

2. **验证编译过程**:
   - 确认 Maven Wrapper 正常工作
   - 检查编译输出无错误
   - 验证 JAR 文件正确生成

3. **检查服务启动**:
   - 后端服务健康检查通过
   - 前端服务正常响应
   - 所有端口正确监听

## 预期结果

修复后的脚本应该能够:
- ✅ 正常编译后端应用
- ✅ 自动处理 Maven Wrapper 问题
- ✅ 清理残留进程
- ✅ 显示正确的数据库统计信息
- ✅ 完整启动所有服务

## 如果仍有问题

如果遇到其他问题，请检查:
1. Java 环境是否正确配置
2. Maven 是否正确安装
3. 网络连接是否正常（用于下载依赖）
4. 磁盘空间是否充足
5. 查看详细的错误日志: `logs/backend-m4.log`