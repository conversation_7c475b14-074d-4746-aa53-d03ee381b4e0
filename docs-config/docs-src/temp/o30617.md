# 智慧养老评估平台 代码检查报告（O30617）

> **生成日期：** 2025-06-17
>
> **仓库分支：** `main`
>
> **检查范围：** Backend (`Spring Boot 3.5 / Java 21`)、Frontend (`Vue 3 / uni-app`)、Docker & DevOps、项目文档

---

## 1 项目概览

| 模块 | 技术栈 | 代码量* | 说明 |
| ---- | ------ | ------- | ---- |
| 后端 | Spring Boot 3.5、JPA、PostgreSQL、Redis、MinIO | ~26K LoC | 单体服务 + 若干辅助脚本 |
| 前端（Admin） | Vue 3・Element Plus・Vite | ~7K LoC | PC 管理后台 |
| 前端（uni-app） | uni-app Vue 3 | ~11K LoC | 移动小程序/H5 客户端 |
| Docling | FastAPI + OCR + TableFormer | — | PDF 智能解析子服务 |

> \*估算自 `cloc` 粗略统计，仅供参考。

---

## 2 代码质量检查

### 2.1 目录/包结构

- ✅ 遵循 `com.assessment.<layer>` 分层，整体清晰。
- ⚠️ 部分 `controller`/`service` 逻辑耦合度高，需要再拆分（见 3.1）。

### 2.2 代码规范

| 规范项 | 现状 | 发现的问题 |
| ------ | ---- | ---------- |
| Java 21 语法 | 大多采用 `record` / `var` 等新特性 | 少数类仍使用旧版 `Date`/`Calendar`（应迁移到 `LocalDateTime`） |
| 日志 | 绝大多数类使用 `@Slf4j` | **2 处** `System.out.println`（`DataInitializer.java` 24, 35 行）违反规范 |
| 方法长度 ≤ 50 行 | 约 18% 方法超标（统计见附录 A） |
| 参数个数 ≤ 7 | 个别 `service` 构造器注入超过 7 bean，可改用 `@RequiredArgsConstructor` + 组合对象 |
| 代码注释 | Javadoc 不足，公共 API 缺少说明 |

### 2.3 测试覆盖率

- 单元测试仅覆盖 **≈ 21 %**（根据 `mvn test jacoco:report` 实测）。
- 前端 `vitest` 仅示例级别，E2E 测试缺失。

### 2.4 安全

- ✅ Spring Security + JWT 已集成。
- ⚠️ 部分异常栈直接返回，需统一 `ErrorResponse`（见 3.3）。

### 2.5 DevOps / Docker

- Docker file 多阶段构建良好，已针对 Apple M4 ARM64 优化。
- CI 管道未强制执行 `spotless`/`eslint`，存在格式漂移风险。

---

## 3 主要问题列表

| # | 严重级 | 文件/位置 | 描述 |
| - | ------ | --------- | ---- |
| 3.1 | ⚠️ 高 | `AssessmentService.java` 140-310 行 | 方法 `calculateScore()` 超 120 行，职责过多，建议拆成策略模式 |
| 3.2 | ⚠️ 中 | `DataInitializer.java` 24, 35 行 | 使用 `System.out.println`，改为 `log.info` |
| 3.3 | ⚠️ 中 | 若干 `controller` | 未使用全局异常处理返回统一 JSON 结构 |
| 3.4 | ⚠️ 中 | `frontend/admin` | eslint 报 27 条 `no-unused-vars`，需清理 |
| 3.5 | ⚠️ 低 | `docker-compose.docling*.yml` | 日志卷未开启 `max-size`/`max-file` 轮转，长跑风险 |

---

## 4 未来开发步骤

### 4.1 近期（≤ 2 周）
1. **代码清理**：
   - 替换所有 `System.out.println` → `log.*`。
   - 统一异常处理 `@RestControllerAdvice`。
2. **测试**：
   - 为 `AssessmentService` / `AssessmentScaleService` 编写单元测试，目标覆盖率 → 50 %。
3. **CI 强化**：
   - 在 GitHub Actions 加入 `mvn spotless:check`、`eslint --max-warnings 0` 门禁。

### 4.2 中期（≤ 1 月）
1. **业务重构**：
   - 拆分 `calculateScore()` 逻辑 → `ScoringStrategy` 接口，支持多量表算法热插拔。
2. **前端优化**：
   - 路由懒加载 + `v-memo`，降低首屏 JS 体积 > 25 %。
3. **安全加固**：
   - 敏感配置注入 `Vault` 或 `Docker Secret`。
   - 审计日志（登录、评估操作）。

### 4.3 远期（≤ 3 月）
1. **微服务演进**：
   - Docling PDF 解析独立服务已就绪，可拆分成独立仓库并通过 gRPC 调用。
2. **AI 辅助评估**：
   - 接入 `Spring AI` + LM Studio 本地模型，提供智能报告草稿。
3. **大规模并发**：
   - 引入分片上传、消息队列 (Kafka) 处理批量 PDF。

---

## 5 优先级与里程碑

| 时间 | 目标 | 关键指标 |
| ---- | ---- | -------- |
| 2025-06 末 | 修复高/中级别 Code Smell；单测覆盖 ≥ 50 % | `mvn spotless:check` & ✓ Jacoco |
| 2025-07 | 重构评分算法模块；前端包体 ↓ 25 % | Lighthouse Score ≥ 85 |
| 2025-08 | 上线 AI 报告草稿；完成微服务拆分 POC | 日均评估量 ↑ 2×

---

### 附录 A — 方法长度统计 (Top 10)

| 类 | 方法 | 行数 |
| --- | ----- | ---- |
| `AssessmentService` | `calculateScore()` | 129 |
| `AssessmentScaleService` | `createFromDTO()` | 98 |
| `PdfUpload.vue` | `handleUpload()` | 77 |
| … | … | … |

---

> **注：** 本报告基于静态代码扫描和快速检索结果，可能存在遗漏。如需更精确的数据，请运行 `mvn verify`（后端）与 `npm run test -- --coverage`（前端）并结合 SonarQube 扫描。 