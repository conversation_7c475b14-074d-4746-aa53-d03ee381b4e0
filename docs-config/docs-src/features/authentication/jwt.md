# 🔐 JWT Token管理

## 🎯 JWT概述

智慧养老评估平台采用JWT (JSON Web Token) 作为认证和授权的核心机制，提供无状态、安全、高效的令牌管理方案。

## 🏗️ JWT架构设计

### Token类型
- **Access Token**: 短期访问令牌（2小时）
- **Refresh Token**: 长期刷新令牌（7天）
- **ID Token**: 用户身份令牌（包含用户信息）

### JWT结构
```json
{
  "header": {
    "alg": "HS512",
    "typ": "JWT"
  },
  "payload": {
    "sub": "user123",
    "tenant": "hospital_001",
    "roles": ["ADMIN", "ASSESSOR"],
    "permissions": ["USER:READ", "ASSESSMENT:CREATE"],
    "iat": **********,
    "exp": **********,
    "iss": "assessment-platform",
    "aud": "assessment-client"
  },
  "signature": "HMACSHA512(base64UrlEncode(header) + '.' + base64UrlEncode(payload), secret)"
}
```

## 🔧 后端实现

### JWT工具类
```java
@Component
@Slf4j
public class JwtTokenProvider {
    
    @Value("${jwt.secret}")
    private String jwtSecret;
    
    @Value("${jwt.access-token-validity:7200}") // 2小时
    private int accessTokenValidity;
    
    @Value("${jwt.refresh-token-validity:604800}") // 7天
    private int refreshTokenValidity;
    
    @Value("${jwt.issuer:assessment-platform}")
    private String issuer;
    
    /**
     * 生成访问令牌
     */
    public String generateAccessToken(final UserDetails userDetails, final String tenantId) {
        final Map<String, Object> claims = new HashMap<>();
        claims.put("tenant", tenantId);
        claims.put("roles", extractRoles(userDetails));
        claims.put("permissions", extractPermissions(userDetails));
        
        return generateToken(userDetails.getUsername(), claims, accessTokenValidity);
    }
    
    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(final String username, final String tenantId) {
        final Map<String, Object> claims = new HashMap<>();
        claims.put("tenant", tenantId);
        claims.put("type", "refresh");
        
        return generateToken(username, claims, refreshTokenValidity);
    }
    
    /**
     * 生成令牌通用方法
     */
    private String generateToken(final String subject, final Map<String, Object> claims, final int validity) {
        final Date now = new Date();
        final Date expiryDate = new Date(now.getTime() + validity * 1000L);
        
        return Jwts.builder()
            .setClaims(claims)
            .setSubject(subject)
            .setIssuedAt(now)
            .setExpiration(expiryDate)
            .setIssuer(issuer)
            .setAudience("assessment-client")
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
    }
    
    /**
     * 验证令牌
     */
    public boolean validateToken(final String token) {
        try {
            final Claims claims = getClaims(token);
            return !isTokenExpired(claims);
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("JWT验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取令牌声明
     */
    public Claims getClaims(final String token) {
        return Jwts.parser()
            .setSigningKey(jwtSecret)
            .parseClaimsJws(token)
            .getBody();
    }
    
    /**
     * 从令牌获取用户名
     */
    public String getUsernameFromToken(final String token) {
        return getClaims(token).getSubject();
    }
    
    /**
     * 从令牌获取租户ID
     */
    public String getTenantFromToken(final String token) {
        return getClaims(token).get("tenant", String.class);
    }
    
    /**
     * 从令牌获取角色
     */
    @SuppressWarnings("unchecked")
    public List<String> getRolesFromToken(final String token) {
        return getClaims(token).get("roles", List.class);
    }
    
    /**
     * 从令牌获取权限
     */
    @SuppressWarnings("unchecked")
    public List<String> getPermissionsFromToken(final String token) {
        return getClaims(token).get("permissions", List.class);
    }
    
    /**
     * 检查令牌是否过期
     */
    private boolean isTokenExpired(final Claims claims) {
        return claims.getExpiration().before(new Date());
    }
    
    /**
     * 提取用户角色
     */
    private List<String> extractRoles(final UserDetails userDetails) {
        return userDetails.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .filter(auth -> auth.startsWith("ROLE_"))
            .map(role -> role.substring(5)) // 移除 "ROLE_" 前缀
            .collect(Collectors.toList());
    }
    
    /**
     * 提取用户权限
     */
    private List<String> extractPermissions(final UserDetails userDetails) {
        return userDetails.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .filter(auth -> !auth.startsWith("ROLE_"))
            .collect(Collectors.toList());
    }
}
```

### JWT认证过滤器
```java
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    @Autowired
    private JwtTokenProvider jwtTokenProvider;
    
    @Autowired
    private UserDetailsService userDetailsService;
    
    @Override
    protected void doFilterInternal(final HttpServletRequest request,
                                  final HttpServletResponse response,
                                  final FilterChain filterChain) throws ServletException, IOException {
        
        final String token = extractToken(request);
        
        if (token != null && jwtTokenProvider.validateToken(token)) {
            final String username = jwtTokenProvider.getUsernameFromToken(token);
            final String tenantId = jwtTokenProvider.getTenantFromToken(token);
            
            // 设置租户上下文
            TenantContext.setCurrentTenant(tenantId);
            
            // 设置认证信息
            final UserDetails userDetails = userDetailsService.loadUserByUsername(username);
            final Authentication authentication = new JwtAuthenticationToken(
                userDetails, 
                token, 
                userDetails.getAuthorities()
            );
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }
        
        try {
            filterChain.doFilter(request, response);
        } finally {
            // 清理租户上下文
            TenantContext.clear();
        }
    }
    
    /**
     * 从请求中提取令牌
     */
    private String extractToken(final HttpServletRequest request) {
        final String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
```

### JWT认证Token
```java
public class JwtAuthenticationToken extends AbstractAuthenticationToken {
    
    private final Object principal;
    private final String token;
    
    public JwtAuthenticationToken(final Object principal, 
                                 final String token,
                                 final Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.token = token;
        setAuthenticated(true);
    }
    
    @Override
    public Object getCredentials() {
        return token;
    }
    
    @Override
    public Object getPrincipal() {
        return principal;
    }
}
```

## 🎨 前端实现

### Token管理Service
```typescript
// utils/tokenManager.ts
export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token'
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token'
  private static readonly TOKEN_EXPIRY_KEY = 'token_expiry'
  
  // 保存令牌
  static saveTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken)
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken)
    
    // 解析过期时间
    const payload = this.parseJwtPayload(accessToken)
    if (payload.exp) {
      localStorage.setItem(this.TOKEN_EXPIRY_KEY, payload.exp.toString())
    }
  }
  
  // 获取访问令牌
  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY)
  }
  
  // 获取刷新令牌
  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY)
  }
  
  // 检查令牌是否有效
  static isTokenValid(): boolean {
    const token = this.getAccessToken()
    if (!token) return false
    
    const expiryTime = localStorage.getItem(this.TOKEN_EXPIRY_KEY)
    if (!expiryTime) return false
    
    const now = Math.floor(Date.now() / 1000)
    return parseInt(expiryTime) > now
  }
  
  // 检查令牌是否即将过期（5分钟内）
  static isTokenExpiringSoon(): boolean {
    const expiryTime = localStorage.getItem(this.TOKEN_EXPIRY_KEY)
    if (!expiryTime) return true
    
    const now = Math.floor(Date.now() / 1000)
    const expiry = parseInt(expiryTime)
    return (expiry - now) < 300 // 5分钟
  }
  
  // 解析JWT载荷
  static parseJwtPayload(token: string): any {
    try {
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      )
      return JSON.parse(jsonPayload)
    } catch (error) {
      console.error('JWT解析失败:', error)
      return {}
    }
  }
  
  // 获取用户信息
  static getUserInfo(): {
    username: string
    tenant: string
    roles: string[]
    permissions: string[]
  } | null {
    const token = this.getAccessToken()
    if (!token) return null
    
    const payload = this.parseJwtPayload(token)
    return {
      username: payload.sub,
      tenant: payload.tenant,
      roles: payload.roles || [],
      permissions: payload.permissions || []
    }
  }
  
  // 清除令牌
  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY)
    localStorage.removeItem(this.REFRESH_TOKEN_KEY)
    localStorage.removeItem(this.TOKEN_EXPIRY_KEY)
  }
}
```

### HTTP拦截器
```typescript
// utils/httpInterceptor.ts
import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { TokenManager } from './tokenManager'
import { refreshTokens } from './authApi'

// 请求拦截器
axios.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = TokenManager.getAccessToken()
    if (token && TokenManager.isTokenValid()) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean }
    
    // 如果是401错误且未重试过
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      try {
        // 尝试刷新令牌
        const refreshToken = TokenManager.getRefreshToken()
        if (refreshToken) {
          const response = await refreshTokens(refreshToken)
          TokenManager.saveTokens(response.accessToken, response.refreshToken)
          
          // 重新发送原始请求
          originalRequest.headers = originalRequest.headers || {}
          originalRequest.headers.Authorization = `Bearer ${response.accessToken}`
          return axios(originalRequest)
        }
      } catch (refreshError) {
        // 刷新失败，清除令牌并跳转到登录页
        TokenManager.clearTokens()
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }
    
    return Promise.reject(error)
  }
)
```

### 自动刷新机制
```typescript
// utils/tokenRefresh.ts
export class TokenRefreshManager {
  private refreshTimer: NodeJS.Timeout | null = null
  
  // 启动自动刷新
  startAutoRefresh(): void {
    this.stopAutoRefresh()
    this.scheduleNextRefresh()
  }
  
  // 停止自动刷新
  stopAutoRefresh(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
  }
  
  // 调度下次刷新
  private scheduleNextRefresh(): void {
    const expiryTime = localStorage.getItem('token_expiry')
    if (!expiryTime) return
    
    const now = Math.floor(Date.now() / 1000)
    const expiry = parseInt(expiryTime)
    const timeUntilExpiry = expiry - now
    
    // 在过期前5分钟刷新
    const refreshTime = Math.max(timeUntilExpiry - 300, 60) * 1000
    
    this.refreshTimer = setTimeout(async () => {
      try {
        await this.performTokenRefresh()
        this.scheduleNextRefresh()
      } catch (error) {
        console.error('自动刷新令牌失败:', error)
        // 跳转到登录页
        window.location.href = '/login'
      }
    }, refreshTime)
  }
  
  // 执行令牌刷新
  private async performTokenRefresh(): Promise<void> {
    const refreshToken = TokenManager.getRefreshToken()
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }
    
    const response = await refreshTokens(refreshToken)
    TokenManager.saveTokens(response.accessToken, response.refreshToken)
  }
}

// 全局实例
export const tokenRefreshManager = new TokenRefreshManager()
```

## 🔒 安全最佳实践

### 1. 令牌存储安全
```typescript
// 安全存储选项
enum TokenStorageType {
  MEMORY = 'memory',          // 内存存储（最安全，但刷新丢失）
  SESSION_STORAGE = 'session', // 会话存储（标签页关闭时清除）
  LOCAL_STORAGE = 'local',     // 本地存储（持久化，但有XSS风险）
  HTTP_ONLY_COOKIE = 'cookie'  // HTTP-Only Cookie（推荐）
}

// 安全配置
const securityConfig = {
  storage: TokenStorageType.HTTP_ONLY_COOKIE,
  encryption: true,           // 加密存储
  csrfProtection: true,      // CSRF保护
  secureCookies: true,       // 仅HTTPS传输
  sameSite: 'strict'         // 同站策略
}
```

### 2. 令牌生命周期管理
```java
@Service
public class TokenLifecycleService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    /**
     * 令牌黑名单管理
     */
    public void addToBlacklist(final String token) {
        final Claims claims = jwtTokenProvider.getClaims(token);
        final long expiry = claims.getExpiration().getTime();
        final long ttl = expiry - System.currentTimeMillis();
        
        if (ttl > 0) {
            redisTemplate.opsForValue().set(
                "blacklist:" + token, 
                "true", 
                ttl, 
                TimeUnit.MILLISECONDS
            );
        }
    }
    
    /**
     * 检查令牌是否在黑名单中
     */
    public boolean isBlacklisted(final String token) {
        return Boolean.TRUE.equals(
            redisTemplate.hasKey("blacklist:" + token)
        );
    }
    
    /**
     * 用户登出时使所有令牌失效
     */
    public void invalidateAllUserTokens(final String username) {
        final String pattern = "user_tokens:" + username + ":*";
        final Set<String> keys = redisTemplate.keys(pattern);
        
        if (!keys.isEmpty()) {
            keys.forEach(this::addToBlacklist);
            redisTemplate.delete(keys);
        }
    }
}
```

### 3. 令牌签名轮换
```java
@Component
@Scheduled(cron = "0 0 0 1 * ?") // 每月1号轮换
public class TokenSigningKeyRotation {
    
    @Value("${jwt.key-rotation.enabled:true}")
    private boolean keyRotationEnabled;
    
    public void rotateSigningKey() {
        if (!keyRotationEnabled) return;
        
        // 生成新的签名密钥
        final String newKey = generateSecureKey();
        
        // 保留旧密钥用于验证现有令牌
        final String oldKey = getCurrentKey();
        saveOldKey(oldKey);
        
        // 更新当前密钥
        updateCurrentKey(newKey);
        
        // 通知所有服务实例更新密钥
        publishKeyRotationEvent();
    }
    
    private String generateSecureKey() {
        final SecureRandom random = new SecureRandom();
        final byte[] key = new byte[64]; // 512位
        random.nextBytes(key);
        return Base64.getEncoder().encodeToString(key);
    }
}
```

## 📊 监控和审计

### JWT使用监控
```java
@Component
public class JwtMonitoringService {
    
    private final MeterRegistry meterRegistry;
    private final Counter tokenGeneratedCounter;
    private final Counter tokenValidationCounter;
    private final Timer tokenValidationTimer;
    
    public JwtMonitoringService(final MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.tokenGeneratedCounter = Counter.builder("jwt.tokens.generated")
            .description("Number of JWT tokens generated")
            .register(meterRegistry);
        this.tokenValidationCounter = Counter.builder("jwt.tokens.validated")
            .description("Number of JWT tokens validated")
            .tag("result", "success")
            .register(meterRegistry);
        this.tokenValidationTimer = Timer.builder("jwt.validation.duration")
            .description("JWT token validation duration")
            .register(meterRegistry);
    }
    
    public void recordTokenGenerated(final String tokenType) {
        tokenGeneratedCounter.increment(Tags.of("type", tokenType));
    }
    
    public void recordTokenValidation(final boolean success, final String reason) {
        tokenValidationCounter.increment(
            Tags.of("result", success ? "success" : "failure", "reason", reason)
        );
    }
}
```

## 📞 相关资源

- [认证系统总览](./index.md) - 整体认证架构
- [权限控制系统](./rbac.md) - RBAC权限设计
- [多因子认证](./mfa.md) - MFA实现方案
- [安全规范](../../development/quality/security/) - 安全最佳实践

---

*最后更新：2025-07-01*