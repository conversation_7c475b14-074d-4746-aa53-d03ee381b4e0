# 🔐 认证系统总览

## 🎯 认证系统概述

智慧养老评估平台认证系统支持多租户架构，提供完整的用户认证、授权和安全管理功能，确保系统安全性和数据隔离。

## 🏗️ 系统架构

### 核心组件
- **JWT Token认证**: 无状态的Token认证机制
- **多租户登录**: 支持不同租户的独立登录
- **权限控制**: 基于角色的访问控制(RBAC)
- **安全防护**: 多层次的安全防护机制

### 技术实现
- **Spring Security**: 后端安全框架
- **JWT**: JSON Web Token标准
- **Redis**: Token缓存和会话管理
- **BCrypt**: 密码加密算法

## 📚 文档导航

### 🚀 系统设计和实现
- [多租户登录系统实现报告_2025-06-21](./多租户登录系统实现报告_2025-06-21.md) - 多租户登录系统完整实现
- [登录系统设计与实现](./登录系统设计与实现.md) - 登录系统架构设计文档

### 🔧 功能修复和优化
- [多租户登录系统修复完成报告_2025-06-25](./多租户登录系统修复完成报告_2025-06-25.md) - 系统修复完成报告
- [登录功能修复报告](./登录功能修复报告.md) - 登录功能问题修复记录
- [前端登录修复验证_2025-06-25](./前端登录修复验证_2025-06-25.md) - 前端登录功能验证

### 🧪 测试和验证
- [用户登录测试成功报告_2025-06-25](./用户登录测试成功报告_2025-06-25.md) - 完整的登录测试报告
- [用户登录测试报告_2025-06-25](./用户登录测试报告_2025-06-25.md) - 用户登录功能测试
- [用户登录快速参考表_2025-06-25](./用户登录快速参考表_2025-06-25.md) - 登录操作快速参考

### 🔒 安全和Token管理
- [Token刷新功能安全实施报告_2025-01-02](./Token刷新功能安全实施报告_2025-01-02.md) - Token安全管理实施

### 🐛 问题排查
- [登录问题调试指南](./登录问题调试指南.md) - 登录问题诊断和解决方案

## 🔑 核心功能

### 用户认证流程
1. **用户登录**: 用户名/密码验证
2. **Token生成**: 生成JWT访问令牌和刷新令牌
3. **Token验证**: 请求验证和自动续期
4. **登录状态管理**: 前端登录状态维护

### 多租户支持
- **租户隔离**: 不同租户的用户数据完全隔离
- **域名路由**: 基于子域名的租户识别
- **权限分离**: 租户级别的权限管理
- **数据安全**: 确保租户间数据不泄露

### 权限控制系统
- **角色管理**: 灵活的角色定义和管理
- **权限分配**: 细粒度的权限分配机制
- **资源保护**: API和页面级别的访问控制
- **动态权限**: 运行时权限验证和控制

## 🛠️ 技术实现细节

### JWT Token设计
```json
{
  "sub": "user_id",
  "tenant": "tenant_id", 
  "roles": ["ADMIN", "USER"],
  "permissions": ["USER_READ", "USER_WRITE"],
  "iat": 1625097600,
  "exp": 1625184000
}
```

### 认证流程图
```
用户登录 → 验证凭据 → 生成Token → 返回Token
    ↓
前端存储 → API请求携带Token → 后端验证 → 返回数据
    ↓
Token过期 → 自动刷新 → 更新Token → 继续访问
```

### 安全机制
- **密码强度**: 强制密码复杂度要求
- **登录限制**: 防止暴力破解攻击
- **Token安全**: Token加密和签名验证
- **会话管理**: 安全的会话生命周期管理

## 📊 系统监控

### 认证指标
- **登录成功率**: 用户登录成功统计
- **Token使用情况**: Token生成和验证统计
- **安全事件**: 异常登录和安全威胁监控
- **性能指标**: 认证相关的性能监控

### 审计日志
- **登录记录**: 完整的用户登录日志
- **权限变更**: 用户权限修改记录
- **安全事件**: 安全相关事件记录
- **系统访问**: 关键系统访问审计

## 🔧 配置管理

### JWT配置
```yaml
jwt:
  secret: ${JWT_SECRET}
  access-token-validity: 3600    # 1小时
  refresh-token-validity: 86400  # 24小时
  issuer: assessment-platform
```

### Security配置
```yaml
security:
  password:
    min-length: 8
    require-uppercase: true
    require-lowercase: true
    require-numbers: true
    require-special-chars: true
  login:
    max-attempts: 5
    lockout-duration: 300  # 5分钟
```

## 🚀 最佳实践

### 安全建议
1. **定期更新密钥**: 定期轮换JWT签名密钥
2. **Token最小权限**: 根据用户角色生成最小权限Token
3. **安全传输**: 始终使用HTTPS传输敏感信息
4. **日志监控**: 实时监控异常的认证行为

### 性能优化
1. **Redis缓存**: 缓存用户信息和权限数据
2. **Token复用**: 合理的Token过期时间设置
3. **批量验证**: 批量权限验证减少数据库查询
4. **异步处理**: 异步处理登录日志和审计记录

## 🔗 API接口

### 认证相关接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - Token刷新
- `GET /api/auth/me` - 获取当前用户信息

### 用户管理接口
- `GET /api/users` - 用户列表查询
- `POST /api/users` - 创建新用户
- `PUT /api/users/{id}` - 更新用户信息
- `DELETE /api/users/{id}` - 删除用户

## 🧪 测试策略

### 功能测试
- **登录流程测试**: 完整的用户登录流程验证
- **权限验证测试**: 不同角色的权限访问测试
- **Token管理测试**: Token生成、验证和刷新测试
- **多租户测试**: 租户隔离和数据安全测试

### 安全测试
- **暴力破解防护**: 登录限制和锁定机制测试
- **SQL注入防护**: 登录参数的安全性测试
- **XSS防护**: 前端输入的安全性验证
- **CSRF防护**: 跨站请求伪造防护测试

## 📞 相关资源

- [JWT Token管理](./jwt.md) - JWT实现细节
- [权限控制系统](./rbac.md) - RBAC权限设计
- [多因子认证](./mfa.md) - MFA实现方案
- [后端开发指南](../../development/backend-guide.md) - 后端集成指南
- [前端开发指南](../../development/frontend-guide.md) - 前端集成指南

---

*最后更新：2025-07-01*
---

**文档版本**: v2.0 (v1.0.0-SNAPSHOT)  
**最后更新**: 2025年07月01日  
**更新内容**: 更新JWT认证实现、多租户登录支持  

*本文档随项目版本同步更新，获取最新版本请访问 [VitePress文档门户](http://localhost:3005)*
