# 🔐 多因子认证 (MFA)

## 🎯 MFA概述

智慧养老评估平台的多因子认证(Multi-Factor Authentication, MFA)系统为用户账户提供额外的安全保护层，通过要求用户提供两个或更多验证因子来确认身份。

## 🏗️ MFA架构设计

### 认证因子类型

智慧养老评估平台支持以下三类认证因子：

1. **知识因子 (Something You Know)**
   - 用户密码
   - 安全问题答案
   - PIN码

2. **持有因子 (Something You Have)**
   - 短信验证码
   - 邮箱验证码
   - TOTP应用(Google Authenticator, Authy等)
   - 硬件令牌

3. **生物因子 (Something You Are)**
   - 指纹识别(移动端)
   - 面部识别(移动端)
   - 声纹识别(未来支持)

### MFA流程架构

```
用户登录请求 → 主要认证(密码) → MFA验证 → 访问授权
    ↓               ↓              ↓          ↓
用户名/密码     → 密码验证通过   → 二次验证   → 生成JWT Token
    ↓               ↓              ↓          ↓
验证失败返回    → 触发MFA流程   → 验证通过   → 用户成功登录
```

## 🗄️ 数据模型设计

### MFA相关表结构

```sql
-- MFA设备表
CREATE TABLE mfa_devices (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    device_type VARCHAR(20) NOT NULL, -- 'SMS', 'EMAIL', 'TOTP', 'HARDWARE'
    device_name VARCHAR(100) NOT NULL,
    secret_key VARCHAR(255), -- TOTP密钥或设备标识
    phone_number VARCHAR(20), -- SMS设备
    email_address VARCHAR(100), -- EMAIL设备
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主要设备
    is_enabled BOOLEAN DEFAULT TRUE,
    backup_codes JSON, -- 备用恢复码
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP,
    verified_at TIMESTAMP,
    UNIQUE(user_id, device_type, phone_number),
    UNIQUE(user_id, device_type, email_address)
);

-- MFA验证记录表
CREATE TABLE mfa_verifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    device_id BIGINT REFERENCES mfa_devices(id),
    verification_code VARCHAR(10) NOT NULL,
    verification_type VARCHAR(20) NOT NULL, -- 'LOGIN', 'SENSITIVE_OP', 'DEVICE_SETUP'
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    is_successful BOOLEAN DEFAULT FALSE,
    attempt_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- MFA策略配置表
CREATE TABLE mfa_policies (
    id BIGSERIAL PRIMARY KEY,
    organization_id BIGINT REFERENCES organizations(id),
    role_id BIGINT REFERENCES roles(id),
    policy_name VARCHAR(100) NOT NULL,
    is_required BOOLEAN DEFAULT FALSE, -- 是否强制要求MFA
    allowed_methods JSON NOT NULL, -- 允许的MFA方法 ['SMS', 'EMAIL', 'TOTP']
    grace_period_hours INTEGER DEFAULT 24, -- 新用户宽限期
    backup_codes_count INTEGER DEFAULT 10, -- 备用码数量
    session_timeout_minutes INTEGER DEFAULT 480, -- MFA会话超时时间
    max_attempts_per_hour INTEGER DEFAULT 5, -- 每小时最大尝试次数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_mfa_devices_user_id ON mfa_devices(user_id);
CREATE INDEX idx_mfa_devices_type ON mfa_devices(device_type);
CREATE INDEX idx_mfa_verifications_user_id ON mfa_verifications(user_id);
CREATE INDEX idx_mfa_verifications_code ON mfa_verifications(verification_code);
CREATE INDEX idx_mfa_verifications_expires ON mfa_verifications(expires_at);
```

## 🔧 后端实现

### MFA服务核心类

```java
@Service
@Transactional
@Slf4j
public class MfaService {
    
    private static final int TOTP_WINDOW = 1; // TOTP时间窗口
    private static final int BACKUP_CODE_LENGTH = 8;
    private static final int VERIFICATION_CODE_LENGTH = 6;
    private static final int CODE_EXPIRY_MINUTES = 5;
    
    @Autowired
    private MfaDeviceRepository mfaDeviceRepository;
    
    @Autowired
    private MfaVerificationRepository mfaVerificationRepository;
    
    @Autowired
    private MfaPolicyRepository mfaPolicyRepository;
    
    @Autowired
    private SmsService smsService;
    
    @Autowired
    private EmailService emailService;
    
    @Autowired
    private TotpService totpService;
    
    /**
     * 检查用户是否需要进行MFA验证
     */
    public boolean isMfaRequired(final Long userId) {
        final User user = userRepository.findById(userId)
            .orElseThrow(() -> new NotFoundException("用户不存在"));
        
        final MfaPolicy policy = getMfaPolicyForUser(user);
        
        // 检查是否强制要求MFA
        if (policy.getIsRequired()) {
            return true;
        }
        
        // 检查用户是否已启用MFA设备
        final List<MfaDevice> enabledDevices = mfaDeviceRepository
            .findByUserIdAndIsEnabledTrue(userId);
        
        return !enabledDevices.isEmpty();
    }
    
    /**
     * 为用户设置TOTP设备
     */
    public MfaSetupResponse setupTotpDevice(final Long userId, final String deviceName) {
        final String secretKey = totpService.generateSecret();
        final String qrCodeUrl = totpService.generateQrCodeUrl(
            getCurrentUser(userId).getEmail(), 
            secretKey,
            "智慧养老评估平台"
        );
        
        final MfaDevice device = MfaDevice.builder()
            .userId(userId)
            .deviceType(MfaDeviceType.TOTP)
            .deviceName(deviceName)
            .secretKey(secretKey)
            .isEnabled(false) // 需要验证后启用
            .build();
        
        mfaDeviceRepository.save(device);
        
        return MfaSetupResponse.builder()
            .deviceId(device.getId())
            .secretKey(secretKey)
            .qrCodeUrl(qrCodeUrl)
            .backupCodes(generateBackupCodes())
            .build();
    }
    
    /**
     * 验证TOTP设备设置
     */
    public void verifyTotpSetup(final Long userId, final Long deviceId, final String code) {
        final MfaDevice device = mfaDeviceRepository.findById(deviceId)
            .orElseThrow(() -> new NotFoundException("MFA设备不存在"));
        
        if (!device.getUserId().equals(userId)) {
            throw new UnauthorizedException("无权访问此设备");
        }
        
        if (!totpService.validateCode(device.getSecretKey(), code, TOTP_WINDOW)) {
            throw new InvalidMfaCodeException("验证码无效");
        }
        
        // 启用设备
        device.setIsEnabled(true);
        device.setVerifiedAt(LocalDateTime.now());
        
        // 如果这是用户的第一个MFA设备，设为主要设备
        final long deviceCount = mfaDeviceRepository.countByUserIdAndIsEnabledTrue(userId);
        if (deviceCount == 0) {
            device.setIsPrimary(true);
        }
        
        mfaDeviceRepository.save(device);
        
        log.info("用户 {} 成功设置TOTP设备: {}", userId, device.getDeviceName());
    }
    
    /**
     * 设置短信MFA设备
     */
    public void setupSmsDevice(final Long userId, final String phoneNumber, final String deviceName) {
        // 检查手机号格式
        if (!isValidPhoneNumber(phoneNumber)) {
            throw new InvalidParameterException("手机号格式无效");
        }
        
        // 检查是否已存在相同手机号的设备
        final Optional<MfaDevice> existingDevice = mfaDeviceRepository
            .findByUserIdAndDeviceTypeAndPhoneNumber(userId, MfaDeviceType.SMS, phoneNumber);
        
        if (existingDevice.isPresent()) {
            throw new DuplicateDeviceException("该手机号已绑定");
        }
        
        final MfaDevice device = MfaDevice.builder()
            .userId(userId)
            .deviceType(MfaDeviceType.SMS)
            .deviceName(deviceName)
            .phoneNumber(phoneNumber)
            .isEnabled(false)
            .build();
        
        mfaDeviceRepository.save(device);
        
        // 发送验证短信
        sendSmsVerification(userId, device.getId());
    }
    
    /**
     * 发送MFA验证码
     */
    public void sendMfaVerification(final Long userId, final MfaDeviceType deviceType, 
                                  final String context) {
        final MfaDevice device = mfaDeviceRepository
            .findByUserIdAndDeviceTypeAndIsEnabledTrueAndIsPrimaryTrue(userId, deviceType)
            .orElseThrow(() -> new NotFoundException("未找到可用的MFA设备"));
        
        final String verificationCode = generateVerificationCode();
        final LocalDateTime expiresAt = LocalDateTime.now().plusMinutes(CODE_EXPIRY_MINUTES);
        
        // 保存验证记录
        final MfaVerification verification = MfaVerification.builder()
            .userId(userId)
            .deviceId(device.getId())
            .verificationCode(verificationCode)
            .verificationType(context)
            .expiresAt(expiresAt)
            .ipAddress(getCurrentIpAddress())
            .userAgent(getCurrentUserAgent())
            .build();
        
        mfaVerificationRepository.save(verification);
        
        // 发送验证码
        switch (deviceType) {
            case SMS:
                smsService.sendMfaCode(device.getPhoneNumber(), verificationCode);
                break;
            case EMAIL:
                emailService.sendMfaCode(device.getEmailAddress(), verificationCode);
                break;
            case TOTP:
                // TOTP不需要发送，用户从应用中获取
                break;
            default:
                throw new UnsupportedOperationException("不支持的MFA设备类型");
        }
        
        log.info("为用户 {} 发送 {} MFA验证码", userId, deviceType);
    }
    
    /**
     * 验证MFA代码
     */
    public boolean verifyMfaCode(final Long userId, final String code, final String context) {
        // 获取用户的活跃验证记录
        final List<MfaVerification> activeVerifications = mfaVerificationRepository
            .findByUserIdAndVerificationTypeAndExpiresAtAfterAndUsedAtIsNull(
                userId, context, LocalDateTime.now()
            );
        
        if (activeVerifications.isEmpty()) {
            return false;
        }
        
        // 尝试匹配验证码
        for (final MfaVerification verification : activeVerifications) {
            final MfaDevice device = mfaDeviceRepository.findById(verification.getDeviceId())
                .orElse(null);
            
            if (device == null) {
                continue;
            }
            
            boolean isValid = false;
            
            switch (device.getDeviceType()) {
                case SMS:
                case EMAIL:
                    isValid = code.equals(verification.getVerificationCode());
                    break;
                case TOTP:
                    isValid = totpService.validateCode(device.getSecretKey(), code, TOTP_WINDOW);
                    break;
            }
            
            if (isValid) {
                // 标记验证成功
                verification.setUsedAt(LocalDateTime.now());
                verification.setIsSuccessful(true);
                mfaVerificationRepository.save(verification);
                
                // 更新设备最后使用时间
                device.setLastUsedAt(LocalDateTime.now());
                mfaDeviceRepository.save(device);
                
                log.info("用户 {} MFA验证成功: deviceType={}", userId, device.getDeviceType());
                return true;
            }
        }
        
        // 验证失败，记录尝试次数
        recordFailedAttempt(userId, context);
        return false;
    }
    
    /**
     * 使用备用恢复码
     */
    public boolean verifyBackupCode(final Long userId, final String backupCode) {
        final List<MfaDevice> devices = mfaDeviceRepository.findByUserIdAndIsEnabledTrue(userId);
        
        for (final MfaDevice device : devices) {
            if (device.getBackupCodes() != null) {
                final List<String> codes = JsonUtils.fromJson(
                    device.getBackupCodes().toString(), 
                    List.class
                );
                
                if (codes.contains(backupCode)) {
                    // 移除已使用的备用码
                    codes.remove(backupCode);
                    device.setBackupCodes(JsonUtils.toJsonNode(codes));
                    mfaDeviceRepository.save(device);
                    
                    log.info("用户 {} 使用备用恢复码成功", userId);
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 生成备用恢复码
     */
    private List<String> generateBackupCodes() {
        final List<String> codes = new ArrayList<>();
        final SecureRandom random = new SecureRandom();
        
        for (int i = 0; i < 10; i++) {
            final StringBuilder code = new StringBuilder();
            for (int j = 0; j < BACKUP_CODE_LENGTH; j++) {
                code.append(random.nextInt(10));
            }
            codes.add(code.toString());
        }
        
        return codes;
    }
    
    /**
     * 生成验证码
     */
    private String generateVerificationCode() {
        final SecureRandom random = new SecureRandom();
        final StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < VERIFICATION_CODE_LENGTH; i++) {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }
    
    /**
     * 记录失败尝试
     */
    private void recordFailedAttempt(final Long userId, final String context) {
        final int attemptCount = mfaVerificationRepository
            .countFailedAttemptsInLastHour(userId, LocalDateTime.now().minusHours(1));
        
        final MfaPolicy policy = getMfaPolicyForUser(getCurrentUser(userId));
        
        if (attemptCount >= policy.getMaxAttemptsPerHour()) {
            log.warn("用户 {} MFA验证失败次数过多，触发安全策略", userId);
            // 触发账户锁定或其他安全措施
            securityService.handleSuspiciousActivity(userId, "MFA_TOO_MANY_FAILURES");
        }
    }
}
```

### TOTP服务实现

```java
@Service
@Slf4j
public class TotpService {
    
    private static final String ISSUER = "智慧养老评估平台";
    private static final int SECRET_LENGTH = 32;
    private static final int TOTP_PERIOD = 30; // 30秒周期
    
    /**
     * 生成TOTP密钥
     */
    public String generateSecret() {
        final SecureRandom random = new SecureRandom();
        final byte[] bytes = new byte[SECRET_LENGTH];
        random.nextBytes(bytes);
        return Base32.encode(bytes);
    }
    
    /**
     * 生成QR码URL
     */
    public String generateQrCodeUrl(final String userEmail, final String secret, 
                                  final String issuer) {
        final String encodedIssuer = URLEncoder.encode(issuer, StandardCharsets.UTF_8);
        final String encodedEmail = URLEncoder.encode(userEmail, StandardCharsets.UTF_8);
        
        return String.format(
            "otpauth://totp/%s:%s?secret=%s&issuer=%s&algorithm=SHA1&digits=6&period=%d",
            encodedIssuer,
            encodedEmail,
            secret,
            encodedIssuer,
            TOTP_PERIOD
        );
    }
    
    /**
     * 验证TOTP代码
     */
    public boolean validateCode(final String secret, final String code, final int window) {
        final long currentTimeSlot = getCurrentTimeSlot();
        
        // 在时间窗口内验证
        for (int i = -window; i <= window; i++) {
            final String expectedCode = generateTotpCode(secret, currentTimeSlot + i);
            if (code.equals(expectedCode)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 生成当前时间的TOTP代码
     */
    public String generateCurrentCode(final String secret) {
        return generateTotpCode(secret, getCurrentTimeSlot());
    }
    
    /**
     * 生成指定时间槽的TOTP代码
     */
    private String generateTotpCode(final String secret, final long timeSlot) {
        try {
            final byte[] secretBytes = Base32.decode(secret);
            final byte[] timeBytes = ByteBuffer.allocate(8).putLong(timeSlot).array();
            
            final Mac mac = Mac.getInstance("HmacSHA1");
            final SecretKeySpec keySpec = new SecretKeySpec(secretBytes, "HmacSHA1");
            mac.init(keySpec);
            
            final byte[] hash = mac.doFinal(timeBytes);
            final int offset = hash[19] & 0x0f;
            
            final int truncatedHash = ((hash[offset] & 0x7f) << 24) |
                                    ((hash[offset + 1] & 0xff) << 16) |
                                    ((hash[offset + 2] & 0xff) << 8) |
                                    (hash[offset + 3] & 0xff);
            
            final int code = truncatedHash % 1000000;
            return String.format("%06d", code);
            
        } catch (final Exception e) {
            log.error("生成TOTP代码失败", e);
            throw new RuntimeException("TOTP代码生成失败", e);
        }
    }
    
    /**
     * 获取当前时间槽
     */
    private long getCurrentTimeSlot() {
        return System.currentTimeMillis() / 1000 / TOTP_PERIOD;
    }
}
```

### MFA控制器

```java
@RestController
@RequestMapping("/api/mfa")
@Validated
@Slf4j
public class MfaController {
    
    @Autowired
    private MfaService mfaService;
    
    /**
     * 获取MFA状态
     */
    @GetMapping("/status")
    @PreAuthorize("hasPermission(null, 'MFA:READ')")
    public ResponseEntity<MfaStatusResponse> getMfaStatus() {
        final Long userId = getCurrentUserId();
        final boolean isRequired = mfaService.isMfaRequired(userId);
        final List<MfaDevice> devices = mfaService.getUserMfaDevices(userId);
        
        return ResponseEntity.ok(MfaStatusResponse.builder()
            .isEnabled(isRequired)
            .isRequired(isRequired)
            .devices(devices.stream()
                .map(this::toDeviceInfo)
                .collect(Collectors.toList()))
            .build());
    }
    
    /**
     * 设置TOTP设备
     */
    @PostMapping("/setup/totp")
    @PreAuthorize("hasPermission(null, 'MFA:SETUP')")
    public ResponseEntity<MfaSetupResponse> setupTotp(
            @RequestBody @Valid final SetupTotpRequest request) {
        final Long userId = getCurrentUserId();
        final MfaSetupResponse response = mfaService.setupTotpDevice(
            userId, 
            request.getDeviceName()
        );
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 验证TOTP设备设置
     */
    @PostMapping("/setup/totp/{deviceId}/verify")
    @PreAuthorize("hasPermission(null, 'MFA:SETUP')")
    public ResponseEntity<ApiResponse> verifyTotpSetup(
            @PathVariable final Long deviceId,
            @RequestBody @Valid final VerifyCodeRequest request) {
        final Long userId = getCurrentUserId();
        
        mfaService.verifyTotpSetup(userId, deviceId, request.getCode());
        
        return ResponseEntity.ok(ApiResponse.success("TOTP设备设置成功"));
    }
    
    /**
     * 设置短信MFA设备
     */
    @PostMapping("/setup/sms")
    @PreAuthorize("hasPermission(null, 'MFA:SETUP')")
    public ResponseEntity<ApiResponse> setupSms(
            @RequestBody @Valid final SetupSmsRequest request) {
        final Long userId = getCurrentUserId();
        
        mfaService.setupSmsDevice(
            userId,
            request.getPhoneNumber(),
            request.getDeviceName()
        );
        
        return ResponseEntity.ok(ApiResponse.success("短信验证设备设置成功"));
    }
    
    /**
     * 发送MFA验证码
     */
    @PostMapping("/send-code")
    public ResponseEntity<ApiResponse> sendMfaCode(
            @RequestBody @Valid final SendMfaCodeRequest request) {
        final Long userId = getCurrentUserId();
        
        mfaService.sendMfaVerification(
            userId,
            request.getDeviceType(),
            request.getContext()
        );
        
        return ResponseEntity.ok(ApiResponse.success("验证码已发送"));
    }
    
    /**
     * 验证MFA代码
     */
    @PostMapping("/verify")
    public ResponseEntity<MfaVerificationResponse> verifyMfaCode(
            @RequestBody @Valid final VerifyMfaCodeRequest request) {
        final Long userId = getCurrentUserId();
        
        final boolean isValid = mfaService.verifyMfaCode(
            userId,
            request.getCode(),
            request.getContext()
        );
        
        if (isValid) {
            // 生成MFA会话令牌
            final String mfaToken = jwtService.generateMfaToken(userId);
            
            return ResponseEntity.ok(MfaVerificationResponse.builder()
                .success(true)
                .mfaToken(mfaToken)
                .message("MFA验证成功")
                .build());
        } else {
            return ResponseEntity.badRequest()
                .body(MfaVerificationResponse.builder()
                    .success(false)
                    .message("验证码无效或已过期")
                    .build());
        }
    }
    
    /**
     * 使用备用恢复码
     */
    @PostMapping("/recovery")
    public ResponseEntity<MfaVerificationResponse> useBackupCode(
            @RequestBody @Valid final UseBackupCodeRequest request) {
        final Long userId = getCurrentUserId();
        
        final boolean isValid = mfaService.verifyBackupCode(
            userId,
            request.getBackupCode()
        );
        
        if (isValid) {
            final String mfaToken = jwtService.generateMfaToken(userId);
            
            return ResponseEntity.ok(MfaVerificationResponse.builder()
                .success(true)
                .mfaToken(mfaToken)
                .message("备用恢复码验证成功")
                .build());
        } else {
            return ResponseEntity.badRequest()
                .body(MfaVerificationResponse.builder()
                    .success(false)
                    .message("恢复码无效")
                    .build());
        }
    }
    
    /**
     * 删除MFA设备
     */
    @DeleteMapping("/devices/{deviceId}")
    @PreAuthorize("hasPermission(null, 'MFA:DELETE')")
    public ResponseEntity<ApiResponse> removeMfaDevice(@PathVariable final Long deviceId) {
        final Long userId = getCurrentUserId();
        
        mfaService.removeMfaDevice(userId, deviceId);
        
        return ResponseEntity.ok(ApiResponse.success("MFA设备删除成功"));
    }
}
```

## 🎨 前端MFA实现

### MFA设置组件

```vue
<!-- components/MfaSetup.vue -->
<template>
  <div class="mfa-setup">
    <div class="mfa-header">
      <h2>多因子认证设置</h2>
      <p class="text-gray-600">为您的账户添加额外的安全保护</p>
    </div>
    
    <!-- 当前MFA状态 -->
    <div class="mfa-status-card">
      <div class="status-indicator" :class="{ active: mfaStatus.isEnabled }">
        <i :class="mfaStatus.isEnabled ? 'el-icon-success' : 'el-icon-warning'"></i>
      </div>
      <div class="status-content">
        <h3>{{ mfaStatus.isEnabled ? 'MFA已启用' : 'MFA未启用' }}</h3>
        <p>{{ mfaStatus.isEnabled ? '您的账户已启用多因子认证保护' : '建议启用多因子认证增强账户安全' }}</p>
      </div>
    </div>
    
    <!-- MFA设备列表 -->
    <div class="mfa-devices">
      <h3>认证设备</h3>
      
      <div v-if="mfaDevices.length === 0" class="no-devices">
        <i class="el-icon-phone text-4xl text-gray-400"></i>
        <p>暂无认证设备</p>
        <p class="text-sm text-gray-500">添加认证设备以启用多因子认证</p>
      </div>
      
      <div v-else class="device-list">
        <div 
          v-for="device in mfaDevices" 
          :key="device.id"
          class="device-item"
        >
          <div class="device-icon">
            <i :class="getDeviceIcon(device.deviceType)"></i>
          </div>
          <div class="device-info">
            <h4>{{ device.deviceName }}</h4>
            <p class="device-details">
              <span v-if="device.deviceType === 'SMS'">{{ maskPhoneNumber(device.phoneNumber) }}</span>
              <span v-else-if="device.deviceType === 'EMAIL'">{{ maskEmail(device.emailAddress) }}</span>
              <span v-else-if="device.deviceType === 'TOTP'">身份验证器应用</span>
            </p>
            <p class="device-meta">
              <span v-if="device.isPrimary" class="primary-badge">主要设备</span>
              <span class="last-used">最后使用: {{ formatTime(device.lastUsedAt) }}</span>
            </p>
          </div>
          <div class="device-actions">
            <BaseButton 
              size="small" 
              @click="removeDevice(device)"
              :loading="removing === device.id"
            >
              移除
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加设备按钮 -->
    <div class="add-device-section">
      <h3>添加新设备</h3>
      <div class="device-options">
        <div class="device-option" @click="showTotpSetup = true">
          <div class="option-icon">
            <i class="el-icon-mobile-phone"></i>
          </div>
          <div class="option-content">
            <h4>身份验证器应用</h4>
            <p>使用Google Authenticator、Authy等应用</p>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
        
        <div class="device-option" @click="showSmsSetup = true">
          <div class="option-icon">
            <i class="el-icon-message"></i>
          </div>
          <div class="option-content">
            <h4>短信验证</h4>
            <p>通过手机短信接收验证码</p>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
        
        <div class="device-option" @click="showEmailSetup = true">
          <div class="option-icon">
            <i class="el-icon-mail"></i>
          </div>
          <div class="option-content">
            <h4>邮箱验证</h4>
            <p>通过邮箱接收验证码</p>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>
    
    <!-- TOTP设置对话框 -->
    <TotpSetupDialog 
      v-model="showTotpSetup"
      @success="handleSetupSuccess"
    />
    
    <!-- 短信设置对话框 -->
    <SmsSetupDialog 
      v-model="showSmsSetup"
      @success="handleSetupSuccess"
    />
    
    <!-- 邮箱设置对话框 -->
    <EmailSetupDialog 
      v-model="showEmailSetup"
      @success="handleSetupSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BaseButton from '@/components/base/BaseButton.vue'
import TotpSetupDialog from './TotpSetupDialog.vue'
import SmsSetupDialog from './SmsSetupDialog.vue'
import EmailSetupDialog from './EmailSetupDialog.vue'
import { useMfaStore } from '@/stores/modules/mfa'

const mfaStore = useMfaStore()

const mfaStatus = ref({
  isEnabled: false,
  isRequired: false
})

const mfaDevices = ref([])
const removing = ref(null)
const showTotpSetup = ref(false)
const showSmsSetup = ref(false)
const showEmailSetup = ref(false)

onMounted(async () => {
  await loadMfaStatus()
})

async function loadMfaStatus() {
  try {
    const status = await mfaStore.getMfaStatus()
    mfaStatus.value = status
    mfaDevices.value = status.devices || []
  } catch (error) {
    ElMessage.error('加载MFA状态失败')
  }
}

function getDeviceIcon(deviceType: string) {
  switch (deviceType) {
    case 'TOTP': return 'el-icon-mobile-phone'
    case 'SMS': return 'el-icon-message'
    case 'EMAIL': return 'el-icon-mail'
    default: return 'el-icon-key'
  }
}

function maskPhoneNumber(phone: string) {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

function maskEmail(email: string) {
  if (!email) return ''
  const [username, domain] = email.split('@')
  if (username.length <= 2) return email
  return `${username.slice(0, 2)}***@${domain}`
}

function formatTime(time: string) {
  if (!time) return '从未使用'
  return new Date(time).toLocaleString('zh-CN')
}

async function removeDevice(device) {
  try {
    await ElMessageBox.confirm(
      `确认移除认证设备 "${device.deviceName}" 吗？`,
      '移除设备',
      {
        type: 'warning',
        confirmButtonText: '移除',
        cancelButtonText: '取消'
      }
    )
    
    removing.value = device.id
    await mfaStore.removeMfaDevice(device.id)
    await loadMfaStatus()
    ElMessage.success('设备移除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('设备移除失败')
    }
  } finally {
    removing.value = null
  }
}

async function handleSetupSuccess() {
  await loadMfaStatus()
  ElMessage.success('MFA设备设置成功')
}
</script>

<style scoped>
.mfa-setup {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.mfa-header {
  margin-bottom: 32px;
  text-align: center;
}

.mfa-status-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
}

.status-indicator {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: #f5f5f5;
  color: #999;
}

.status-indicator.active {
  background: #52c41a;
  color: white;
}

.device-list {
  space-y: 12px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.device-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.device-info {
  flex: 1;
}

.device-details {
  color: #666;
  font-size: 14px;
  margin: 4px 0;
}

.device-meta {
  font-size: 12px;
  color: #999;
}

.primary-badge {
  background: #1890ff;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  margin-right: 8px;
}

.device-options {
  display: grid;
  gap: 12px;
}

.device-option {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.device-option:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.option-content {
  flex: 1;
}
</style>
```

### TOTP设置对话框

```vue
<!-- components/TotpSetupDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    title="设置身份验证器应用"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="totp-setup">
      <!-- 步骤1: 扫描二维码 -->
      <div v-if="step === 1" class="setup-step">
        <h3>步骤1: 扫描二维码</h3>
        <p class="step-description">
          使用身份验证器应用(如Google Authenticator、Authy)扫描下方二维码
        </p>
        
        <div class="qr-code-section">
          <div v-if="qrCodeUrl" class="qr-code">
            <QRCode :value="qrCodeUrl" :size="200" />
          </div>
          <div v-else class="loading">
            <i class="el-icon-loading"></i>
            <p>正在生成二维码...</p>
          </div>
        </div>
        
        <div class="manual-entry">
          <el-collapse>
            <el-collapse-item title="无法扫描二维码？手动输入密钥">
              <div class="secret-key">
                <label>密钥:</label>
                <el-input
                  :value="secretKey"
                  readonly
                  class="secret-input"
                >
                  <template #append>
                    <el-button @click="copySecret">复制</el-button>
                  </template>
                </el-input>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        
        <div class="step-actions">
          <BaseButton type="primary" @click="nextStep">下一步</BaseButton>
          <BaseButton @click="handleClose">取消</BaseButton>
        </div>
      </div>
      
      <!-- 步骤2: 验证设置 -->
      <div v-if="step === 2" class="setup-step">
        <h3>步骤2: 验证设置</h3>
        <p class="step-description">
          输入身份验证器应用中显示的6位验证码以完成设置
        </p>
        
        <el-form :model="verifyForm" :rules="verifyRules" ref="verifyFormRef">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input
              v-model="verifyForm.deviceName"
              placeholder="例如: 我的手机"
              maxlength="50"
            />
          </el-form-item>
          
          <el-form-item label="验证码" prop="code">
            <el-input
              v-model="verifyForm.code"
              placeholder="输入6位验证码"
              maxlength="6"
              class="code-input"
            />
          </el-form-item>
        </el-form>
        
        <div class="backup-codes" v-if="backupCodes.length > 0">
          <h4>备用恢复码</h4>
          <p class="backup-description">
            请保存这些恢复码，当您无法使用身份验证器时可以使用它们。每个恢复码只能使用一次。
          </p>
          <div class="codes-grid">
            <div v-for="code in backupCodes" :key="code" class="backup-code">
              {{ code }}
            </div>
          </div>
          <div class="backup-actions">
            <BaseButton @click="downloadBackupCodes">下载恢复码</BaseButton>
            <BaseButton @click="copyBackupCodes">复制恢复码</BaseButton>
          </div>
        </div>
        
        <div class="step-actions">
          <BaseButton 
            type="primary" 
            @click="verifySetup"
            :loading="verifying"
          >
            完成设置
          </BaseButton>
          <BaseButton @click="previousStep">上一步</BaseButton>
          <BaseButton @click="handleClose">取消</BaseButton>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElCollapse, ElCollapseItem } from 'element-plus'
import QRCode from '@/components/base/QRCode.vue'
import BaseButton from '@/components/base/BaseButton.vue'
import { useMfaStore } from '@/stores/modules/mfa'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

const mfaStore = useMfaStore()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const step = ref(1)
const deviceId = ref(null)
const secretKey = ref('')
const qrCodeUrl = ref('')
const backupCodes = ref([])
const verifying = ref(false)

const verifyForm = ref({
  deviceName: '',
  code: ''
})

const verifyRules = {
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 1, max: 50, message: '设备名称长度为1-50字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

const verifyFormRef = ref()

watch(visible, async (newValue) => {
  if (newValue) {
    await startSetup()
  } else {
    resetForm()
  }
})

async function startSetup() {
  try {
    const response = await mfaStore.setupTotpDevice(
      verifyForm.value.deviceName || '身份验证器'
    )
    
    deviceId.value = response.deviceId
    secretKey.value = response.secretKey
    qrCodeUrl.value = response.qrCodeUrl
    backupCodes.value = response.backupCodes || []
    
  } catch (error) {
    ElMessage.error('TOTP设备设置失败')
    handleClose()
  }
}

function nextStep() {
  step.value = 2
}

function previousStep() {
  step.value = 1
}

async function verifySetup() {
  if (!verifyFormRef.value) return
  
  const valid = await verifyFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  try {
    verifying.value = true
    
    await mfaStore.verifyTotpSetup(
      deviceId.value,
      verifyForm.value.code
    )
    
    ElMessage.success('TOTP设备设置成功')
    emit('success')
    handleClose()
    
  } catch (error) {
    ElMessage.error('验证码无效，请重试')
  } finally {
    verifying.value = false
  }
}

function copySecret() {
  navigator.clipboard.writeText(secretKey.value)
  ElMessage.success('密钥已复制')
}

function copyBackupCodes() {
  const codesText = backupCodes.value.join('\n')
  navigator.clipboard.writeText(codesText)
  ElMessage.success('恢复码已复制')
}

function downloadBackupCodes() {
  const codesText = [
    '智慧养老评估平台 - 备用恢复码',
    '请安全保存这些恢复码，每个恢复码只能使用一次',
    '',
    ...backupCodes.value,
    '',
    `生成时间: ${new Date().toLocaleString('zh-CN')}`
  ].join('\n')
  
  const blob = new Blob([codesText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `backup-codes-${Date.now()}.txt`
  link.click()
  URL.revokeObjectURL(url)
}

function resetForm() {
  step.value = 1
  deviceId.value = null
  secretKey.value = ''
  qrCodeUrl.value = ''
  backupCodes.value = []
  verifying.value = false
  verifyForm.value = {
    deviceName: '',
    code: ''
  }
}

function handleClose() {
  visible.value = false
}
</script>

<style scoped>
.setup-step {
  text-align: center;
}

.step-description {
  color: #666;
  margin-bottom: 24px;
}

.qr-code-section {
  margin: 24px 0;
}

.qr-code {
  display: inline-block;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.manual-entry {
  margin: 24px 0;
  text-align: left;
}

.secret-input {
  font-family: monospace;
}

.backup-codes {
  margin: 24px 0;
  text-align: left;
}

.backup-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 16px;
}

.codes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.backup-code {
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-family: monospace;
  text-align: center;
}

.backup-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.step-actions {
  margin-top: 24px;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.code-input {
  text-align: center;
  font-family: monospace;
  font-size: 18px;
}
</style>
```

## 📱 移动端生物识别

### 移动端MFA组件

```typescript
// composables/useBiometric.ts
import { ref } from 'vue'

export interface BiometricConfig {
  title: string
  subtitle: string
  description: string
  negativeButtonText: string
}

export function useBiometric() {
  const isSupported = ref(false)
  const isEnrolled = ref(false)
  
  // 检查生物识别支持
  async function checkBiometricSupport() {
    try {
      // uni-app 生物识别API
      const result = await uni.checkIsSupportSoterAuthentication()
      isSupported.value = result.supportMode.includes('fingerPrint') || 
                         result.supportMode.includes('facial')
      
      if (isSupported.value) {
        const enrollResult = await uni.checkIsSoterEnrolledInDevice({
          checkAuthMode: 'fingerPrint'
        })
        isEnrolled.value = enrollResult.isEnrolled
      }
    } catch (error) {
      console.error('检查生物识别支持失败:', error)
      isSupported.value = false
    }
  }
  
  // 启动生物识别
  async function startBiometricAuth(config: BiometricConfig) {
    if (!isSupported.value || !isEnrolled.value) {
      throw new Error('设备不支持生物识别或未注册')
    }
    
    return new Promise((resolve, reject) => {
      uni.startSoterAuthentication({
        requestAuthModes: ['fingerPrint'],
        challenge: generateChallenge(),
        authContent: config.title,
        success: (result) => {
          resolve(result)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }
  
  // 生成挑战码
  function generateChallenge(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  }
  
  return {
    isSupported,
    isEnrolled,
    checkBiometricSupport,
    startBiometricAuth
  }
}
```

## 🔒 安全最佳实践

### 1. MFA策略配置

```java
@Component
public class MfaSecurityPolicies {
    
    /**
     * 基于风险的MFA策略
     */
    public boolean shouldRequireMfa(final Long userId, final LoginContext context) {
        // 检查用户风险等级
        final UserRiskLevel riskLevel = getUserRiskLevel(userId);
        
        // 检查登录上下文风险
        final LoginRisk loginRisk = assessLoginRisk(context);
        
        // 高风险用户或异常登录必须MFA
        if (riskLevel == UserRiskLevel.HIGH || loginRisk.isAnomalous()) {
            return true;
        }
        
        // 管理员角色必须MFA
        if (hasAdminRole(userId)) {
            return true;
        }
        
        // 敏感操作需要MFA
        if (context.getRequestedResource().isSensitive()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * MFA设备安全验证
     */
    public void validateMfaDeviceSecurity(final MfaDevice device) {
        switch (device.getDeviceType()) {
            case SMS:
                validateSmsDeviceSecurity(device);
                break;
            case EMAIL:
                validateEmailDeviceSecurity(device);
                break;
            case TOTP:
                validateTotpDeviceSecurity(device);
                break;
        }
    }
    
    private void validateSmsDeviceSecurity(final MfaDevice device) {
        // 检查手机号是否为虚拟号码
        if (isVirtualPhoneNumber(device.getPhoneNumber())) {
            throw new SecurityException("不允许使用虚拟手机号");
        }
        
        // 检查号码归属地
        final PhoneLocation location = getPhoneLocation(device.getPhoneNumber());
        if (!isTrustedLocation(location)) {
            log.warn("用户 {} 使用非信任地区手机号: {}", 
                device.getUserId(), location.getCountry());
        }
    }
}
```

### 2. MFA会话管理

```java
@Component
public class MfaSessionManager {
    
    private static final String MFA_SESSION_PREFIX = "mfa:session:";
    private static final int MFA_SESSION_TIMEOUT = 8 * 60 * 60; // 8小时
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 创建MFA会话
     */
    public String createMfaSession(final Long userId) {
        final String sessionId = UUID.randomUUID().toString();
        final String key = MFA_SESSION_PREFIX + sessionId;
        
        final MfaSession session = MfaSession.builder()
            .userId(userId)
            .sessionId(sessionId)
            .createdAt(LocalDateTime.now())
            .expiresAt(LocalDateTime.now().plusSeconds(MFA_SESSION_TIMEOUT))
            .ipAddress(getCurrentIpAddress())
            .userAgent(getCurrentUserAgent())
            .build();
        
        redisTemplate.opsForValue().set(key, session, MFA_SESSION_TIMEOUT, TimeUnit.SECONDS);
        
        return sessionId;
    }
    
    /**
     * 验证MFA会话
     */
    public boolean validateMfaSession(final String sessionId, final Long userId) {
        final String key = MFA_SESSION_PREFIX + sessionId;
        final MfaSession session = (MfaSession) redisTemplate.opsForValue().get(key);
        
        if (session == null) {
            return false;
        }
        
        if (!session.getUserId().equals(userId)) {
            return false;
        }
        
        if (session.getExpiresAt().isBefore(LocalDateTime.now())) {
            redisTemplate.delete(key);
            return false;
        }
        
        return true;
    }
    
    /**
     * 延长MFA会话
     */
    public void extendMfaSession(final String sessionId) {
        final String key = MFA_SESSION_PREFIX + sessionId;
        redisTemplate.expire(key, MFA_SESSION_TIMEOUT, TimeUnit.SECONDS);
    }
    
    /**
     * 销毁MFA会话
     */
    public void destroyMfaSession(final String sessionId) {
        final String key = MFA_SESSION_PREFIX + sessionId;
        redisTemplate.delete(key);
    }
}
```

## 📊 MFA监控和分析

### MFA使用统计

```java
@Service
public class MfaAnalyticsService {
    
    /**
     * 生成MFA使用报告
     */
    public MfaUsageReport generateMfaReport(final LocalDate startDate, 
                                          final LocalDate endDate) {
        return MfaUsageReport.builder()
            .totalMfaAttempts(getTotalMfaAttempts(startDate, endDate))
            .successfulVerifications(getSuccessfulVerifications(startDate, endDate))
            .failedVerifications(getFailedVerifications(startDate, endDate))
            .deviceTypeDistribution(getDeviceTypeDistribution(startDate, endDate))
            .userAdoptionRate(calculateUserAdoptionRate())
            .securityIncidents(getSecurityIncidents(startDate, endDate))
            .build();
    }
    
    /**
     * 检测异常MFA模式
     */
    @Scheduled(fixedDelay = 300000) // 5分钟
    public void detectAnomalousPatterns() {
        // 检测大量失败尝试
        final List<AnomalousUser> suspiciousUsers = findUsersWithExcessiveFailures();
        
        for (final AnomalousUser user : suspiciousUsers) {
            log.warn("检测到用户 {} 存在异常MFA行为: {}", 
                user.getUserId(), user.getDescription());
            
            // 触发安全警报
            securityAlertService.createAlert(
                AlertType.MFA_ANOMALY,
                user.getUserId(),
                user.getDescription()
            );
        }
        
        // 检测设备克隆或共享
        detectDeviceAnomalies();
    }
}
```

## 📞 相关资源

- [认证系统总览](./index.md) - 整体认证架构
- [JWT Token管理](./jwt.md) - Token实现
- [权限控制系统](./rbac.md) - RBAC实现方案
- [安全规范](../../development/quality/security/) - 安全最佳实践

---

*最后更新：2025-07-01*