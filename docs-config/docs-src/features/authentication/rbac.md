# 🔐 权限控制系统 (RBAC)

## 🎯 RBAC概述

智慧养老评估平台采用基于角色的访问控制(Role-Based Access Control, RBAC)模型，提供灵活、安全、可扩展的权限管理体系。

## 🏗️ RBAC架构设计

### 核心概念
- **用户(User)**: 系统使用者
- **角色(Role)**: 权限的集合
- **权限(Permission)**: 对资源的操作许可
- **资源(Resource)**: 系统中受保护的对象

### 权限模型
```
用户 ←→ 用户角色 ←→ 角色 ←→ 角色权限 ←→ 权限
  ↓                    ↓                      ↓
组织机构              角色层次              资源操作
```

## 🗄️ 数据模型设计

### 核心实体关系
```sql
-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    organization_id BIGINT REFERENCES organizations(id)
);

-- 角色表
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    level INTEGER DEFAULT 0, -- 角色层级
    organization_id BIGINT REFERENCES organizations(id),
    UNIQUE(name, organization_id)
);

-- 权限表
CREATE TABLE permissions (
    id BIGSERIAL PRIMARY KEY,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    UNIQUE(resource, action)
);

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    role_id BIGINT NOT NULL REFERENCES roles(id),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by BIGINT REFERENCES users(id),
    expires_at TIMESTAMP,
    UNIQUE(user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL REFERENCES roles(id),
    permission_id BIGINT NOT NULL REFERENCES permissions(id),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by BIGINT REFERENCES users(id),
    UNIQUE(role_id, permission_id)
);

-- 角色继承关系表
CREATE TABLE role_hierarchy (
    id BIGSERIAL PRIMARY KEY,
    parent_role_id BIGINT NOT NULL REFERENCES roles(id),
    child_role_id BIGINT NOT NULL REFERENCES roles(id),
    UNIQUE(parent_role_id, child_role_id)
);
```

## 🔧 后端实现

### 权限实体类
```java
@Entity
@Table(name = "permissions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Permission {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "resource", nullable = false, length = 100)
    private String resource;
    
    @Column(name = "action", nullable = false, length = 50)
    private String action;
    
    @Column(name = "description")
    private String description;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 获取权限标识符
     */
    public String getIdentifier() {
        return resource + ":" + action;
    }
}

@Entity
@Table(name = "roles")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Role {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false, length = 50)
    private String name;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "level")
    private Integer level = 0;
    
    @Column(name = "organization_id")
    private Long organizationId;
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "role_hierarchy",
        joinColumns = @JoinColumn(name = "parent_role_id"),
        inverseJoinColumns = @JoinColumn(name = "child_role_id")
    )
    private Set<Role> childRoles = new HashSet<>();
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
}
```

### 权限服务实现
```java
@Service
@Transactional
@Slf4j
public class PermissionService {
    
    @Autowired
    private PermissionRepository permissionRepository;
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private UserRoleRepository userRoleRepository;
    
    /**
     * 检查用户是否具有指定权限
     */
    public boolean hasPermission(final Long userId, final String resource, final String action) {
        final String permission = resource + ":" + action;
        return getUserPermissions(userId).contains(permission);
    }
    
    /**
     * 获取用户所有权限
     */
    public Set<String> getUserPermissions(final Long userId) {
        final Set<String> permissions = new HashSet<>();
        
        // 获取用户所有角色
        final List<Role> userRoles = getUserRoles(userId);
        
        // 收集所有权限（包括继承的权限）
        for (final Role role : userRoles) {
            permissions.addAll(getRolePermissions(role));
        }
        
        return permissions;
    }
    
    /**
     * 获取角色所有权限（包括继承的权限）
     */
    private Set<String> getRolePermissions(final Role role) {
        final Set<String> permissions = new HashSet<>();
        
        // 直接权限
        role.getPermissions().forEach(permission -> 
            permissions.add(permission.getIdentifier())
        );
        
        // 继承权限
        role.getChildRoles().forEach(childRole -> 
            permissions.addAll(getRolePermissions(childRole))
        );
        
        return permissions;
    }
    
    /**
     * 获取用户角色
     */
    private List<Role> getUserRoles(final Long userId) {
        return userRoleRepository.findActiveRolesByUserId(userId);
    }
    
    /**
     * 为用户分配角色
     */
    public void assignRoleToUser(final Long userId, final Long roleId, final Long grantedBy) {
        final UserRole userRole = UserRole.builder()
            .userId(userId)
            .roleId(roleId)
            .grantedBy(grantedBy)
            .grantedAt(LocalDateTime.now())
            .build();
            
        userRoleRepository.save(userRole);
        
        log.info("角色分配成功: userId={}, roleId={}, grantedBy={}", userId, roleId, grantedBy);
    }
    
    /**
     * 撤销用户角色
     */
    public void revokeRoleFromUser(final Long userId, final Long roleId) {
        userRoleRepository.deleteByUserIdAndRoleId(userId, roleId);
        
        log.info("角色撤销成功: userId={}, roleId={}", userId, roleId);
    }
    
    /**
     * 为角色分配权限
     */
    public void assignPermissionToRole(final Long roleId, final Long permissionId, final Long grantedBy) {
        final Role role = roleRepository.findById(roleId)
            .orElseThrow(() -> new NotFoundException("角色不存在"));
        
        final Permission permission = permissionRepository.findById(permissionId)
            .orElseThrow(() -> new NotFoundException("权限不存在"));
        
        role.getPermissions().add(permission);
        roleRepository.save(role);
        
        log.info("权限分配成功: roleId={}, permissionId={}, grantedBy={}", roleId, permissionId, grantedBy);
    }
    
    /**
     * 检查权限层次合规性
     */
    public boolean validatePermissionHierarchy(final Long userId, final String targetPermission) {
        final Set<String> userPermissions = getUserPermissions(userId);
        
        // 超级管理员拥有所有权限
        if (userPermissions.contains("SYSTEM:ADMIN")) {
            return true;
        }
        
        // 组织管理员只能管理本组织权限
        if (targetPermission.startsWith("ORG:") && userPermissions.contains("ORG:ADMIN")) {
            return true;
        }
        
        // 普通权限检查
        return userPermissions.contains(targetPermission);
    }
}
```

### Spring Security集成
```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    
    @Autowired
    private PermissionService permissionService;
    
    @Bean
    public PermissionEvaluator permissionEvaluator() {
        return new CustomPermissionEvaluator(permissionService);
    }
    
    @Bean
    public MethodSecurityExpressionHandler methodSecurityExpressionHandler() {
        final DefaultMethodSecurityExpressionHandler handler = new DefaultMethodSecurityExpressionHandler();
        handler.setPermissionEvaluator(permissionEvaluator());
        return handler;
    }
}

@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {
    
    private final PermissionService permissionService;
    
    public CustomPermissionEvaluator(final PermissionService permissionService) {
        this.permissionService = permissionService;
    }
    
    @Override
    public boolean hasPermission(final Authentication authentication, 
                               final Object targetDomainObject, 
                               final Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        final Long userId = getCurrentUserId(authentication);
        final String permissionStr = permission.toString();
        final String[] parts = permissionStr.split(":");
        
        if (parts.length != 2) {
            return false;
        }
        
        return permissionService.hasPermission(userId, parts[0], parts[1]);
    }
    
    @Override
    public boolean hasPermission(final Authentication authentication, 
                               final Serializable targetId, 
                               final String targetType, 
                               final Object permission) {
        return hasPermission(authentication, null, permission);
    }
    
    private Long getCurrentUserId(final Authentication authentication) {
        final UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        // 从UserDetails中获取用户ID
        return ((CustomUserDetails) userDetails).getUserId();
    }
}
```

### 权限注解
```java
// 方法级权限控制
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping
    @PreAuthorize("hasPermission(null, 'USER:READ')")
    public List<User> getUsers() {
        return userService.getAllUsers();
    }
    
    @PostMapping
    @PreAuthorize("hasPermission(null, 'USER:CREATE')")
    public User createUser(@RequestBody final CreateUserRequest request) {
        return userService.createUser(request);
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasPermission(#id, 'USER:UPDATE')")
    public User updateUser(@PathVariable final Long id, 
                          @RequestBody final UpdateUserRequest request) {
        return userService.updateUser(id, request);
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasPermission(#id, 'USER:DELETE')")
    public void deleteUser(@PathVariable final Long id) {
        userService.deleteUser(id);
    }
}

// 自定义权限注解
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@PreAuthorize("hasPermission(null, #resource + ':' + #action)")
public @interface RequiresPermission {
    String resource();
    String action();
}

// 使用自定义注解
@RequiresPermission(resource = "ASSESSMENT", action = "CREATE")
public Assessment createAssessment(final CreateAssessmentRequest request) {
    return assessmentService.createAssessment(request);
}
```

## 🎨 前端权限控制

### 权限指令
```typescript
// directives/permission.ts
import { Directive, DirectiveBinding } from 'vue'
import { useAuth } from '@/stores/composables/useAuth'

interface PermissionBinding {
  resource: string
  action: string
  mode?: 'hide' | 'disable' // 默认hide
}

export const vPermission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<string | PermissionBinding>) {
    const { checkPermission } = useAuth()
    
    let hasAccess = false
    
    if (typeof binding.value === 'string') {
      hasAccess = checkPermission(binding.value)
    } else {
      const { resource, action, mode = 'hide' } = binding.value
      hasAccess = checkPermission(`${resource}:${action}`)
      
      if (!hasAccess && mode === 'disable') {
        el.setAttribute('disabled', 'true')
        el.style.opacity = '0.5'
        el.style.cursor = 'not-allowed'
        return
      }
    }
    
    if (!hasAccess) {
      el.style.display = 'none'
    }
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding<string | PermissionBinding>) {
    // 权限变化时重新检查
    const { checkPermission } = useAuth()
    
    let hasAccess = false
    
    if (typeof binding.value === 'string') {
      hasAccess = checkPermission(binding.value)
    } else {
      const { resource, action } = binding.value
      hasAccess = checkPermission(`${resource}:${action}`)
    }
    
    el.style.display = hasAccess ? '' : 'none'
  }
}
```

### 权限组件
```vue
<!-- components/PermissionWrapper.vue -->
<template>
  <div v-if="hasAccess" :class="wrapperClass">
    <slot />
  </div>
  <div v-else-if="showFallback" class="permission-denied">
    <slot name="fallback">
      <div class="text-center py-8 text-gray-500">
        <i class="el-icon-lock text-4xl mb-2"></i>
        <p>您没有权限访问此内容</p>
      </div>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuth } from '@/stores/composables/useAuth'

interface Props {
  permission?: string
  resource?: string
  action?: string
  any?: string[] // 满足任一权限即可
  all?: string[] // 必须满足所有权限
  showFallback?: boolean
  wrapperClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  showFallback: false,
  wrapperClass: ''
})

const { checkPermission, hasAnyPermission, hasAllPermissions } = useAuth()

const hasAccess = computed(() => {
  // 检查单个权限
  if (props.permission) {
    return checkPermission(props.permission)
  }
  
  // 检查资源:操作权限
  if (props.resource && props.action) {
    return checkPermission(`${props.resource}:${props.action}`)
  }
  
  // 检查任一权限
  if (props.any && props.any.length > 0) {
    return hasAnyPermission(...props.any)
  }
  
  // 检查所有权限
  if (props.all && props.all.length > 0) {
    return hasAllPermissions(...props.all)
  }
  
  return true
})
</script>
```

### 路由权限守卫
```typescript
// router/guards/permission.ts
import { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useAuth } from '@/stores/composables/useAuth'

export function createPermissionGuard() {
  return (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) => {
    const { isAuthenticated, checkPermission } = useAuth()
    
    // 未登录用户重定向到登录页
    if (!isAuthenticated.value) {
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
      return
    }
    
    // 检查路由权限
    const requiredPermission = to.meta.permission as string
    if (requiredPermission && !checkPermission(requiredPermission)) {
      // 无权限时显示403页面
      next('/403')
      return
    }
    
    // 检查角色权限
    const requiredRoles = to.meta.roles as string[]
    if (requiredRoles && requiredRoles.length > 0) {
      const { currentUser } = useAuth()
      const userRoles = currentUser.value?.roles.map(role => role.name) || []
      
      if (!requiredRoles.some(role => userRoles.includes(role))) {
        next('/403')
        return
      }
    }
    
    next()
  }
}

// 路由配置示例
const routes = [
  {
    path: '/users',
    component: () => import('@/views/UserManagement.vue'),
    meta: {
      permission: 'USER:READ',
      title: '用户管理'
    }
  },
  {
    path: '/admin',
    component: () => import('@/views/AdminPanel.vue'),
    meta: {
      roles: ['ADMIN', 'SUPER_ADMIN'],
      title: '管理面板'
    }
  }
]
```

## 📊 权限管理界面

### 角色管理组件
```vue
<!-- views/RoleManagement.vue -->
<template>
  <div class="role-management">
    <PageHeader title="角色管理">
      <template #actions>
        <BaseButton 
          type="primary" 
          @click="showCreateDialog = true"
          v-permission="'ROLE:CREATE'"
        >
          新建角色
        </BaseButton>
      </template>
    </PageHeader>
    
    <div class="role-list">
      <el-table :data="roles" style="width: 100%">
        <el-table-column prop="name" label="角色名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="level" label="层级" />
        <el-table-column label="权限数量">
          <template #default="{ row }">
            {{ row.permissions?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <BaseButton 
              size="small" 
              @click="editRole(row)"
              v-permission="'ROLE:UPDATE'"
            >
              编辑
            </BaseButton>
            <BaseButton 
              size="small" 
              @click="managePermissions(row)"
              v-permission="'ROLE:PERMISSION'"
            >
              权限
            </BaseButton>
            <BaseButton 
              size="small" 
              type="danger" 
              @click="deleteRole(row)"
              v-permission="'ROLE:DELETE'"
            >
              删除
            </BaseButton>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 权限分配对话框 -->
    <PermissionAssignDialog
      v-model="showPermissionDialog"
      :role="currentRole"
      @confirm="handlePermissionAssign"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElMessage } from 'element-plus'
import PageHeader from '@/components/layout/PageHeader.vue'
import BaseButton from '@/components/base/BaseButton.vue'
import PermissionAssignDialog from './components/PermissionAssignDialog.vue'
import { useRoleStore } from '@/stores/modules/role'

const roleStore = useRoleStore()
const roles = ref([])
const showCreateDialog = ref(false)
const showPermissionDialog = ref(false)
const currentRole = ref(null)

onMounted(async () => {
  await loadRoles()
})

async function loadRoles() {
  roles.value = await roleStore.fetchRoles()
}

function editRole(role) {
  currentRole.value = role
  showCreateDialog.value = true
}

function managePermissions(role) {
  currentRole.value = role
  showPermissionDialog.value = true
}

async function deleteRole(role) {
  await roleStore.deleteRole(role.id)
  await loadRoles()
  ElMessage.success('角色删除成功')
}

async function handlePermissionAssign(permissions) {
  await roleStore.assignPermissions(currentRole.value.id, permissions)
  await loadRoles()
  ElMessage.success('权限分配成功')
}
</script>
```

## 🔒 安全最佳实践

### 1. 最小权限原则
```java
// 权限设计原则
public class PermissionPrinciples {
    
    // 最小权限：用户只获得完成工作所需的最小权限
    public void assignMinimalPermissions(final Long userId, final String jobFunction) {
        final Set<Permission> minimalPerms = getMinimalPermissionsFor(jobFunction);
        assignPermissionsToUser(userId, minimalPerms);
    }
    
    // 职责分离：关键操作需要多人协作
    @RequiresDualApproval
    public void deleteUser(final Long userId) {
        // 需要两个管理员审批
    }
    
    // 权限审计：定期审查权限分配
    @Scheduled(cron = "0 0 0 1 * ?") // 每月审计
    public void auditPermissions() {
        final List<User> users = getAllUsers();
        for (final User user : users) {
            auditUserPermissions(user);
        }
    }
}
```

### 2. 动态权限检查
```java
@Component
public class DynamicPermissionChecker {
    
    /**
     * 基于数据的动态权限检查
     */
    public boolean canAccessData(final Long userId, final Object data) {
        // 检查数据所有权
        if (data instanceof OwnedData) {
            final OwnedData ownedData = (OwnedData) data;
            if (ownedData.getOwnerId().equals(userId)) {
                return true;
            }
        }
        
        // 检查组织权限
        if (data instanceof OrganizationData) {
            final OrganizationData orgData = (OrganizationData) data;
            return isSameOrganization(userId, orgData.getOrganizationId());
        }
        
        // 检查时间窗口权限
        if (data instanceof TimeRestrictedData) {
            final TimeRestrictedData timeData = (TimeRestrictedData) data;
            return isWithinAccessWindow(timeData.getAccessWindow());
        }
        
        return false;
    }
}
```

## 📊 权限监控和审计

### 权限使用监控
```java
@Component
public class PermissionAuditService {
    
    @EventListener
    public void handlePermissionCheck(final PermissionCheckEvent event) {
        final AuditLog log = AuditLog.builder()
            .userId(event.getUserId())
            .permission(event.getPermission())
            .resource(event.getResource())
            .action(event.getAction())
            .result(event.isGranted() ? "GRANTED" : "DENIED")
            .timestamp(LocalDateTime.now())
            .ipAddress(event.getIpAddress())
            .userAgent(event.getUserAgent())
            .build();
            
        auditLogRepository.save(log);
    }
    
    /**
     * 生成权限使用报告
     */
    public PermissionUsageReport generateUsageReport(final LocalDate startDate, 
                                                   final LocalDate endDate) {
        return PermissionUsageReport.builder()
            .totalChecks(getTotalPermissionChecks(startDate, endDate))
            .deniedAttempts(getDeniedAttempts(startDate, endDate))
            .topPermissions(getTopUsedPermissions(startDate, endDate))
            .suspiciousActivity(getSuspiciousActivity(startDate, endDate))
            .build();
    }
}
```

## 📞 相关资源

- [认证系统总览](./index.md) - 整体认证架构
- [JWT Token管理](./jwt.md) - Token实现
- [多因子认证](./mfa.md) - MFA实现方案
- [安全规范](../../development/quality/security/) - 安全最佳实践

---

*最后更新：2025-07-01*