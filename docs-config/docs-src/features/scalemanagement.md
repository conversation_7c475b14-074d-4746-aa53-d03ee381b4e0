# 量表管理功能文档

**功能名称**: Scale Management (量表管理)  
**创建日期**: 2025-07-02  
**文档版本**: v1.0  
**负责人**: 开发团队  

## 📋 功能概述

量表管理功能是智慧养老评估平台的核心业务模块，提供完整的量表生命周期管理，包括创建、测试、部署和维护。

### 核心特性
- 📊 量表创建和编辑管理
- 🔄 双数据库部署机制
- 🧪 三阶段测试验证
- 📈 量表性能监控
- 🔒 多租户数据隔离

## 🛠️ 技术实现

### 后端实现

#### 核心类结构
```java
// Controller层
@RestController
@RequestMapping("/api/scale-management")
public class ScaleManagementController {
    
    @PostMapping("/execute-ddl")
    public ResponseEntity<ExecuteDDLResult> executeDDL(@RequestBody final DDLRequest request);
    
    @PostMapping("/migrate")
    public ResponseEntity<MigrationResult> migrateToProduction(@RequestBody final MigrationRequest request);
    
    @GetMapping("/validate-structure")
    public ResponseEntity<ValidationResult> validateTableStructure(@RequestParam final String tableName);
    
    @PostMapping("/rollback")
    public ResponseEntity<RollbackResult> rollbackMigration(@RequestBody final RollbackRequest request);
}

// Service层
@Service
public class EnhancedDatabaseService {
    // 双数据库操作
    public ExecuteDDLResult executeDDLInStaging(final String sql);
    public MigrationResult migrateToProduction(final String tableName);
    
    // 结构验证
    public ValidationResult validateTableStructure(final String tableName);
    
    // 回滚操作
    public RollbackResult performRollback(final String tableName, final String backupId);
}
```

#### 数据模型
```java
// DDL执行请求
@Data
@Builder
public class DDLRequest {
    @NotBlank(message = "SQL语句不能为空")
    private String sql;
    
    @Builder.Default
    private Boolean validateOnly = false;
    
    @Builder.Default
    private String targetDatabase = "staging";
}

// 迁移请求
@Data
@Builder
public class MigrationRequest {
    @NotBlank(message = "表名不能为空")
    private String tableName;
    
    @Builder.Default
    private Boolean createBackup = true;
    
    @Builder.Default
    private Boolean validateBefore = true;
}

// 迁移结果
@Data
@Builder
public class MigrationResult {
    private boolean success;
    private String message;
    private String backupId;
    private LocalDateTime executedAt;
    private Map<String, Object> details;
}
```

### 前端实现

#### 组件结构
```typescript
// 量表管理主组件
interface ScaleManagementProps {
  tenantId: string
  permissions: Permission[]
  onScaleCreated: (scale: Scale) => void
}

// 状态管理
interface ScaleManagementState {
  scales: Scale[]
  selectedScale: Scale | null
  testResults: TestResult[]
  migrationStatus: MigrationStatus
  loading: LoadingState
}
```

#### API调用
```typescript
// API服务
export const scaleManagementApi = {
  // DDL操作
  executeDDL: (request: DDLRequest) => 
    request.post('/api/scale-management/execute-ddl', request),
    
  // 迁移操作
  migrateToProduction: (request: MigrationRequest) => 
    request.post('/api/scale-management/migrate', request),
    
  // 验证操作
  validateStructure: (tableName: string) => 
    request.get(`/api/scale-management/validate-structure?tableName=${tableName}`),
    
  // 回滚操作
  rollback: (request: RollbackRequest) => 
    request.post('/api/scale-management/rollback', request)
}
```

## 🔧 关键技术点

### 双数据库架构
- **临时数据库**: 用于开发测试，端口5433，数据库名`assessment_staging`
- **生产数据库**: 正式环境，端口5433，数据库名`assessment_multitenant`
- 数据同步机制和一致性保证

### 三阶段测试系统
1. **结构测试**: 验证表结构完整性、字段类型、约束条件
2. **数据填入测试**: 批量数据插入、CRUD操作验证
3. **性能测试**: 并发访问、大数据量处理、响应时间监控

### 迁移管理
- 自动备份机制
- 版本控制和回滚
- 迁移状态跟踪
- 失败恢复策略

### 多租户支持
- 租户数据隔离
- 权限验证机制
- 资源配额管理
- 数据访问审计

## 📈 核心工作流程

### 1. 量表创建流程
```mermaid
graph LR
    A[量表设计] --> B[AI生成SQL]
    B --> C[临时库部署]
    C --> D[结构测试]
    D --> E[数据测试]
    E --> F[性能测试]
    F --> G[生产部署]
```

### 2. 测试验证流程
```java
// 测试执行示例
@Service
public class ScaleTestingService {
    
    public ScaleTestResult executeFullTest(final ScaleTestRequest request) {
        // 第一阶段：结构测试
        StructureTestResult structureResult = testTableStructure(request);
        
        // 第二阶段：数据填入测试
        DataFillTestResult dataResult = testDataOperations(request);
        
        // 第三阶段：性能测试
        PerformanceTestResult perfResult = testPerformance(request);
        
        return ScaleTestResult.builder()
            .structureTest(structureResult)
            .dataFillTest(dataResult)
            .performanceTest(perfResult)
            .overallSuccess(allTestsPassed(structureResult, dataResult, perfResult))
            .build();
    }
}
```

### 3. 迁移部署流程
```java
// 迁移执行示例
public MigrationResult migrateToProduction(final MigrationRequest request) {
    try {
        // 1. 创建备份
        String backupId = createProductionBackup(request.getTableName());
        
        // 2. 预迁移验证
        if (request.getValidateBefore()) {
            ValidationResult validation = validateTableStructure(request.getTableName());
            if (!validation.isValid()) {
                return MigrationResult.failure("预验证失败: " + validation.getErrors());
            }
        }
        
        // 3. 执行迁移
        executeMigration(request);
        
        // 4. 后验证
        ValidationResult postValidation = validateMigrationResult(request.getTableName());
        
        return MigrationResult.success(backupId);
        
    } catch (Exception e) {
        // 自动回滚
        performAutoRollback(request.getTableName());
        return MigrationResult.failure("迁移失败: " + e.getMessage());
    }
}
```

## 🎯 使用场景

### 量表开发场景
- 新量表创建和测试
- 现有量表结构调整
- 量表版本升级管理

### 运维管理场景
- 生产环境数据库维护
- 性能问题诊断优化
- 数据备份和恢复

### 多租户场景
- 租户专用量表管理
- 数据隔离验证
- 权限控制测试

## 📝 配置说明

### 数据库配置
```yaml
# 生产数据库配置
spring:
  datasource:
    url: *******************************************************
    username: assessment_user
    password: ${DB_PASSWORD:assessment123}

# 临时数据库配置  
assessment:
  database:
    staging:
      url: ***************************************************
      username: ${spring.datasource.username}
      password: ${spring.datasource.password}
```

### 测试配置
```yaml
# 测试参数配置
scale-testing:
  structure-test:
    timeout: 30000
    max-retries: 3
  
  data-fill-test:
    batch-size: 1000
    max-records: 10000
    
  performance-test:
    concurrent-users: 50
    test-duration: 300
    response-threshold: 1000
```

## 🔍 监控指标

### 业务指标
- 量表创建成功率: ≥ 95%
- 测试通过率: ≥ 90%
- 迁移成功率: ≥ 98%

### 性能指标
- DDL执行时间: ≤ 30秒
- 迁移执行时间: ≤ 5分钟
- 回滚执行时间: ≤ 2分钟

### 质量指标
- 数据一致性: 100%
- 备份完整性: 100%
- 权限验证准确性: 100%

## 🚨 注意事项

### 安全考虑
- 所有DDL操作需要权限验证
- 生产数据库操作需要审批流程
- 敏感数据访问需要审计日志

### 性能考虑
- 大表迁移需要分批处理
- 高峰期避免执行重大变更
- 监控资源使用情况

### 灾难恢复
- 定期备份验证
- 回滚流程测试
- 应急响应预案

## 🚀 未来规划

### 短期目标 (1-3个月)
- 支持在线DDL操作
- 增加更多测试类型
- 优化迁移性能

### 长期目标 (3-6个月)
- 自动化运维流程
- 智能故障诊断
- 多云部署支持

---

**更新记录**:
- 2025-07-02: 初始版本创建
- 完整功能实现和文档编写

**相关文档**:
- [数据库配置文档](../configuration/database-config.md)
- [测试规范文档](../testing/scale-testing-guide.md)
- [运维手册](../operations/scale-management-ops.md)