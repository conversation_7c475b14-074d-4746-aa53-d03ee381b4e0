# 🔄 前端状态管理

## 🎯 状态管理概述

智慧养老评估平台前端采用Pinia作为状态管理解决方案，提供类型安全、模块化和响应式的状态管理体验。

## 🏗️ Pinia架构设计

### 状态管理分层
```
stores/
├── modules/                    # 模块化Store
│   ├── auth.ts               # 认证状态
│   ├── user.ts               # 用户状态
│   ├── assessment.ts         # 评估状态
│   ├── organization.ts       # 组织状态
│   └── config.ts             # 配置状态
├── composables/              # 组合式函数
│   ├── useAuth.ts           
│   ├── useUser.ts
│   └── useAssessment.ts
├── plugins/                  # Pinia插件
│   ├── persist.ts           # 持久化插件
│   └── logger.ts            # 日志插件
└── index.ts                 # Store导出
```

## 🛠️ Store实现

### 认证Store
```typescript
// stores/modules/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { request } from '@shared/utils/request'
import { API_ENDPOINTS } from '@shared/constants/api'
import type { User, LoginRequest, LoginResponse } from '@shared/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const currentUser = ref<User | null>(null)
  const loginLoading = ref(false)
  const loginError = ref<string | null>(null)
  
  // 计算属性
  const isAuthenticated = computed(() => !!accessToken.value)
  const isAdmin = computed(() => 
    currentUser.value?.roles.some(role => role.name === 'ADMIN') ?? false
  )
  const permissions = computed(() => {
    const perms = new Set<string>()
    currentUser.value?.roles.forEach(role => {
      role.permissions.forEach(permission => {
        perms.add(`${permission.resource}:${permission.action}`)
      })
    })
    return Array.from(perms)
  })
  
  // Actions
  async function login(credentials: LoginRequest) {
    loginLoading.value = true
    loginError.value = null
    
    try {
      const response = await request.post<LoginResponse>(
        API_ENDPOINTS.AUTH.LOGIN, 
        credentials
      )
      
      accessToken.value = response.accessToken
      refreshToken.value = response.refreshToken
      currentUser.value = response.user
      
      // 保存到本地存储
      localStorage.setItem('access_token', response.accessToken)
      localStorage.setItem('refresh_token', response.refreshToken)
      
      return response
    } catch (error: any) {
      loginError.value = error.response?.data?.message || '登录失败'
      throw error
    } finally {
      loginLoading.value = false
    }
  }
  
  async function logout() {
    try {
      await request.post(API_ENDPOINTS.AUTH.LOGOUT)
    } finally {
      // 清除状态
      accessToken.value = null
      refreshToken.value = null
      currentUser.value = null
      
      // 清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }
  
  async function refreshAccessToken() {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }
    
    try {
      const response = await request.post<LoginResponse>(
        API_ENDPOINTS.AUTH.REFRESH,
        { refreshToken: refreshToken.value }
      )
      
      accessToken.value = response.accessToken
      localStorage.setItem('access_token', response.accessToken)
      
      return response.accessToken
    } catch (error) {
      // 刷新失败，清除认证状态
      await logout()
      throw error
    }
  }
  
  async function fetchCurrentUser() {
    if (!isAuthenticated.value) {
      throw new Error('Not authenticated')
    }
    
    const user = await request.get<User>(API_ENDPOINTS.AUTH.ME)
    currentUser.value = user
    return user
  }
  
  // 权限检查
  function hasPermission(resource: string, action: string): boolean {
    return permissions.value.includes(`${resource}:${action}`)
  }
  
  function hasAnyPermission(...perms: string[]): boolean {
    return perms.some(perm => permissions.value.includes(perm))
  }
  
  function hasAllPermissions(...perms: string[]): boolean {
    return perms.every(perm => permissions.value.includes(perm))
  }
  
  // 初始化
  function initializeAuth() {
    const token = localStorage.getItem('access_token')
    const refresh = localStorage.getItem('refresh_token')
    
    if (token && refresh) {
      accessToken.value = token
      refreshToken.value = refresh
      // 自动获取用户信息
      fetchCurrentUser().catch(() => {
        // 获取失败，清除认证状态
        logout()
      })
    }
  }
  
  return {
    // 状态
    accessToken,
    refreshToken,
    currentUser,
    loginLoading,
    loginError,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    permissions,
    
    // Actions
    login,
    logout,
    refreshAccessToken,
    fetchCurrentUser,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    initializeAuth
  }
})
```

### 评估Store
```typescript
// stores/modules/assessment.ts
import { defineStore } from 'pinia'
import { ref, computed, shallowRef } from 'vue'
import { request } from '@shared/utils/request'
import type { Assessment, AssessmentTemplate, CreateAssessmentRequest } from '@shared/types'

export const useAssessmentStore = defineStore('assessment', () => {
  // 状态
  const assessments = ref<Assessment[]>([])
  const currentAssessment = shallowRef<Assessment | null>(null)
  const templates = ref<AssessmentTemplate[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 过滤和排序
  const filters = ref({
    status: 'ALL',
    startDate: null as Date | null,
    endDate: null as Date | null,
    elderlyId: null as number | null,
    assessorId: null as number | null
  })
  
  const sortBy = ref<'date' | 'score'>('date')
  const sortOrder = ref<'asc' | 'desc'>('desc')
  
  // 计算属性
  const filteredAssessments = computed(() => {
    let result = [...assessments.value]
    
    // 应用过滤
    if (filters.value.status !== 'ALL') {
      result = result.filter(a => a.status === filters.value.status)
    }
    
    if (filters.value.startDate) {
      result = result.filter(a => 
        new Date(a.assessmentDate) >= filters.value.startDate!
      )
    }
    
    if (filters.value.endDate) {
      result = result.filter(a => 
        new Date(a.assessmentDate) <= filters.value.endDate!
      )
    }
    
    if (filters.value.elderlyId) {
      result = result.filter(a => a.elderlyId === filters.value.elderlyId)
    }
    
    if (filters.value.assessorId) {
      result = result.filter(a => a.assessorId === filters.value.assessorId)
    }
    
    // 应用排序
    result.sort((a, b) => {
      let compareValue = 0
      
      if (sortBy.value === 'date') {
        compareValue = new Date(a.assessmentDate).getTime() - 
                      new Date(b.assessmentDate).getTime()
      } else if (sortBy.value === 'score') {
        compareValue = (a.totalScore || 0) - (b.totalScore || 0)
      }
      
      return sortOrder.value === 'asc' ? compareValue : -compareValue
    })
    
    return result
  })
  
  const statistics = computed(() => {
    const total = assessments.value.length
    const completed = assessments.value.filter(a => a.status === 'COMPLETED').length
    const inProgress = assessments.value.filter(a => a.status === 'IN_PROGRESS').length
    const avgScore = assessments.value
      .filter(a => a.status === 'COMPLETED' && a.totalScore)
      .reduce((sum, a) => sum + (a.totalScore || 0), 0) / (completed || 1)
    
    return {
      total,
      completed,
      inProgress,
      completionRate: total > 0 ? (completed / total) * 100 : 0,
      avgScore: Math.round(avgScore * 10) / 10
    }
  })
  
  // Actions
  async function fetchAssessments(params?: {
    page?: number
    size?: number
    organizationId?: number
  }) {
    loading.value = true
    error.value = null
    
    try {
      const response = await request.get<{
        content: Assessment[]
        totalElements: number
        totalPages: number
      }>('/api/assessments', { params })
      
      assessments.value = response.content
      return response
    } catch (err: any) {
      error.value = err.message || '获取评估列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  async function fetchAssessmentById(id: number) {
    loading.value = true
    error.value = null
    
    try {
      const assessment = await request.get<Assessment>(`/api/assessments/${id}`)
      currentAssessment.value = assessment
      return assessment
    } catch (err: any) {
      error.value = err.message || '获取评估详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  async function createAssessment(data: CreateAssessmentRequest) {
    loading.value = true
    error.value = null
    
    try {
      const assessment = await request.post<Assessment>('/api/assessments', data)
      assessments.value.unshift(assessment)
      return assessment
    } catch (err: any) {
      error.value = err.message || '创建评估失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  async function updateAssessment(id: number, data: Partial<Assessment>) {
    loading.value = true
    error.value = null
    
    try {
      const updated = await request.put<Assessment>(`/api/assessments/${id}`, data)
      
      // 更新列表中的数据
      const index = assessments.value.findIndex(a => a.id === id)
      if (index !== -1) {
        assessments.value[index] = updated
      }
      
      // 更新当前评估
      if (currentAssessment.value?.id === id) {
        currentAssessment.value = updated
      }
      
      return updated
    } catch (err: any) {
      error.value = err.message || '更新评估失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  async function submitAssessment(id: number, answers: Record<string, any>) {
    return updateAssessment(id, {
      answers,
      status: 'COMPLETED',
      completedAt: new Date().toISOString()
    })
  }
  
  async function fetchTemplates() {
    try {
      const response = await request.get<AssessmentTemplate[]>('/api/assessment-templates')
      templates.value = response
      return response
    } catch (err: any) {
      error.value = err.message || '获取评估模板失败'
      throw err
    }
  }
  
  // 过滤和排序
  function setFilters(newFilters: Partial<typeof filters.value>) {
    filters.value = { ...filters.value, ...newFilters }
  }
  
  function setSorting(field: typeof sortBy.value, order?: typeof sortOrder.value) {
    sortBy.value = field
    if (order) {
      sortOrder.value = order
    } else {
      // 切换排序顺序
      sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
    }
  }
  
  function resetFilters() {
    filters.value = {
      status: 'ALL',
      startDate: null,
      endDate: null,
      elderlyId: null,
      assessorId: null
    }
  }
  
  return {
    // 状态
    assessments,
    currentAssessment,
    templates,
    loading,
    error,
    filters,
    sortBy,
    sortOrder,
    
    // 计算属性
    filteredAssessments,
    statistics,
    
    // Actions
    fetchAssessments,
    fetchAssessmentById,
    createAssessment,
    updateAssessment,
    submitAssessment,
    fetchTemplates,
    setFilters,
    setSorting,
    resetFilters
  }
})
```

## 🔌 Pinia插件

### 持久化插件
```typescript
// stores/plugins/persist.ts
import { PiniaPluginContext } from 'pinia'
import { watch } from 'vue'

interface PersistOptions {
  enabled?: boolean
  strategies?: Array<{
    key?: string
    storage?: Storage
    paths?: string[]
  }>
}

declare module 'pinia' {
  export interface DefineStoreOptionsBase<S, Store> {
    persist?: PersistOptions
  }
}

export function persistPlugin({ store, options }: PiniaPluginContext) {
  if (!options.persist?.enabled) return
  
  const strategies = options.persist.strategies || [{
    key: store.$id,
    storage: localStorage,
    paths: null
  }]
  
  strategies.forEach(strategy => {
    const { 
      key = store.$id, 
      storage = localStorage, 
      paths = null 
    } = strategy
    
    // 恢复状态
    const savedState = storage.getItem(key)
    if (savedState) {
      try {
        const state = JSON.parse(savedState)
        if (paths) {
          // 只恢复指定路径
          paths.forEach(path => {
            const keys = path.split('.')
            let target = store.$state
            let source = state
            
            for (let i = 0; i < keys.length - 1; i++) {
              target = target[keys[i]]
              source = source[keys[i]]
            }
            
            const lastKey = keys[keys.length - 1]
            target[lastKey] = source[lastKey]
          })
        } else {
          // 恢复整个状态
          store.$patch(state)
        }
      } catch (error) {
        console.error(`Failed to restore state for ${key}:`, error)
      }
    }
    
    // 监听状态变化
    watch(
      () => {
        if (paths) {
          // 只保存指定路径
          const partialState: any = {}
          paths.forEach(path => {
            const keys = path.split('.')
            let source = store.$state
            let target = partialState
            
            for (let i = 0; i < keys.length - 1; i++) {
              const key = keys[i]
              source = source[key]
              target[key] = target[key] || {}
              target = target[key]
            }
            
            const lastKey = keys[keys.length - 1]
            target[lastKey] = source[lastKey]
          })
          return partialState
        } else {
          return store.$state
        }
      },
      (state) => {
        try {
          storage.setItem(key, JSON.stringify(state))
        } catch (error) {
          console.error(`Failed to persist state for ${key}:`, error)
        }
      },
      { deep: true }
    )
  })
}
```

### 使用持久化
```typescript
// stores/modules/user.ts
export const useUserStore = defineStore('user', () => {
  const profile = ref<UserProfile | null>(null)
  const preferences = ref({
    theme: 'light',
    language: 'zh-CN',
    notifications: true
  })
  
  return {
    profile,
    preferences
  }
}, {
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'user-preferences',
        storage: localStorage,
        paths: ['preferences']
      }
    ]
  }
})
```

## 🎨 组合式函数

### useAuth组合式函数
```typescript
// stores/composables/useAuth.ts
import { storeToRefs } from 'pinia'
import { useAuthStore } from '../modules/auth'
import { useRouter } from 'vue-router'

export function useAuth() {
  const authStore = useAuthStore()
  const router = useRouter()
  
  const { 
    isAuthenticated, 
    currentUser, 
    permissions,
    loginLoading,
    loginError 
  } = storeToRefs(authStore)
  
  // 登录方法封装
  async function loginWithRedirect(credentials: LoginRequest) {
    try {
      await authStore.login(credentials)
      
      // 获取重定向URL
      const redirect = router.currentRoute.value.query.redirect as string
      await router.push(redirect || '/dashboard')
    } catch (error) {
      // 错误已在store中处理
    }
  }
  
  // 登出方法封装
  async function logoutWithRedirect() {
    await authStore.logout()
    await router.push('/login')
  }
  
  // 权限指令
  function checkPermission(permission: string | string[]): boolean {
    if (!isAuthenticated.value) return false
    
    if (Array.isArray(permission)) {
      return authStore.hasAnyPermission(...permission)
    }
    
    const [resource, action] = permission.split(':')
    return authStore.hasPermission(resource, action)
  }
  
  return {
    // 状态
    isAuthenticated,
    currentUser,
    permissions,
    loginLoading,
    loginError,
    
    // 方法
    login: loginWithRedirect,
    logout: logoutWithRedirect,
    checkPermission,
    hasPermission: authStore.hasPermission,
    hasAnyPermission: authStore.hasAnyPermission,
    hasAllPermissions: authStore.hasAllPermissions
  }
}
```

## 🔗 全局状态类型

### Store类型定义
```typescript
// stores/types.ts
import type { 
  useAuthStore,
  useUserStore,
  useAssessmentStore,
  useOrganizationStore,
  useConfigStore
} from './modules'

export interface StoreTypes {
  auth: ReturnType<typeof useAuthStore>
  user: ReturnType<typeof useUserStore>
  assessment: ReturnType<typeof useAssessmentStore>
  organization: ReturnType<typeof useOrganizationStore>
  config: ReturnType<typeof useConfigStore>
}

// 全局store类型
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $stores: StoreTypes
  }
}
```

## 🚀 最佳实践

### 1. 状态组织原则
- **单一职责**: 每个store负责一个功能域
- **扁平化结构**: 避免深层嵌套
- **响应式优化**: 使用shallowRef处理大对象
- **计算属性**: 派生状态使用computed

### 2. 异步处理
```typescript
// 统一的loading和error处理
export function createAsyncAction<T extends (...args: any[]) => Promise<any>>(
  action: T,
  options?: {
    loading?: Ref<boolean>
    error?: Ref<string | null>
  }
): T {
  return (async (...args) => {
    if (options?.loading) options.loading.value = true
    if (options?.error) options.error.value = null
    
    try {
      return await action(...args)
    } catch (err: any) {
      if (options?.error) {
        options.error.value = err.message || '操作失败'
      }
      throw err
    } finally {
      if (options?.loading) options.loading.value = false
    }
  }) as T
}
```

### 3. 性能优化
```typescript
// 防抖的action
import { debounce } from 'lodash-es'

const debouncedSearch = debounce(async (query: string) => {
  const results = await searchAPI(query)
  searchResults.value = results
}, 300)

// 缓存计算结果
const cachedComputation = computed(() => {
  const cache = new Map()
  
  return (id: number) => {
    if (cache.has(id)) {
      return cache.get(id)
    }
    
    const result = expensiveComputation(id)
    cache.set(id, result)
    return result
  }
})
```

## 📞 相关资源

- [前端架构总览](./index.md) - 整体前端架构
- [Monorepo架构](./monorepo.md) - Monorepo实施
- [组件设计](./components.md) - 组件设计规范
- [状态管理迁移报告](../../development/guides/状态管理迁移最终报告.md) - 迁移实践

---

*最后更新：2025-07-01*