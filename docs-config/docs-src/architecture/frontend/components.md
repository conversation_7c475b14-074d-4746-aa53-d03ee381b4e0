# 🎨 组件设计规范

## 🎯 组件设计概述

智慧养老评估平台前端组件遵循原子设计理念，采用Vue 3 Composition API，实现可复用、可维护、高性能的组件体系。

## 🏗️ 组件架构分层

### 组件层级结构
```
components/
├── base/                    # 基础组件（原子级）
│   ├── BaseButton.vue      # 按钮
│   ├── BaseInput.vue       # 输入框
│   ├── BaseIcon.vue        # 图标
│   └── BaseCard.vue        # 卡片
├── form/                    # 表单组件（分子级）
│   ├── FormField.vue       # 表单字段
│   ├── FormSelect.vue      # 下拉选择
│   ├── FormDatePicker.vue  # 日期选择
│   └── FormUpload.vue      # 文件上传
├── layout/                  # 布局组件（有机体级）
│   ├── PageLayout.vue      # 页面布局
│   ├── SideMenu.vue        # 侧边菜单
│   ├── TopNavbar.vue       # 顶部导航
│   └── ContentArea.vue     # 内容区域
├── business/                # 业务组件（模板级）
│   ├── UserCard.vue        # 用户卡片
│   ├── AssessmentForm.vue  # 评估表单
│   ├── ElderlyProfile.vue  # 老人档案
│   └── StatisticsChart.vue # 统计图表
└── shared/                  # 共享组件
    ├── LoadingSpinner.vue   # 加载动画
    ├── ErrorMessage.vue     # 错误提示
    └── EmptyState.vue       # 空状态
```

## 🎨 基础组件设计

### BaseButton组件
```vue
<!-- components/base/BaseButton.vue -->
<template>
  <button
    :type="nativeType"
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <BaseIcon 
      v-if="loading" 
      name="loading" 
      class="animate-spin mr-2" 
    />
    <BaseIcon 
      v-else-if="icon" 
      :name="icon" 
      class="mr-2" 
    />
    <span v-if="$slots.default">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseIcon from './BaseIcon.vue'

export interface ButtonProps {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  size?: 'mini' | 'small' | 'medium' | 'large'
  nativeType?: 'button' | 'submit' | 'reset'
  disabled?: boolean
  loading?: boolean
  icon?: string
  round?: boolean
  circle?: boolean
  plain?: boolean
}

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'primary',
  size: 'medium',
  nativeType: 'button',
  disabled: false,
  loading: false,
  round: false,
  circle: false,
  plain: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => {
  const baseClasses = [
    'inline-flex',
    'items-center',
    'justify-center',
    'font-medium',
    'transition-all',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2'
  ]
  
  // 类型样式
  const typeClasses = {
    primary: props.plain 
      ? 'text-blue-600 border-blue-600 hover:bg-blue-50'
      : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    success: props.plain
      ? 'text-green-600 border-green-600 hover:bg-green-50'
      : 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
    warning: props.plain
      ? 'text-yellow-600 border-yellow-600 hover:bg-yellow-50'
      : 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
    danger: props.plain
      ? 'text-red-600 border-red-600 hover:bg-red-50'
      : 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    info: props.plain
      ? 'text-gray-600 border-gray-600 hover:bg-gray-50'
      : 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    text: 'text-blue-600 hover:text-blue-700 hover:bg-gray-50'
  }
  
  // 尺寸样式
  const sizeClasses = {
    mini: 'text-xs px-2 py-1',
    small: 'text-sm px-3 py-1.5',
    medium: 'text-base px-4 py-2',
    large: 'text-lg px-6 py-3'
  }
  
  // 形状样式
  const shapeClasses = []
  if (props.round) shapeClasses.push('rounded-full')
  else if (props.circle) shapeClasses.push('rounded-full', 'w-10', 'h-10', 'p-0')
  else shapeClasses.push('rounded')
  
  // 边框样式
  if (props.plain) {
    baseClasses.push('border-2')
  }
  
  // 禁用样式
  if (props.disabled || props.loading) {
    baseClasses.push('opacity-50', 'cursor-not-allowed')
  }
  
  return [
    ...baseClasses,
    typeClasses[props.type],
    sizeClasses[props.size],
    ...shapeClasses
  ]
})

function handleClick(event: MouseEvent) {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
```

### BaseInput组件
```vue
<!-- components/base/BaseInput.vue -->
<template>
  <div class="base-input" :class="containerClasses">
    <label 
      v-if="label" 
      :for="inputId"
      class="block text-sm font-medium text-gray-700 mb-1"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>
    
    <div class="relative">
      <div
        v-if="$slots.prefix || prefixIcon"
        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
      >
        <slot name="prefix">
          <BaseIcon v-if="prefixIcon" :name="prefixIcon" class="text-gray-400" />
        </slot>
      </div>
      
      <input
        :id="inputId"
        ref="inputRef"
        v-model="modelValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :class="inputClasses"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />
      
      <div
        v-if="$slots.suffix || suffixIcon || clearable"
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
      >
        <BaseIcon
          v-if="clearable && modelValue"
          name="close-circle"
          class="text-gray-400 cursor-pointer hover:text-gray-600"
          @click="handleClear"
        />
        <slot name="suffix">
          <BaseIcon v-if="suffixIcon" :name="suffixIcon" class="text-gray-400" />
        </slot>
      </div>
    </div>
    
    <p v-if="error" class="mt-1 text-sm text-red-600">
      {{ error }}
    </p>
    <p v-else-if="hint" class="mt-1 text-sm text-gray-500">
      {{ hint }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useId } from '@/composables/useId'
import BaseIcon from './BaseIcon.vue'

export interface InputProps {
  modelValue?: string | number
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url'
  size?: 'small' | 'medium' | 'large'
  placeholder?: string
  label?: string
  hint?: string
  error?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  clearable?: boolean
  prefixIcon?: string
  suffixIcon?: string
  maxlength?: number
  validateEvent?: boolean
}

const props = withDefaults(defineProps<InputProps>(), {
  type: 'text',
  size: 'medium',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  validateEvent: true
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  'input': [value: string | number, event: Event]
  'change': [value: string | number, event: Event]
  'focus': [event: FocusEvent]
  'blur': [event: FocusEvent]
  'clear': []
  'keydown': [event: KeyboardEvent]
}>()

const inputRef = ref<HTMLInputElement>()
const inputId = useId('input')
const isFocused = ref(false)

const containerClasses = computed(() => ({
  'opacity-50': props.disabled
}))

const inputClasses = computed(() => {
  const baseClasses = [
    'block',
    'w-full',
    'border',
    'rounded-md',
    'shadow-sm',
    'transition-all',
    'duration-200'
  ]
  
  // 尺寸样式
  const sizeClasses = {
    small: 'text-sm px-3 py-1.5',
    medium: 'text-base px-4 py-2',
    large: 'text-lg px-5 py-3'
  }
  
  // 状态样式
  if (props.error) {
    baseClasses.push(
      'border-red-300',
      'focus:ring-red-500',
      'focus:border-red-500'
    )
  } else {
    baseClasses.push(
      'border-gray-300',
      'focus:ring-blue-500',
      'focus:border-blue-500'
    )
  }
  
  // 前后缀图标样式
  if (props.prefixIcon || props.$slots.prefix) {
    baseClasses.push('pl-10')
  }
  if (props.suffixIcon || props.$slots.suffix || props.clearable) {
    baseClasses.push('pr-10')
  }
  
  // 禁用和只读样式
  if (props.disabled) {
    baseClasses.push('bg-gray-50', 'cursor-not-allowed')
  } else if (props.readonly) {
    baseClasses.push('bg-gray-50', 'cursor-default')
  }
  
  return [
    ...baseClasses,
    sizeClasses[props.size]
  ]
})

// 双向绑定
const modelValue = computed({
  get: () => props.modelValue ?? '',
  set: (value) => emit('update:modelValue', value)
})

// 事件处理
function handleInput(event: Event) {
  const target = event.target as HTMLInputElement
  const value = props.type === 'number' ? Number(target.value) : target.value
  emit('input', value, event)
  if (props.validateEvent) {
    // 触发表单验证
  }
}

function handleChange(event: Event) {
  const target = event.target as HTMLInputElement
  const value = props.type === 'number' ? Number(target.value) : target.value
  emit('change', value, event)
}

function handleFocus(event: FocusEvent) {
  isFocused.value = true
  emit('focus', event)
}

function handleBlur(event: FocusEvent) {
  isFocused.value = false
  emit('blur', event)
  if (props.validateEvent) {
    // 触发表单验证
  }
}

function handleClear() {
  modelValue.value = ''
  emit('clear')
  inputRef.value?.focus()
}

function handleKeydown(event: KeyboardEvent) {
  emit('keydown', event)
}

// 公开方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  select: () => inputRef.value?.select()
})
</script>
```

## 🧩 业务组件设计

### AssessmentForm组件
```vue
<!-- components/business/AssessmentForm.vue -->
<template>
  <div class="assessment-form">
    <div class="form-header mb-6">
      <h2 class="text-2xl font-bold text-gray-900">
        {{ template?.name || '评估表单' }}
      </h2>
      <p class="mt-2 text-gray-600">
        {{ template?.description }}
      </p>
    </div>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="top"
      @submit.prevent="handleSubmit"
    >
      <div 
        v-for="(section, sectionIndex) in template?.sections"
        :key="section.id"
        class="form-section mb-8"
      >
        <h3 class="text-lg font-medium text-gray-900 mb-4">
          {{ sectionIndex + 1 }}. {{ section.title }}
        </h3>
        
        <div class="space-y-4">
          <el-form-item
            v-for="item in section.items"
            :key="item.id"
            :label="item.label"
            :prop="`answers.${item.id}`"
            :required="item.required"
          >
            <component
              :is="getFieldComponent(item.type)"
              v-model="formData.answers[item.id]"
              :options="item.options"
              :config="item.config"
              :disabled="readonly"
              @change="handleFieldChange(item, $event)"
            />
            <template v-if="item.hint" #help>
              <span class="text-sm text-gray-500">{{ item.hint }}</span>
            </template>
          </el-form-item>
        </div>
      </div>
      
      <div class="form-footer sticky bottom-0 bg-white border-t pt-4">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-600">
            <span>进度: </span>
            <span class="font-medium">{{ completedCount }} / {{ totalCount }}</span>
          </div>
          
          <div class="space-x-3">
            <BaseButton
              v-if="!readonly"
              type="text"
              @click="handleSaveDraft"
            >
              保存草稿
            </BaseButton>
            <BaseButton
              v-if="!readonly"
              type="primary"
              :loading="submitting"
              @click="handleSubmit"
            >
              提交评估
            </BaseButton>
            <BaseButton
              v-else
              type="primary"
              @click="handleExport"
            >
              导出报告
            </BaseButton>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElForm, ElFormItem, ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import BaseButton from '@/components/base/BaseButton.vue'
import { useAssessmentStore } from '@/stores/modules/assessment'
import type { AssessmentTemplate, AssessmentItem } from '@/types'

// 动态导入字段组件
const fieldComponents = {
  text: () => import('@/components/form/fields/TextField.vue'),
  number: () => import('@/components/form/fields/NumberField.vue'),
  select: () => import('@/components/form/fields/SelectField.vue'),
  radio: () => import('@/components/form/fields/RadioField.vue'),
  checkbox: () => import('@/components/form/fields/CheckboxField.vue'),
  date: () => import('@/components/form/fields/DateField.vue'),
  scale: () => import('@/components/form/fields/ScaleField.vue'),
  matrix: () => import('@/components/form/fields/MatrixField.vue')
}

interface Props {
  template: AssessmentTemplate
  initialData?: Record<string, any>
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<{
  'submit': [data: Record<string, any>]
  'save-draft': [data: Record<string, any>]
  'field-change': [field: AssessmentItem, value: any]
}>()

const assessmentStore = useAssessmentStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = ref({
  answers: props.initialData || {}
})

// 动态生成表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {}
  
  props.template?.sections.forEach(section => {
    section.items.forEach(item => {
      if (item.required) {
        rules[`answers.${item.id}`] = [
          { 
            required: true, 
            message: `请填写${item.label}`,
            trigger: ['blur', 'change']
          }
        ]
        
        // 自定义验证规则
        if (item.validation) {
          rules[`answers.${item.id}`].push({
            validator: (rule, value, callback) => {
              const result = evaluateValidation(item.validation, value)
              if (result.valid) {
                callback()
              } else {
                callback(new Error(result.message))
              }
            },
            trigger: 'blur'
          })
        }
      }
    })
  })
  
  return rules
})

// 进度统计
const totalCount = computed(() => {
  return props.template?.sections.reduce(
    (sum, section) => sum + section.items.length, 
    0
  ) || 0
})

const completedCount = computed(() => {
  return Object.values(formData.value.answers).filter(v => v !== null && v !== '').length
})

// 获取字段组件
function getFieldComponent(type: string) {
  return fieldComponents[type] || fieldComponents.text
}

// 字段值变化处理
function handleFieldChange(field: AssessmentItem, value: any) {
  emit('field-change', field, value)
  
  // 处理联动逻辑
  if (field.dependencies) {
    updateDependentFields(field.dependencies)
  }
}

// 保存草稿
async function handleSaveDraft() {
  try {
    await assessmentStore.saveDraft({
      templateId: props.template.id,
      answers: formData.value.answers
    })
    ElMessage.success('草稿保存成功')
    emit('save-draft', formData.value.answers)
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  }
}

// 提交表单
async function handleSubmit() {
  const valid = await formRef.value?.validate()
  if (!valid) {
    ElMessage.warning('请完成所有必填项')
    return
  }
  
  submitting.value = true
  try {
    await assessmentStore.submitAssessment({
      templateId: props.template.id,
      answers: formData.value.answers
    })
    ElMessage.success('评估提交成功')
    emit('submit', formData.value.answers)
  } catch (error) {
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 导出报告
function handleExport() {
  // 实现报告导出逻辑
}

// 评估验证规则
function evaluateValidation(validation: any, value: any) {
  // 实现自定义验证逻辑
  return { valid: true, message: '' }
}

// 更新依赖字段
function updateDependentFields(dependencies: any[]) {
  // 实现字段联动逻辑
}
</script>

<style lang="scss" scoped>
.assessment-form {
  .form-section {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .form-footer {
    margin-top: 24px;
    padding-top: 20px;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
  }
}
</style>
```

## 🎯 组件设计原则

### 1. 单一职责原则
每个组件只负责一个功能，保持组件的简单和可维护性。

### 2. 组合优于继承
通过组合小组件来构建复杂组件，而不是通过继承。

### 3. Props向下，Events向上
数据通过props向下传递，事件通过emit向上传递。

### 4. 插槽优于Props
对于复杂的内容定制，优先使用插槽而不是props。

## 🔧 组件开发规范

### 命名规范
```typescript
// 组件文件命名：PascalCase
UserProfile.vue
AssessmentForm.vue

// 组件名称：PascalCase
export default {
  name: 'UserProfile'
}

// Props命名：camelCase
interface Props {
  userName: string
  isActive: boolean
}

// 事件命名：kebab-case
emit('update:model-value', value)
emit('row-click', row)
```

### TypeScript类型定义
```typescript
// 组件Props类型
export interface ComponentProps {
  // 必需属性
  id: string
  name: string
  
  // 可选属性
  description?: string
  disabled?: boolean
  
  // 联合类型
  size?: 'small' | 'medium' | 'large'
  
  // 对象类型
  config?: {
    theme: string
    locale: string
  }
  
  // 函数类型
  validator?: (value: any) => boolean
}

// 组件Emits类型
export interface ComponentEmits {
  'update:modelValue': [value: string]
  'change': [value: string, oldValue: string]
  'click': [event: MouseEvent]
}

// 组件Slots类型
export interface ComponentSlots {
  default?: () => VNode[]
  header?: (props: { title: string }) => VNode[]
  footer?: () => VNode[]
}
```

### 组件文档
```vue
<script setup lang="ts">
/**
 * 用户卡片组件
 * @displayName UserCard
 * @description 显示用户基本信息的卡片组件
 * @example
 * ```vue
 * <UserCard 
 *   :user="currentUser"
 *   :show-actions="true"
 *   @edit="handleEdit"
 *   @delete="handleDelete"
 * />
 * ```
 */

export interface UserCardProps {
  /** 用户对象 */
  user: User
  /** 是否显示操作按钮 */
  showActions?: boolean
  /** 是否处于加载状态 */
  loading?: boolean
}

const props = withDefaults(defineProps<UserCardProps>(), {
  showActions: true,
  loading: false
})
</script>
```

## 🧪 组件测试

### 单元测试示例
```typescript
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import BaseButton from '@/components/base/BaseButton.vue'

describe('BaseButton', () => {
  it('renders properly', () => {
    const wrapper = mount(BaseButton, {
      props: {
        type: 'primary'
      },
      slots: {
        default: 'Click me'
      }
    })
    
    expect(wrapper.text()).toContain('Click me')
    expect(wrapper.classes()).toContain('bg-blue-600')
  })
  
  it('emits click event when clicked', async () => {
    const wrapper = mount(BaseButton)
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted()).toHaveProperty('click')
    expect(wrapper.emitted('click')).toHaveLength(1)
  })
  
  it('does not emit click when disabled', async () => {
    const wrapper = mount(BaseButton, {
      props: {
        disabled: true
      }
    })
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('click')).toBeUndefined()
  })
})
```

## 🚀 性能优化

### 1. 组件懒加载
```typescript
// 路由级别懒加载
const UserProfile = () => import('@/views/UserProfile.vue')

// 组件级别懒加载
const HeavyComponent = defineAsyncComponent(() =>
  import('@/components/HeavyComponent.vue')
)
```

### 2. Props稳定性
```typescript
// ❌ 避免在模板中创建新对象
<UserList :config="{ pageSize: 10 }" />

// ✅ 使用稳定的引用
const listConfig = { pageSize: 10 }
<UserList :config="listConfig" />
```

### 3. 事件处理优化
```typescript
// ❌ 避免在模板中创建新函数
<div v-for="item in items" @click="() => handleClick(item.id)">

// ✅ 使用事件委托
<div @click="handleClick">
  <div v-for="item in items" :data-id="item.id">
```

## 📞 相关资源

- [前端架构总览](./index.md) - 整体前端架构
- [Monorepo架构](./monorepo.md) - Monorepo实施
- [状态管理](./state-management.md) - Pinia状态管理
- [前端开发指南](../../development/frontend-guide.md) - 开发指南

---

*最后更新：2025-07-01*