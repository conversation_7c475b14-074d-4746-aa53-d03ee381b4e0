# 📦 Monorepo架构实施

## 🎯 Monorepo架构概述

智慧养老评估平台前端采用Monorepo架构，将管理后台、企业官网和移动端H5三个应用统一管理，实现代码共享、依赖统一和开发效率提升。

## 🏗️ Monorepo架构设计

### 目录结构
```
frontend/
├── package.json                    # 根配置文件
├── tsconfig.json                  # TypeScript根配置
├── .eslintrc.js                   # ESLint根配置
├── packages/                      # 工作区包
│   ├── admin/                     # 管理后台应用
│   │   ├── package.json          
│   │   ├── src/
│   │   ├── public/
│   │   └── vite.config.ts
│   ├── website/                   # 企业官网应用
│   │   ├── package.json
│   │   ├── src/
│   │   ├── public/
│   │   └── vite.config.ts
│   └── mobile/                    # 移动端H5应用
│       ├── package.json
│       ├── src/
│       └── vite.config.ts
├── shared/                        # 共享资源
│   ├── components/                # 共享组件
│   ├── utils/                     # 共享工具函数
│   ├── types/                     # TypeScript类型定义
│   ├── constants/                 # 共享常量
│   └── styles/                    # 共享样式
└── scripts/                       # 构建脚本
    ├── build-all.js
    └── dev-all.js
```

## 🔧 技术实现

### npm Workspaces配置
```json
// frontend/package.json
{
  "name": "@assessment/root",
  "private": true,
  "workspaces": [
    "packages/*",
    "shared"
  ],
  "scripts": {
    "dev": "npm run dev --workspace=@assessment/admin",
    "dev:admin": "npm run dev --workspace=@assessment/admin",
    "dev:website": "npm run dev --workspace=@assessment/website",
    "dev:mobile": "npm run dev --workspace=@assessment/mobile",
    "dev:all": "concurrently \"npm:dev:admin\" \"npm:dev:website\" \"npm:dev:mobile\"",
    "build": "npm run build:all",
    "build:all": "npm run build --workspaces",
    "build:admin": "npm run build --workspace=@assessment/admin",
    "build:website": "npm run build --workspace=@assessment/website",
    "build:mobile": "npm run build --workspace=@assessment/mobile",
    "lint": "eslint . --ext .ts,.tsx,.js,.jsx,.vue",
    "type-check": "tsc --noEmit",
    "test": "vitest",
    "clean": "npm run clean --workspaces && rimraf node_modules"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-vue": "^4.5.0",
    "concurrently": "^8.0.0",
    "eslint": "^8.45.0",
    "eslint-plugin-vue": "^9.15.0",
    "prettier": "^3.0.0",
    "rimraf": "^5.0.0",
    "typescript": "^5.2.0",
    "vite": "^5.0.0",
    "vitest": "^1.0.0"
  }
}
```

### 子包配置
```json
// packages/admin/package.json
{
  "name": "@assessment/admin",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "vite --port 5274",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "@assessment/shared": "workspace:*",
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "axios": "^1.6.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@vue/tsconfig": "^0.4.0"
  }
}
```

### TypeScript配置继承
```json
// frontend/tsconfig.json (根配置)
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "strict": true,
    "jsx": "preserve",
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@assessment/shared/*": ["shared/*"]
    }
  },
  "references": [
    { "path": "./packages/admin" },
    { "path": "./packages/website" },
    { "path": "./packages/mobile" },
    { "path": "./shared" }
  ]
}
```

```json
// packages/admin/tsconfig.json (子包配置)
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@shared/*": ["../../shared/*"]
    }
  },
  "include": ["src/**/*", "../../shared/**/*"],
  "references": [
    { "path": "../../shared" }
  ]
}
```

## 📦 共享资源管理

### 共享组件
```typescript
// shared/components/PageHeader.vue
<template>
  <div class="page-header">
    <h1 class="page-header__title">{{ title }}</h1>
    <div class="page-header__actions">
      <slot name="actions" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid var(--border-color);
  
  &__title {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-primary);
  }
}
</style>
```

### 共享工具函数
```typescript
// shared/utils/request.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'

export function createRequest(config: AxiosRequestConfig): AxiosInstance {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: 30000,
    ...config
  })
  
  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('access_token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )
  
  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      return response.data
    },
    (error) => {
      const message = error.response?.data?.message || '请求失败'
      ElMessage.error(message)
      return Promise.reject(error)
    }
  )
  
  return instance
}

// 导出默认实例
export const request = createRequest({})
```

### 共享类型定义
```typescript
// shared/types/user.ts
export interface User {
  id: number
  username: string
  email: string
  fullName: string
  phone?: string
  organizationId: number
  roles: Role[]
  status: UserStatus
  createdAt: string
  updatedAt: string
}

export interface Role {
  id: number
  name: string
  permissions: Permission[]
}

export interface Permission {
  id: number
  resource: string
  action: string
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  LOCKED = 'LOCKED'
}
```

### 共享常量
```typescript
// shared/constants/api.ts
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    ME: '/api/auth/me'
  },
  USERS: {
    LIST: '/api/users',
    CREATE: '/api/users',
    UPDATE: (id: number) => `/api/users/${id}`,
    DELETE: (id: number) => `/api/users/${id}`
  },
  ASSESSMENTS: {
    LIST: '/api/assessments',
    CREATE: '/api/assessments',
    DETAIL: (id: number) => `/api/assessments/${id}`,
    UPDATE: (id: number) => `/api/assessments/${id}`
  }
} as const
```

## 🔄 依赖管理

### 统一版本管理
```json
// frontend/package.json
{
  "overrides": {
    "vue": "3.4.0",
    "typescript": "5.2.0",
    "@types/node": "20.0.0"
  }
}
```

### 共享依赖安装
```bash
# 在根目录安装所有依赖
npm install

# 为特定工作区安装依赖
npm install element-plus --workspace=@assessment/admin

# 为共享包安装依赖
npm install dayjs --workspace=@assessment/shared
```

## 🚀 开发工作流

### 本地开发
```bash
# 启动所有应用
npm run dev:all

# 启动特定应用
npm run dev:admin
npm run dev:website
npm run dev:mobile

# 端口分配
# admin: http://localhost:5274
# website: http://localhost:5175
# mobile: http://localhost:5273
```

### Vite配置共享
```typescript
// shared/vite.config.base.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export function createViteConfig(dirname: string) {
  return defineConfig({
    plugins: [vue()],
    resolve: {
      alias: {
        '@': resolve(dirname, 'src'),
        '@shared': resolve(dirname, '../../shared')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@shared/styles/variables.scss";`
        }
      }
    }
  })
}
```

```typescript
// packages/admin/vite.config.ts
import { createViteConfig } from '../../shared/vite.config.base'

export default {
  ...createViteConfig(__dirname),
  server: {
    port: 5274,
    proxy: {
      '/api': {
        target: 'http://localhost:8181',
        changeOrigin: true
      }
    }
  }
}
```

## 📦 构建和发布

### 统一构建
```bash
# 构建所有应用
npm run build:all

# 构建特定应用
npm run build:admin
npm run build:website
npm run build:mobile
```

### 构建脚本
```javascript
// scripts/build-all.js
const { execSync } = require('child_process')
const path = require('path')

const packages = ['admin', 'website', 'mobile']

console.log('🚀 开始构建所有应用...')

packages.forEach(pkg => {
  console.log(`\n📦 构建 ${pkg}...`)
  try {
    execSync(`npm run build --workspace=@assessment/${pkg}`, {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..')
    })
    console.log(`✅ ${pkg} 构建成功`)
  } catch (error) {
    console.error(`❌ ${pkg} 构建失败`)
    process.exit(1)
  }
})

console.log('\n🎉 所有应用构建完成！')
```

## 🧪 测试策略

### 统一测试配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/mockData.ts'
      ]
    }
  },
  resolve: {
    alias: {
      '@shared': '/shared'
    }
  }
})
```

### 共享组件测试
```typescript
// shared/components/__tests__/PageHeader.spec.ts
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import PageHeader from '../PageHeader.vue'

describe('PageHeader', () => {
  it('renders title correctly', () => {
    const wrapper = mount(PageHeader, {
      props: {
        title: '测试标题'
      }
    })
    
    expect(wrapper.find('h1').text()).toBe('测试标题')
  })
  
  it('renders slot content', () => {
    const wrapper = mount(PageHeader, {
      props: {
        title: '测试标题'
      },
      slots: {
        actions: '<button>操作按钮</button>'
      }
    })
    
    expect(wrapper.find('button').exists()).toBe(true)
  })
})
```

## 🔍 CI/CD集成

### GitHub Actions配置
```yaml
name: Monorepo CI/CD

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Install dependencies
        run: |
          cd frontend
          npm ci
      
      - name: Lint
        run: |
          cd frontend
          npm run lint
      
      - name: Type check
        run: |
          cd frontend
          npm run type-check
      
      - name: Test
        run: |
          cd frontend
          npm test
      
      - name: Build
        run: |
          cd frontend
          npm run build:all
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: |
            frontend/packages/admin/dist
            frontend/packages/website/dist
            frontend/packages/mobile/dist
```

## 📈 最佳实践

### 1. 依赖管理原则
- 共享依赖放在根 package.json
- 应用特定依赖放在各自 package.json
- 使用 workspace 协议引用共享包

### 2. 代码共享策略
- UI组件共享：基础组件放在 shared
- 业务组件：各应用独立维护
- 工具函数：通用工具放在 shared
- 类型定义：全局类型放在 shared

### 3. 版本管理
- 使用统一的版本号
- 同步发布所有包
- 使用 changeset 管理版本

### 4. 性能优化
- 共享包按需加载
- 使用 tree shaking
- 优化构建缓存

## 📞 相关资源

- [前端架构总览](./index.md) - 整体前端架构
- [状态管理](./state-management.md) - Pinia状态管理
- [组件设计](./components.md) - 组件设计规范
- [前端开发指南](../../development/frontend-guide.md) - 开发指南

---

*最后更新：2025-07-01*