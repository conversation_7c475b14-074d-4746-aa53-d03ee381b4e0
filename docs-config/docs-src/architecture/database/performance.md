# ⚡ 数据库性能优化

## 🎯 性能优化概述

智慧养老评估平台通过多层次的数据库优化策略，确保系统在高并发、大数据量场景下的稳定运行和快速响应。

## 🏗️ 性能优化架构

### 优化层次
1. **查询优化**: SQL语句优化、执行计划分析
2. **索引优化**: 合理的索引设计和维护
3. **表结构优化**: 分区表、物化视图
4. **连接池优化**: 连接池参数调优
5. **缓存优化**: 多级缓存策略

## 📊 查询优化

### 慢查询分析
```sql
-- 启用慢查询日志
ALTER SYSTEM SET log_min_duration_statement = 1000; -- 记录超过1秒的查询
ALTER SYSTEM SET log_statement = 'all';
SELECT pg_reload_conf();

-- 查看慢查询统计
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    stddev_time,
    rows
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC
LIMIT 20;
```

### 查询计划分析
```sql
-- 分析评估查询的执行计划
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    a.id,
    a.assessment_date,
    a.total_score,
    e.name as elderly_name,
    u.full_name as assessor_name
FROM assessments a
JOIN elderly_profiles e ON a.elderly_id = e.id
JOIN users u ON a.assessor_id = u.id
WHERE a.organization_id = 1
    AND a.status = 'COMPLETED'
    AND a.assessment_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY a.assessment_date DESC
LIMIT 20;
```

### 查询优化示例
```java
@Repository
public class OptimizedAssessmentRepository {
    
    // 优化前：N+1查询问题
    @Query("SELECT a FROM Assessment a WHERE a.organizationId = :orgId")
    List<Assessment> findByOrganizationIdBad(@Param("orgId") Long orgId);
    
    // 优化后：使用JOIN FETCH避免N+1
    @Query("""
        SELECT DISTINCT a 
        FROM Assessment a
        LEFT JOIN FETCH a.elderly e
        LEFT JOIN FETCH a.assessor u
        LEFT JOIN FETCH a.template t
        WHERE a.organizationId = :orgId
        ORDER BY a.assessmentDate DESC
        """)
    List<Assessment> findByOrganizationIdOptimized(@Param("orgId") Long orgId);
    
    // 使用投影减少数据传输
    @Query("""
        SELECT new com.assessment.dto.AssessmentSummary(
            a.id, a.assessmentDate, a.totalScore, a.status,
            e.name, u.fullName
        )
        FROM Assessment a
        JOIN a.elderly e
        JOIN a.assessor u
        WHERE a.organizationId = :orgId
        AND a.assessmentDate BETWEEN :startDate AND :endDate
        """)
    List<AssessmentSummary> findAssessmentSummaries(
        @Param("orgId") Long orgId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
}
```

## 🔍 索引优化策略

### 核心索引设计
```sql
-- 1. 复合索引优化查询性能
CREATE INDEX idx_assessments_org_status_date 
ON assessments(organization_id, status, assessment_date DESC);

-- 2. 部分索引减少索引大小
CREATE INDEX idx_assessments_completed 
ON assessments(assessment_date DESC) 
WHERE status = 'COMPLETED';

-- 3. 覆盖索引避免回表查询
CREATE INDEX idx_elderly_search 
ON elderly_profiles(organization_id, name, id_card) 
INCLUDE (phone, status);

-- 4. 表达式索引优化函数查询
CREATE INDEX idx_users_lower_username 
ON users(LOWER(username));

-- 5. GIN索引优化JSONB查询
CREATE INDEX idx_assessments_answers_gin 
ON assessments USING GIN(answers);
```

### 索引维护
```sql
-- 定期重建索引
REINDEX INDEX CONCURRENTLY idx_assessments_org_status_date;

-- 查看索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- 识别无用索引
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
WHERE idx_scan = 0
AND indexrelid > 16384
ORDER BY pg_relation_size(indexrelid) DESC;
```

## 📈 表结构优化

### 分区表设计
```sql
-- 按月分区的审计日志表
CREATE TABLE audit_logs (
    id BIGSERIAL,
    user_id BIGINT,
    action VARCHAR(100),
    resource_type VARCHAR(50),
    resource_id BIGINT,
    created_at TIMESTAMP NOT NULL,
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- 创建分区
CREATE TABLE audit_logs_2025_01 PARTITION OF audit_logs
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE audit_logs_2025_02 PARTITION OF audit_logs
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- 自动创建分区的函数
CREATE OR REPLACE FUNCTION create_monthly_partition()
RETURNS void AS $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    start_date := DATE_TRUNC('month', CURRENT_DATE);
    end_date := start_date + INTERVAL '1 month';
    partition_name := 'audit_logs_' || TO_CHAR(start_date, 'YYYY_MM');
    
    EXECUTE format(
        'CREATE TABLE IF NOT EXISTS %I PARTITION OF audit_logs 
         FOR VALUES FROM (%L) TO (%L)',
        partition_name, start_date, end_date
    );
END;
$$ LANGUAGE plpgsql;

-- 定期执行分区创建
CREATE EXTENSION IF NOT EXISTS pg_cron;
SELECT cron.schedule('create-partitions', '0 0 1 * *', 'SELECT create_monthly_partition()');
```

### 物化视图优化
```sql
-- 创建评估统计物化视图
CREATE MATERIALIZED VIEW assessment_statistics_mv AS
SELECT 
    o.id as organization_id,
    o.name as organization_name,
    DATE_TRUNC('month', a.assessment_date) as month,
    COUNT(DISTINCT e.id) as elderly_count,
    COUNT(a.id) as assessment_count,
    AVG(a.total_score) as avg_score,
    COUNT(DISTINCT a.assessor_id) as assessor_count
FROM organizations o
LEFT JOIN elderly_profiles e ON o.id = e.organization_id
LEFT JOIN assessments a ON e.id = a.elderly_id
WHERE a.status = 'COMPLETED'
GROUP BY o.id, o.name, DATE_TRUNC('month', a.assessment_date);

-- 创建索引
CREATE INDEX idx_assessment_stats_org_month 
ON assessment_statistics_mv(organization_id, month DESC);

-- 定期刷新
CREATE OR REPLACE FUNCTION refresh_assessment_statistics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY assessment_statistics_mv;
END;
$$ LANGUAGE plpgsql;
```

## 🔧 连接池优化

### HikariCP配置优化
```yaml
spring:
  datasource:
    hikari:
      # 连接池大小
      maximum-pool-size: 20
      minimum-idle: 5
      
      # 连接超时设置
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
      # 连接测试
      connection-test-query: SELECT 1
      validation-timeout: 5000
      
      # 性能优化
      auto-commit: false
      pool-name: AssessmentHikariCP
      
      # 监控
      register-mbeans: true
```

### 连接池监控
```java
@Component
public class ConnectionPoolMonitor {
    
    @Autowired
    private HikariDataSource dataSource;
    
    @Scheduled(fixedDelay = 60000)
    public void monitorConnectionPool() {
        HikariPoolMXBean poolMXBean = dataSource.getHikariPoolMXBean();
        
        int activeConnections = poolMXBean.getActiveConnections();
        int idleConnections = poolMXBean.getIdleConnections();
        int totalConnections = poolMXBean.getTotalConnections();
        int threadsAwaitingConnection = poolMXBean.getThreadsAwaitingConnection();
        
        if (threadsAwaitingConnection > 0) {
            log.warn("连接池等待线程数: {}", threadsAwaitingConnection);
        }
        
        if (activeConnections > totalConnections * 0.8) {
            log.warn("连接池使用率过高: {}/{}", activeConnections, totalConnections);
        }
    }
}
```

## 💾 缓存优化策略

### 多级缓存架构
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .withCacheConfiguration("users", 
                config.entryTtl(Duration.ofHours(1)))
            .withCacheConfiguration("assessments", 
                config.entryTtl(Duration.ofMinutes(10)))
            .build();
    }
}

@Service
public class CachedAssessmentService {
    
    @Cacheable(value = "assessments", key = "#id")
    public Assessment getAssessment(Long id) {
        return assessmentRepository.findById(id)
            .orElseThrow(() -> new NotFoundException("评估记录不存在"));
    }
    
    @CacheEvict(value = "assessments", key = "#assessment.id")
    public Assessment updateAssessment(Assessment assessment) {
        return assessmentRepository.save(assessment);
    }
    
    @Cacheable(value = "assessment-stats", key = "#orgId + ':' + #month")
    public AssessmentStatistics getMonthlyStatistics(Long orgId, YearMonth month) {
        return calculateStatistics(orgId, month);
    }
}
```

### 查询结果缓存
```java
@Repository
public interface AssessmentRepository extends JpaRepository<Assessment, Long> {
    
    @QueryHints(@QueryHint(name = "org.hibernate.cacheable", value = "true"))
    @Query("SELECT a FROM Assessment a WHERE a.organizationId = :orgId")
    List<Assessment> findByOrganizationIdCached(@Param("orgId") Long orgId);
}
```

## 📊 批量操作优化

### 批量插入优化
```java
@Service
@Transactional
public class BatchAssessmentService {
    
    @Value("${spring.jpa.properties.hibernate.jdbc.batch_size:50}")
    private int batchSize;
    
    public void batchCreateAssessments(List<Assessment> assessments) {
        // 使用JDBC批处理
        jdbcTemplate.batchUpdate(
            """
            INSERT INTO assessments (elderly_id, template_id, assessor_id, 
                assessment_date, status, answers, scores, total_score)
            VALUES (?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?)
            """,
            new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    Assessment assessment = assessments.get(i);
                    ps.setLong(1, assessment.getElderlyId());
                    ps.setLong(2, assessment.getTemplateId());
                    ps.setLong(3, assessment.getAssessorId());
                    ps.setTimestamp(4, Timestamp.valueOf(assessment.getAssessmentDate()));
                    ps.setString(5, assessment.getStatus().name());
                    ps.setString(6, objectMapper.writeValueAsString(assessment.getAnswers()));
                    ps.setString(7, objectMapper.writeValueAsString(assessment.getScores()));
                    ps.setBigDecimal(8, assessment.getTotalScore());
                }
                
                @Override
                public int getBatchSize() {
                    return assessments.size();
                }
            }
        );
    }
}
```

## 🔍 性能监控

### 实时性能监控
```java
@RestController
@RequestMapping("/api/admin/performance")
public class PerformanceMonitorController {
    
    @GetMapping("/database/stats")
    public DatabaseStats getDatabaseStats() {
        return jdbcTemplate.queryForObject(
            """
            SELECT 
                (SELECT count(*) FROM pg_stat_activity) as active_connections,
                (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
                (SELECT count(*) FROM pg_stat_activity WHERE wait_event_type IS NOT NULL) as waiting_queries,
                pg_database_size(current_database()) as database_size,
                (SELECT sum(idx_scan) FROM pg_stat_user_indexes) as total_index_scans,
                (SELECT sum(seq_scan) FROM pg_stat_user_tables) as total_seq_scans
            """,
            (rs, rowNum) -> DatabaseStats.builder()
                .activeConnections(rs.getInt("active_connections"))
                .idleConnections(rs.getInt("idle_connections"))
                .waitingQueries(rs.getInt("waiting_queries"))
                .databaseSize(rs.getLong("database_size"))
                .totalIndexScans(rs.getLong("total_index_scans"))
                .totalSeqScans(rs.getLong("total_seq_scans"))
                .build()
        );
    }
}
```

### 性能基准测试
```java
@Component
public class PerformanceBenchmark {
    
    @Scheduled(cron = "0 0 3 * * SUN") // 每周日凌晨3点
    public void runBenchmark() {
        BenchmarkResult result = new BenchmarkResult();
        
        // 测试查询性能
        long queryTime = measureQueryPerformance();
        result.setAverageQueryTime(queryTime);
        
        // 测试写入性能
        long writeTime = measureWritePerformance();
        result.setAverageWriteTime(writeTime);
        
        // 测试并发性能
        int concurrentCapacity = measureConcurrentCapacity();
        result.setConcurrentCapacity(concurrentCapacity);
        
        // 保存结果
        benchmarkRepository.save(result);
        
        // 性能告警
        if (queryTime > 100) { // 超过100ms
            alertService.sendPerformanceAlert("查询性能下降", result);
        }
    }
}
```

## 📈 优化建议

### 1. 定期维护任务
```sql
-- 更新统计信息
ANALYZE;

-- 清理死元组
VACUUM (VERBOSE, ANALYZE);

-- 重建索引
REINDEX DATABASE assessment_db;
```

### 2. 参数调优
```sql
-- PostgreSQL配置优化
ALTER SYSTEM SET shared_buffers = '4GB';
ALTER SYSTEM SET effective_cache_size = '12GB';
ALTER SYSTEM SET maintenance_work_mem = '1GB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;
```

## 📞 相关资源

- [数据库设计总览](./index.md) - 数据库架构
- [表结构设计](./tables.md) - 表结构详情
- [多租户数据隔离](./multi-tenant-isolation.md) - 隔离策略
- [架构优化报告](../../架构优化影响评估报告_保守方案_2025-01-02.md) - 优化方案

---

*最后更新：2025-07-01*