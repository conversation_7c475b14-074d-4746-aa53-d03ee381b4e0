# 🔐 多租户数据隔离

## 🎯 概述

智慧养老评估平台采用基于Schema的多租户数据隔离策略，确保不同租户间的数据完全隔离，保证数据安全性和隐私性。

## 🏗️ 隔离架构设计

### Schema级隔离策略
每个租户拥有独立的数据库Schema，实现数据的物理隔离：

```sql
-- 为每个租户创建独立的Schema
CREATE SCHEMA IF NOT EXISTS tenant_hospital_001;
CREATE SCHEMA IF NOT EXISTS tenant_nursing_002;
CREATE SCHEMA IF NOT EXISTS tenant_community_003;

-- 设置Schema搜索路径
SET search_path TO tenant_hospital_001, public;
```

### 租户识别机制
```java
@Component
public class TenantResolver {
    
    public String resolveTenantId(HttpServletRequest request) {
        // 1. 从JWT Token中获取租户ID
        String token = extractToken(request);
        if (token != null) {
            Claims claims = jwtTokenProvider.getClaims(token);
            return claims.get("tenantId", String.class);
        }
        
        // 2. 从子域名获取租户标识
        String serverName = request.getServerName();
        if (serverName.contains(".")) {
            return serverName.substring(0, serverName.indexOf("."));
        }
        
        // 3. 从请求头获取租户ID
        String tenantHeader = request.getHeader("X-Tenant-Id");
        if (tenantHeader != null) {
            return tenantHeader;
        }
        
        throw new TenantNotFoundException("无法识别租户信息");
    }
}
```

## 🔧 技术实现

### 动态数据源路由
```java
@Configuration
public class MultiTenantDataSourceConfig {
    
    @Bean
    public DataSource dataSource() {
        TenantRoutingDataSource routingDataSource = new TenantRoutingDataSource();
        
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put("default", defaultDataSource());
        
        routingDataSource.setTargetDataSources(targetDataSources);
        routingDataSource.setDefaultTargetDataSource(defaultDataSource());
        
        return routingDataSource;
    }
    
    public class TenantRoutingDataSource extends AbstractRoutingDataSource {
        @Override
        protected Object determineCurrentLookupKey() {
            return TenantContext.getCurrentTenant();
        }
    }
}
```

### Hibernate多租户配置
```java
@Configuration
@EnableJpaRepositories(
    basePackages = "com.assessment.repository",
    entityManagerFactoryRef = "multiTenantEntityManagerFactory",
    transactionManagerRef = "multiTenantTransactionManager"
)
public class HibernateMultiTenantConfig {
    
    @Bean
    public LocalContainerEntityManagerFactoryBean multiTenantEntityManagerFactory(
            DataSource dataSource,
            MultiTenantConnectionProvider connectionProvider,
            CurrentTenantIdentifierResolver tenantResolver) {
        
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("com.assessment.entity");
        
        JpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        
        Properties properties = new Properties();
        properties.put(Environment.MULTI_TENANT, MultiTenancyStrategy.SCHEMA);
        properties.put(Environment.MULTI_TENANT_CONNECTION_PROVIDER, connectionProvider);
        properties.put(Environment.MULTI_TENANT_IDENTIFIER_RESOLVER, tenantResolver);
        
        em.setJpaProperties(properties);
        return em;
    }
}
```

### 租户上下文管理
```java
@Component
public class TenantContext {
    
    private static final ThreadLocal<String> currentTenant = new ThreadLocal<>();
    
    public static void setCurrentTenant(String tenantId) {
        currentTenant.set(tenantId);
    }
    
    public static String getCurrentTenant() {
        return currentTenant.get();
    }
    
    public static void clear() {
        currentTenant.remove();
    }
}

@Component
public class TenantInterceptor implements HandlerInterceptor {
    
    @Autowired
    private TenantResolver tenantResolver;
    
    @Override
    public boolean preHandle(HttpServletRequest request, 
                           HttpServletResponse response, 
                           Object handler) {
        String tenantId = tenantResolver.resolveTenantId(request);
        TenantContext.setCurrentTenant(tenantId);
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request,
                              HttpServletResponse response,
                              Object handler,
                              Exception ex) {
        TenantContext.clear();
    }
}
```

## 🛡️ 数据安全保障

### 行级安全策略(RLS)
```sql
-- 启用行级安全
ALTER TABLE assessments ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
CREATE POLICY tenant_isolation_policy ON assessments
    FOR ALL 
    TO assessment_user
    USING (tenant_id = current_setting('app.current_tenant')::VARCHAR);

-- 创建安全函数
CREATE OR REPLACE FUNCTION set_current_tenant(tenant_id VARCHAR)
RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_tenant', tenant_id, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 跨租户访问防护
```java
@Aspect
@Component
public class TenantSecurityAspect {
    
    @Around("@annotation(TenantSecured)")
    public Object checkTenantAccess(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        
        for (Object arg : args) {
            if (arg instanceof TenantAware) {
                TenantAware entity = (TenantAware) arg;
                String currentTenant = TenantContext.getCurrentTenant();
                
                if (!currentTenant.equals(entity.getTenantId())) {
                    throw new TenantAccessDeniedException(
                        "禁止跨租户访问数据"
                    );
                }
            }
        }
        
        return joinPoint.proceed();
    }
}
```

## 📊 Schema管理

### 租户Schema创建
```java
@Service
@Transactional
public class TenantSchemaService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    public void createTenantSchema(String tenantId) {
        // 创建Schema
        String schemaName = "tenant_" + tenantId;
        jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS " + schemaName);
        
        // 复制表结构
        List<String> tables = Arrays.asList(
            "users", "organizations", "elderly_profiles",
            "assessments", "roles", "permissions"
        );
        
        for (String table : tables) {
            String createTableSql = String.format(
                "CREATE TABLE %s.%s (LIKE public.%s INCLUDING ALL)",
                schemaName, table, table
            );
            jdbcTemplate.execute(createTableSql);
        }
        
        // 初始化基础数据
        initializeTenantData(schemaName);
    }
    
    private void initializeTenantData(String schemaName) {
        // 创建默认角色
        jdbcTemplate.update(
            String.format("INSERT INTO %s.roles (name, description) VALUES (?, ?)", schemaName),
            "ADMIN", "系统管理员"
        );
        
        // 创建默认权限
        jdbcTemplate.update(
            String.format("INSERT INTO %s.permissions (resource, action) VALUES (?, ?)", schemaName),
            "USER", "CREATE"
        );
    }
}
```

### Schema迁移管理
```java
@Component
public class TenantFlywayConfig {
    
    @Autowired
    private DataSource dataSource;
    
    public void migrateTenantSchema(String tenantId) {
        String schemaName = "tenant_" + tenantId;
        
        Flyway flyway = Flyway.configure()
            .dataSource(dataSource)
            .schemas(schemaName)
            .locations("classpath:db/migration/tenant")
            .baselineOnMigrate(true)
            .load();
            
        flyway.migrate();
    }
}
```

## 🔍 监控和审计

### 租户访问日志
```java
@EventListener
public class TenantAuditListener {
    
    @Autowired
    private AuditLogRepository auditLogRepository;
    
    @EventListener
    public void handleTenantDataAccess(TenantDataAccessEvent event) {
        AuditLog log = AuditLog.builder()
            .tenantId(event.getTenantId())
            .userId(event.getUserId())
            .action(event.getAction())
            .resourceType(event.getResourceType())
            .resourceId(event.getResourceId())
            .timestamp(LocalDateTime.now())
            .ipAddress(event.getIpAddress())
            .build();
            
        auditLogRepository.save(log);
    }
}
```

### 租户资源监控
```java
@Component
public class TenantResourceMonitor {
    
    @Scheduled(fixedDelay = 300000) // 每5分钟
    public void monitorTenantResources() {
        List<TenantResourceUsage> usages = calculateResourceUsage();
        
        for (TenantResourceUsage usage : usages) {
            if (usage.getStorageUsed() > usage.getStorageLimit() * 0.9) {
                alertService.sendStorageAlert(usage.getTenantId());
            }
            
            if (usage.getUserCount() > usage.getUserLimit() * 0.9) {
                alertService.sendUserLimitAlert(usage.getTenantId());
            }
        }
    }
}
```

## 🚀 最佳实践

### 1. 连接池隔离
为每个租户配置独立的连接池，避免资源竞争：
```properties
# 租户连接池配置
tenant.datasource.maximum-pool-size=10
tenant.datasource.minimum-idle=2
tenant.datasource.connection-timeout=30000
```

### 2. 缓存隔离
使用租户ID作为缓存key前缀：
```java
@Cacheable(value = "users", key = "#tenantId + ':' + #userId")
public User getUser(String tenantId, Long userId) {
    return userRepository.findById(userId).orElse(null);
}
```

### 3. 定期数据清理
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
public void cleanupTenantData() {
    List<String> inactiveTenants = tenantService.getInactiveTenants(30); // 30天未活跃
    
    for (String tenantId : inactiveTenants) {
        archiveTenantData(tenantId);
    }
}
```

## 📞 相关资源

- [多租户架构总览](../multi-tenant/) - 整体多租户设计
- [数据库设计总览](./index.md) - 数据库架构
- [表结构设计](./tables.md) - 详细表结构
- [多租户数据库迁移指南](../multi-tenant/多租户数据库迁移指南_2025-06-21.md) - 迁移方案

---

*最后更新：2025-07-01*