# 📊 数据库表结构设计

## 🎯 表结构概述

智慧养老评估平台数据库采用多租户架构设计，通过Schema隔离实现租户数据分离，同时保证数据一致性和查询性能。

## 🏗️ 核心表结构

### 用户管理表

#### users (用户表)
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    organization_id BIGINT REFERENCES organizations(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT
);

-- 索引优化
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_organization ON users(organization_id);
CREATE INDEX idx_users_status ON users(status);
```

#### organizations (组织机构表)
```sql
CREATE TABLE organizations (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'HOSPITAL', 'NURSING_HOME', 'COMMUNITY'
    parent_id BIGINT REFERENCES organizations(id),
    address TEXT,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    tenant_schema VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_organizations_code ON organizations(code);
CREATE INDEX idx_organizations_type ON organizations(type);
CREATE INDEX idx_organizations_parent ON organizations(parent_id);
CREATE INDEX idx_organizations_tenant ON organizations(tenant_schema);
```

### 评估管理表

#### elderly_profiles (老人档案表)
```sql
CREATE TABLE elderly_profiles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(10) NOT NULL,
    birth_date DATE NOT NULL,
    id_card VARCHAR(18) UNIQUE,
    phone VARCHAR(20),
    address TEXT,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    organization_id BIGINT REFERENCES organizations(id),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT REFERENCES users(id),
    updated_by BIGINT REFERENCES users(id)
);

-- 索引优化
CREATE INDEX idx_elderly_name ON elderly_profiles(name);
CREATE INDEX idx_elderly_id_card ON elderly_profiles(id_card);
CREATE INDEX idx_elderly_organization ON elderly_profiles(organization_id);
CREATE INDEX idx_elderly_status ON elderly_profiles(status);
```

#### assessment_templates (评估模板表)
```sql
CREATE TABLE assessment_templates (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL, -- 'PHYSICAL', 'MENTAL', 'SOCIAL', 'COMPREHENSIVE'
    version VARCHAR(20) NOT NULL,
    content JSONB NOT NULL, -- 评估项目配置
    scoring_rules JSONB, -- 评分规则
    status VARCHAR(20) DEFAULT 'ACTIVE',
    organization_id BIGINT REFERENCES organizations(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT REFERENCES users(id)
);

-- 索引优化
CREATE INDEX idx_assessment_templates_category ON assessment_templates(category);
CREATE INDEX idx_assessment_templates_status ON assessment_templates(status);
CREATE INDEX idx_assessment_templates_organization ON assessment_templates(organization_id);
CREATE INDEX idx_assessment_templates_content ON assessment_templates USING GIN(content);
```

#### assessments (评估记录表)
```sql
CREATE TABLE assessments (
    id BIGSERIAL PRIMARY KEY,
    elderly_id BIGINT NOT NULL REFERENCES elderly_profiles(id),
    template_id BIGINT NOT NULL REFERENCES assessment_templates(id),
    assessor_id BIGINT NOT NULL REFERENCES users(id),
    assessment_date TIMESTAMP NOT NULL,
    status VARCHAR(20) DEFAULT 'IN_PROGRESS', -- 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'
    answers JSONB NOT NULL, -- 评估答案
    scores JSONB, -- 评估得分
    total_score DECIMAL(10,2),
    level VARCHAR(20), -- 评估等级
    recommendations TEXT, -- 建议
    organization_id BIGINT REFERENCES organizations(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_assessments_elderly ON assessments(elderly_id);
CREATE INDEX idx_assessments_template ON assessments(template_id);
CREATE INDEX idx_assessments_assessor ON assessments(assessor_id);
CREATE INDEX idx_assessments_date ON assessments(assessment_date);
CREATE INDEX idx_assessments_status ON assessments(status);
CREATE INDEX idx_assessments_organization ON assessments(organization_id);
CREATE INDEX idx_assessments_answers ON assessments USING GIN(answers);
```

### 权限管理表

#### roles (角色表)
```sql
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    organization_id BIGINT REFERENCES organizations(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 组合唯一约束：同一组织内角色名唯一
ALTER TABLE roles ADD CONSTRAINT uk_roles_name_org UNIQUE(name, organization_id);
```

#### permissions (权限表)
```sql
CREATE TABLE permissions (
    id BIGSERIAL PRIMARY KEY,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 组合唯一约束
ALTER TABLE permissions ADD CONSTRAINT uk_permissions_resource_action UNIQUE(resource, action);
```

#### user_roles (用户角色关联表)
```sql
CREATE TABLE user_roles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    role_id BIGINT NOT NULL REFERENCES roles(id),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by BIGINT REFERENCES users(id)
);

-- 组合唯一约束：用户-角色唯一
ALTER TABLE user_roles ADD CONSTRAINT uk_user_roles UNIQUE(user_id, role_id);
```

#### role_permissions (角色权限关联表)
```sql
CREATE TABLE role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL REFERENCES roles(id),
    permission_id BIGINT NOT NULL REFERENCES permissions(id),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by BIGINT REFERENCES users(id)
);

-- 组合唯一约束：角色-权限唯一
ALTER TABLE role_permissions ADD CONSTRAINT uk_role_permissions UNIQUE(role_id, permission_id);
```

## 🔄 审计日志表

#### audit_logs (审计日志表)
```sql
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id BIGINT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    organization_id BIGINT REFERENCES organizations(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 分区表设计 - 按月分区
CREATE TABLE audit_logs_y2025m01 PARTITION OF audit_logs
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 索引优化
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_date ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_organization ON audit_logs(organization_id);
```

## 📊 系统配置表

#### system_configs (系统配置表)
```sql
CREATE TABLE system_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    data_type VARCHAR(20) DEFAULT 'STRING', -- 'STRING', 'NUMBER', 'BOOLEAN', 'JSON'
    organization_id BIGINT REFERENCES organizations(id), -- NULL表示全局配置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by BIGINT REFERENCES users(id)
);

-- 索引优化
CREATE INDEX idx_system_configs_key ON system_configs(config_key);
CREATE INDEX idx_system_configs_organization ON system_configs(organization_id);
```

#### file_attachments (文件附件表)
```sql
CREATE TABLE file_attachments (
    id BIGSERIAL PRIMARY KEY,
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    md5_hash VARCHAR(32) NOT NULL,
    entity_type VARCHAR(50) NOT NULL, -- 关联实体类型
    entity_id BIGINT NOT NULL, -- 关联实体ID
    organization_id BIGINT REFERENCES organizations(id),
    uploaded_by BIGINT REFERENCES users(id),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_file_attachments_entity ON file_attachments(entity_type, entity_id);
CREATE INDEX idx_file_attachments_hash ON file_attachments(md5_hash);
CREATE INDEX idx_file_attachments_organization ON file_attachments(organization_id);
```

## 🔍 查询优化设计

### 复合索引策略
```sql
-- 评估记录复合查询优化
CREATE INDEX idx_assessments_elderly_date ON assessments(elderly_id, assessment_date DESC);
CREATE INDEX idx_assessments_org_status_date ON assessments(organization_id, status, assessment_date DESC);

-- 用户组织权限查询优化
CREATE INDEX idx_users_org_status ON users(organization_id, status);
CREATE INDEX idx_user_roles_user_role ON user_roles(user_id, role_id);

-- 审计日志查询优化
CREATE INDEX idx_audit_logs_user_date ON audit_logs(user_id, created_at DESC);
CREATE INDEX idx_audit_logs_org_date ON audit_logs(organization_id, created_at DESC);
```

### 部分索引优化
```sql
-- 只对活跃用户创建索引
CREATE INDEX idx_users_active_username ON users(username) WHERE status = 'ACTIVE';

-- 只对已完成的评估创建索引
CREATE INDEX idx_assessments_completed_date ON assessments(assessment_date DESC) 
    WHERE status = 'COMPLETED';
```

## 🔄 视图设计

### 用户权限视图
```sql
CREATE VIEW user_permissions_view AS
SELECT 
    u.id as user_id,
    u.username,
    u.full_name,
    o.name as organization_name,
    r.name as role_name,
    p.resource,
    p.action,
    p.description as permission_description
FROM users u
JOIN organizations o ON u.organization_id = o.id
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.status = 'ACTIVE';
```

### 评估统计视图
```sql
CREATE VIEW assessment_statistics_view AS
SELECT 
    o.id as organization_id,
    o.name as organization_name,
    COUNT(DISTINCT ep.id) as total_elderly,
    COUNT(DISTINCT a.id) as total_assessments,
    COUNT(DISTINCT CASE WHEN a.status = 'COMPLETED' THEN a.id END) as completed_assessments,
    AVG(CASE WHEN a.status = 'COMPLETED' THEN a.total_score END) as avg_score,
    COUNT(DISTINCT a.assessor_id) as active_assessors
FROM organizations o
LEFT JOIN elderly_profiles ep ON o.id = ep.organization_id
LEFT JOIN assessments a ON ep.id = a.elderly_id
WHERE o.status = 'ACTIVE'
GROUP BY o.id, o.name;
```

## 🔒 数据安全设计

### 行级安全(RLS)
```sql
-- 启用行级安全
ALTER TABLE elderly_profiles ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
CREATE POLICY elderly_organization_policy ON elderly_profiles
    FOR ALL TO application_role
    USING (organization_id = current_setting('app.current_organization_id')::BIGINT);

-- 评估记录安全策略
ALTER TABLE assessments ENABLE ROW LEVEL SECURITY;
CREATE POLICY assessments_organization_policy ON assessments
    FOR ALL TO application_role
    USING (organization_id = current_setting('app.current_organization_id')::BIGINT);
```

### 敏感数据加密
```sql
-- 身份证号加密函数
CREATE OR REPLACE FUNCTION encrypt_id_card(id_card TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN encode(encrypt(id_card::bytea, 'encryption_key', 'aes'), 'base64');
END;
$$ LANGUAGE plpgsql;

-- 身份证号解密函数
CREATE OR REPLACE FUNCTION decrypt_id_card(encrypted_id_card TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN convert_from(decrypt(decode(encrypted_id_card, 'base64'), 'encryption_key', 'aes'), 'UTF8');
END;
$$ LANGUAGE plpgsql;
```

## 📈 性能监控

### 慢查询监控
```sql
-- 启用查询统计
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- 查看慢查询
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
WHERE mean_time > 100  -- 平均执行时间超过100ms
ORDER BY total_time DESC
LIMIT 10;
```

### 表空间使用统计
```sql
-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 📚 相关资源

- [数据库设计总览](./index.md) - 数据库架构概述
- [多租户数据隔离](./multi-tenant-isolation.md) - 多租户实现方案
- [性能优化](./performance.md) - 数据库性能优化
- [多租户架构](../multi-tenant/) - 整体多租户设计

---

*最后更新：2025-07-01*