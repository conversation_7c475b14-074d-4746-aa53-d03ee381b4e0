#!/usr/bin/env node

/**
 * 测试 Public API 修复的完整验证脚本
 * 
 * 企业级系统 - 公开API安全性测试
 */

const https = require('http');

console.log('🧪 开始测试 Public API 修复...\n');

// 测试配置
const tests = [
  {
    name: '公开量表API - 无需认证',
    url: 'http://localhost:8181/api/multi-tenant/scales/public?page=0&size=1',
    expectAuth: false,
    expectStatus: 200
  },
  {
    name: '系统级量表API - 需要认证',
    url: 'http://localhost:8181/api/system/scales?page=0&size=1',
    expectAuth: true,
    expectStatus: 401
  },
  {
    name: '健康检查API - 公开访问',
    url: 'http://localhost:8181/api/health',
    expectAuth: false,
    expectStatus: 200
  },
  {
    name: '认证API - 公开访问',
    url: 'http://localhost:8181/api/multi-tenant/auth/login',
    expectAuth: false,
    expectStatus: 400, // POST请求，GET会返回400但不是401
    method: 'GET'
  }
];

// HTTP请求函数
function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// 运行测试
async function runTests() {
  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`🔍 测试: ${test.name}`);
      console.log(`   URL: ${test.url}`);
      
      const response = await makeRequest(test.url, test.method || 'GET');
      
      console.log(`   状态码: ${response.status}`);
      
      // 验证测试结果
      let testPassed = false;
      
      if (test.expectAuth && response.status === 401) {
        console.log('   ✅ 正确：需要认证的API返回401');
        testPassed = true;
      } else if (!test.expectAuth && response.status !== 401) {
        console.log('   ✅ 正确：公开API无需认证可访问');
        testPassed = true;
      } else if (test.expectStatus && response.status === test.expectStatus) {
        console.log('   ✅ 正确：返回预期状态码');
        testPassed = true;
      } else {
        console.log(`   ❌ 失败：期望状态码 ${test.expectStatus || (test.expectAuth ? 401 : '非401')}, 实际收到 ${response.status}`);
      }
      
      if (testPassed) {
        passedTests++;
        console.log('   🎉 测试通过\n');
      } else {
        console.log('   💥 测试失败\n');
      }
      
    } catch (error) {
      console.log(`   ❌ 请求失败: ${error.message}\n`);
    }
  }
  
  // 输出测试总结
  console.log('📊 测试总结:');
  console.log(`   通过: ${passedTests}/${totalTests}`);
  console.log(`   成功率: ${Math.round(passedTests/totalTests*100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！Public API修复成功！');
    console.log('\n✅ 企业级安全配置验证：');
    console.log('   - 公开API可无认证访问 ✓');
    console.log('   - 受保护API仍需认证 ✓');
    console.log('   - 安全边界配置正确 ✓');
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步检查配置');
  }
}

// 启动测试
console.log('等待服务完全启动...\n');
setTimeout(runTests, 2000);