# DrawDB集成使用指南

**版本**: v1.0  
**创建日期**: 2025年1月2日  
**适用环境**: 开发环境、测试环境  

---

## 📋 概述

本指南介绍如何在智慧养老评估平台中使用集成的DrawDB数据库设计器，实现可视化的数据库表结构设计功能。

## 🚀 快速开始

### 1. 环境搭建

#### 自动化设置（推荐）
```bash
# 一键设置DrawDB环境
./scripts/setup-drawdb.sh
```

#### 手动设置
```bash
# 1. 创建Docker网络
docker network create assessment_network

# 2. 启动DrawDB服务
cd docker
docker-compose -f docker-compose.drawdb.yml up -d

# 3. 验证服务
curl http://localhost:3001/health
```

### 2. 启动前端服务

```bash
# 启动Admin管理端
cd frontend/packages/admin
npm run dev
```

### 3. 访问数据库设计器

打开浏览器访问：`http://localhost:5173/assessment/database-design`

---

## 🎯 功能特性

### 主要功能

1. **🎨 可视化设计**
   - 拖拽式表格创建
   - 字段类型选择
   - 关系连线设计

2. **🤖 AI集成**
   - 从AI分析结果导入
   - 智能字段识别
   - SQL自动生成

3. **🔄 双环境支持**
   - 临时环境测试
   - 正式环境部署
   - 一键迁移功能

4. **📊 实时验证**
   - SQL语法检查
   - 性能评估
   - 最佳实践建议

---

## 📖 使用流程

### 流程一：从零开始设计

```mermaid
graph TD
    A[打开数据库设计器] --> B[创建新表]
    B --> C[拖拽添加字段]
    C --> D[设置字段属性]
    D --> E[建立表关系]
    E --> F[验证设计]
    F --> G[导出SQL]
    G --> H[部署到临时库]
    H --> I[测试验证]
    I --> J[迁移到正式库]
```

### 流程二：从AI分析导入

```mermaid
graph TD
    A[上传PDF量表] --> B[AI智能分析]
    B --> C[生成表结构建议]
    C --> D[加载到设计器]
    D --> E[可视化调整]
    E --> F[完善设计]
    F --> G[部署测试]
```

---

## 🛠️ 详细操作指南

### 1. 创建新的数据库设计

#### 步骤1: 打开设计器
- 登录系统后，导航到 `评估管理 > 数据库设计`
- 点击 `新建设计` 按钮

#### 步骤2: 设计表结构
- 在DrawDB界面中右键创建新表
- 双击表名进行重命名
- 添加字段并设置属性：
  ```
  字段名称: elderly_name
  数据类型: VARCHAR(100)
  是否允许空: false
  注释: 老人姓名
  ```

#### 步骤3: 建立表关系
- 拖拽字段到另一个表建立外键关系
- 设置关系类型（一对一、一对多等）

### 2. 从AI分析导入设计

#### 步骤1: 准备AI分析数据
- 在 `量表上传` 页面上传PDF文档
- 等待AI分析完成
- 获得量表结构分析结果

#### 步骤2: 导入到设计器
- 在数据库设计页面点击 `从AI分析加载`
- 系统自动将AI分析结果转换为可视化表格
- 根据需要调整和完善设计

### 3. 部署和测试

#### 部署到临时环境
```bash
# 方式1: 通过界面操作
点击 "部署到临时库" 按钮

# 方式2: 通过API调用
curl -X POST http://localhost:8181/api/scale-management/staging/create-table \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "CREATE TABLE...",
    "tableName": "test_table",
    "overwriteExisting": true
  }'
```

#### 验证部署结果
- 查看部署状态
- 检查表结构完整性
- 进行功能测试

### 4. 迁移到正式环境

```bash
# 通过API迁移
curl -X POST http://localhost:8181/api/scale-management/migrate-to-production \
  -H "Content-Type: application/json" \
  -d '{
    "tableName": "test_table",
    "overwriteExisting": false,
    "migrateData": true,
    "cleanupStaging": true
  }'
```

---

## ⚙️ 配置说明

### 环境变量配置

```bash
# frontend/packages/admin/.env.local
VITE_DRAWDB_URL=http://localhost:3001
VITE_DRAWDB_ENABLED=true
VITE_API_BASE_URL=http://localhost:8181/api
```

### DrawDB服务配置

```yaml
# docker/docker-compose.drawdb.yml
services:
  drawdb:
    ports:
      - "3001:80"
    environment:
      - NODE_ENV=production
      - API_BASE_URL=http://localhost:8181
```

---

## 🔧 管理命令

### 服务管理

```bash
# 启动服务
./scripts/manage-drawdb.sh start

# 停止服务
./scripts/manage-drawdb.sh stop

# 重启服务
./scripts/manage-drawdb.sh restart

# 查看状态
./scripts/manage-drawdb.sh status

# 查看日志
./scripts/manage-drawdb.sh logs

# 清理数据
./scripts/manage-drawdb.sh clean

# 更新镜像
./scripts/manage-drawdb.sh update
```

### 数据库管理

```bash
# 查看临时环境表列表
curl http://localhost:8181/api/scale-management/staging/tables

# 清理过期数据
curl -X POST http://localhost:8181/api/scale-management/staging/cleanup?retentionDays=7

# 检查表是否存在
curl http://localhost:8181/api/scale-management/table-exists/table_name?environment=staging
```

---

## 🐛 故障排除

### 常见问题

#### 1. DrawDB服务无法访问

**症状**: 浏览器显示连接拒绝或超时

**解决方案**:
```bash
# 检查服务状态
./scripts/manage-drawdb.sh status

# 查看日志
./scripts/manage-drawdb.sh logs

# 重启服务
./scripts/manage-drawdb.sh restart
```

#### 2. iframe显示空白

**症状**: 设计器区域显示空白或加载失败

**解决方案**:
1. 检查CORS配置
2. 验证DrawDB URL设置
3. 查看浏览器控制台错误

```javascript
// 检查配置
console.log('DrawDB URL:', import.meta.env.VITE_DRAWDB_URL)

// 检查iframe状态
const iframe = document.querySelector('.drawdb-iframe')
console.log('Iframe loaded:', iframe.contentDocument !== null)
```

#### 3. 与AI分析数据不兼容

**症状**: 导入AI数据时出错

**解决方案**:
1. 检查AI分析结果格式
2. 验证数据转换逻辑
3. 查看控制台错误信息

```typescript
// 调试AI数据转换
const convertAIAnalysisToSchema = (analysisData: any) => {
  console.log('Input data:', analysisData)
  
  const schema = {
    tables: [{
      name: analysisData.tableName || 'new_table',
      columns: analysisData.fields?.map((field: any) => ({
        name: field.name,
        type: field.type,
        nullable: field.nullable,
        primaryKey: field.isPrimaryKey,
        comment: field.comment
      })) || []
    }]
  }
  
  console.log('Converted schema:', schema)
  return schema
}
```

### 日志文件位置

```bash
# Docker容器日志
docker logs drawdb_container

# 前端控制台日志
# 在浏览器开发者工具中查看

# 后端API日志
tail -f logs/assessment.log
```

---

## 🔐 安全注意事项

### 1. 网络安全
- DrawDB服务仅限本地访问
- 生产环境需要配置适当的访问控制
- 使用HTTPS加密通信

### 2. 数据安全
- 临时环境数据定期清理
- 敏感信息不要硬编码在SQL中
- 定期备份重要设计

### 3. 权限控制
- 确保用户有适当的数据库设计权限
- 限制临时环境的数据访问
- 审计重要操作

---

## 📊 性能优化

### 1. 前端优化
- 懒加载DrawDB组件
- 缓存设计数据
- 防抖处理频繁操作

### 2. 后端优化
- 连接池配置优化
- SQL执行超时设置
- 批量操作支持

### 3. 数据库优化
- 合理的索引设计
- 定期清理临时数据
- 监控查询性能

---

## 📚 参考资源

### 官方文档
- [DrawDB GitHub](https://github.com/drawdb-io/drawdb)
- [Vue 3 文档](https://v3.vuejs.org/)
- [Element Plus 文档](https://element-plus.org/)

### 相关文件
- `frontend/packages/admin/src/components/DatabaseDesigner/DrawDBDesigner.vue`
- `frontend/packages/admin/src/views/DatabaseDesign.vue`
- `docker/docker-compose.drawdb.yml`
- `scripts/setup-drawdb.sh`

### API文档
- `/api/scale-management/*` - 量表管理API
- `/api/ai-sql-generation/*` - AI SQL生成API

---

## 🤝 支持与反馈

如果在使用过程中遇到问题或有改进建议，请：

1. 查看本文档的故障排除部分
2. 检查GitHub Issues
3. 联系开发团队

---

**文档结束**

*最后更新: 2025年1月2日*