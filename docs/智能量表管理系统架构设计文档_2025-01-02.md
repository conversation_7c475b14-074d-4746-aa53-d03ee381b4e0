# 智能量表管理系统架构设计文档

**文档版本**: v1.0  
**创建日期**: 2025年1月2日  
**最后更新**: 2025年1月2日  
**DrawDB集成**: ✅ 已完成iframe嵌入实现  
**作者**: 系统架构团队  

---

## 📋 项目概述

### 1.1 项目背景

智慧养老评估平台需要支持用户自主导入业务相关的量表到系统中。通过AI驱动的智能化流程，实现从PDF文档到数据库量表的全自动化转换，确保量表质量和数据安全性。

### 1.2 核心目标

- 🎯 **智能化转换**: PDF → MD → SQL → 数据库的全自动流程
- 🔒 **安全性保障**: 临时环境测试，确保数据质量
- 🎨 **用户友好**: 拖拽式编辑，可视化操作
- 🚀 **高效部署**: 测试通过后一键部署到正式环境

---

## 🏗️ 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "用户层"
        A[PDF文档上传] --> B[量表编辑器]
        B --> C[测试验证]
        C --> D[正式发布]
    end
    
    subgraph "AI处理层"
        E[Docling转换] --> F[AI分析]
        F --> G[SQL生成]
    end
    
    subgraph "数据存储层"
        H[(临时数据库<br/>PostgreSQL:5434)] --> I[(正式数据库<br/>PostgreSQL:5433)]
        J[(Redis缓存<br/>不同DB)]
    end
    
    subgraph "服务层"
        K[DatabaseService] --> L[ScaleTestingService]
        L --> M[MigrationService]
    end
    
    A --> E
    G --> H
    C --> L
    D --> M
    M --> I
```

### 2.2 核心流程设计

#### 2.2.1 PDF到量表转换流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant D as Docling服务
    participant A as AI分析服务
    participant S as 临时数据库
    participant P as 正式数据库
    
    U->>D: 上传PDF文档
    D->>A: 转换为MD格式
    A->>A: 智能分析结构
    A->>S: 生成SQL并创建临时表
    U->>S: 拖拽编辑字段
    U->>S: 填写测试数据
    S->>P: 验证通过后迁移
    P->>U: 发布到量表市场
```

#### 2.2.2 双数据库架构

```yaml
数据库架构:
  正式数据库:
    - 端口: 5433
    - 数据库: assessment_multitenant
    - 用途: 生产环境，正式量表存储
    - 特点: 高可用、备份、监控
    
  临时数据库:
    - 端口: 5434
    - 数据库: assessment_staging
    - 用途: 测试环境，量表验证
    - 特点: 7天自动清理、隔离测试
```

---

## 🔧 技术实现详解

### 3.1 双数据库配置

#### 3.1.1 配置类设计

```java
@Configuration
@Slf4j
public class DatabaseConfig {
    
    @Bean
    @Primary
    public DataSource primaryDataSource() {
        // 正式数据库配置
        // 连接池: 5-20个连接
        // 超时: 30秒
    }
    
    @Bean("stagingDataSource")
    public DataSource stagingDataSource() {
        // 临时数据库配置
        // 连接池: 2-10个连接
        // 自动清理: 7天
    }
}
```

#### 3.1.2 环境配置

```yaml
# application-staging.yml
assessment:
  database:
    staging:
      url: ***************************************************
      auto-cleanup:
        enabled: true
        retention-days: 7
        cleanup-schedule: "0 0 2 * * ?"
```

### 3.2 AI驱动的SQL生成

#### 3.2.1 流程设计

1. **文档解析**: Docling服务将PDF转换为结构化MD
2. **智能分析**: LM Studio分析量表结构和字段
3. **SQL生成**: 基于分析结果生成CREATE TABLE语句
4. **验证优化**: 自动检查字段类型和约束

#### 3.2.2 增强的DatabaseService

```java
@Service
public class EnhancedDatabaseService {
    
    @Qualifier("primaryJdbcTemplate")
    private final JdbcTemplate primaryJdbcTemplate;
    
    @Qualifier("stagingJdbcTemplate") 
    private final JdbcTemplate stagingJdbcTemplate;
    
    public ExecuteDDLResult executeDDLInStaging(ExecuteDDLRequest request) {
        // 在临时数据库中执行DDL
        return executeDDL(request, stagingJdbcTemplate);
    }
    
    public MigrationResult migrateToProduction(String tableName) {
        // 从临时环境迁移到正式环境
        return performMigration(tableName);
    }
}
```

### 3.3 拖拽式量表编辑器

#### 3.3.1 前端组件设计

```vue
<template>
  <div class="scale-builder">
    <!-- 字段库 -->
    <div class="field-palette">
      <draggable-field 
        v-for="fieldType in fieldTypes" 
        :key="fieldType.id"
        :field-type="fieldType"
        @dragstart="handleDragStart"
      />
    </div>
    
    <!-- 编辑画布 -->
    <div class="canvas-area" @drop="handleDrop" @dragover="handleDragOver">
      <draggable 
        v-model="formFields" 
        group="fields"
        @change="handleFieldChange"
      >
        <scale-field
          v-for="field in formFields"
          :key="field.id"
          :field="field"
          @update="updateField"
          @delete="deleteField"
        />
      </draggable>
    </div>
    
    <!-- 实时预览 -->
    <div class="preview-panel">
      <form-preview :fields="formFields" />
    </div>
  </div>
</template>
```

#### 3.3.2 字段类型支持

```javascript
const fieldTypes = [
  {
    id: 'text',
    name: '文本输入',
    icon: 'el-icon-edit',
    defaultProps: {
      label: '文本字段',
      placeholder: '请输入',
      maxLength: 255,
      required: false
    }
  },
  {
    id: 'number',
    name: '数字输入', 
    icon: 'el-icon-s-data',
    defaultProps: {
      label: '数字字段',
      min: 0,
      max: 100,
      step: 1,
      required: false
    }
  },
  {
    id: 'select',
    name: '下拉选择',
    icon: 'el-icon-arrow-down',
    defaultProps: {
      label: '选择字段',
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' }
      ],
      required: false
    }
  },
  {
    id: 'radio',
    name: '单选按钮',
    icon: 'el-icon-success',
    defaultProps: {
      label: '单选字段',
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      required: false
    }
  },
  {
    id: 'checkbox',
    name: '多选框',
    icon: 'el-icon-check',
    defaultProps: {
      label: '多选字段',
      options: [
        { label: '选项A', value: 'A' },
        { label: '选项B', value: 'B' }
      ],
      required: false
    }
  },
  {
    id: 'scale',
    name: '评分量表',
    icon: 'el-icon-star-on',
    defaultProps: {
      label: '评分字段',
      min: 1,
      max: 5,
      step: 1,
      labels: ['很差', '差', '一般', '好', '很好'],
      required: false
    }
  },
  {
    id: 'date',
    name: '日期选择',
    icon: 'el-icon-date',
    defaultProps: {
      label: '日期字段',
      format: 'YYYY-MM-DD',
      required: false
    }
  },
  {
    id: 'textarea',
    name: '多行文本',
    icon: 'el-icon-document',
    defaultProps: {
      label: '多行文本',
      rows: 4,
      maxLength: 1000,
      required: false
    }
  }
];
```

### 3.4 三阶段测试系统

#### 3.4.1 测试流程设计

```mermaid
graph TD
    A[量表创建完成] --> B[阶段1: 结构测试]
    B --> C{结构验证通过?}
    C -->|否| D[修改量表结构]
    D --> B
    C -->|是| E[阶段2: 填写测试]
    E --> F[用户填写测试数据]
    F --> G[验证数据完整性]
    G --> H{测试通过?}
    H -->|否| I[优化量表设计]
    I --> E
    H -->|是| J[阶段3: 性能测试]
    J --> K[并发测试]
    K --> L[数据查询测试]
    L --> M{性能达标?}
    M -->|否| N[优化索引结构]
    N --> J
    M -->|是| O[准备发布]
```

#### 3.4.2 测试服务实现

```java
@Service
@Slf4j
public class ScaleTestingService {
    
    public TestResult performStructureTest(String tableName) {
        // 阶段1: 结构测试
        List<String> issues = new ArrayList<>();
        
        // 检查表结构完整性
        if (!hasRequiredFields(tableName)) {
            issues.add("缺少必需字段");
        }
        
        // 检查字段类型合理性
        if (!validateFieldTypes(tableName)) {
            issues.add("字段类型不合理");
        }
        
        // 检查约束条件
        if (!validateConstraints(tableName)) {
            issues.add("约束条件不完整");
        }
        
        return TestResult.builder()
            .phase("STRUCTURE")
            .passed(issues.isEmpty())
            .issues(issues)
            .build();
    }
    
    public TestResult performDataTest(String tableName, List<Map<String, Object>> testData) {
        // 阶段2: 数据测试
        List<String> issues = new ArrayList<>();
        
        try {
            // 插入测试数据
            insertTestData(tableName, testData);
            
            // 验证数据完整性
            if (!validateDataIntegrity(tableName)) {
                issues.add("数据完整性验证失败");
            }
            
            // 验证计算逻辑
            if (!validateCalculations(tableName)) {
                issues.add("计算逻辑验证失败");
            }
            
        } catch (Exception e) {
            issues.add("数据插入失败: " + e.getMessage());
        }
        
        return TestResult.builder()
            .phase("DATA")
            .passed(issues.isEmpty())
            .issues(issues)
            .build();
    }
    
    public TestResult performPerformanceTest(String tableName) {
        // 阶段3: 性能测试
        List<String> issues = new ArrayList<>();
        
        // 并发插入测试
        long insertTime = testConcurrentInsert(tableName);
        if (insertTime > 5000) { // 5秒阈值
            issues.add("插入性能不达标: " + insertTime + "ms");
        }
        
        // 查询性能测试
        long queryTime = testQueryPerformance(tableName);
        if (queryTime > 1000) { // 1秒阈值
            issues.add("查询性能不达标: " + queryTime + "ms");
        }
        
        return TestResult.builder()
            .phase("PERFORMANCE")
            .passed(issues.isEmpty())
            .issues(issues)
            .build();
    }
}
```

---

## 🤖 AI Agent工具集成

### 4.1 n8n工作流适用性分析

#### 4.1.1 适合使用n8n的场景

```yaml
n8n工作流场景:
  批量处理流程:
    - 批量PDF转换
    - 定时数据清理
    - 批量量表导入
    
  审批流程:
    - 量表审核流程
    - 多级审批
    - 邮件通知
    
  数据同步:
    - 跨系统数据同步
    - 定时备份
    - 监控告警
    
  第三方集成:
    - 与外部系统对接
    - API数据交换
    - 文件传输
```

#### 4.1.2 不适合使用n8n的场景

```yaml
Spring Boot处理场景:
  实时交互:
    - 拖拽编辑器
    - 实时预览
    - 即时验证
    
  复杂业务逻辑:
    - 数据库事务
    - 权限控制
    - 复杂计算
    
  高性能要求:
    - 大数据量处理
    - 高并发访问
    - 实时响应
```

### 4.2 混合架构设计

```mermaid
graph TB
    subgraph "n8n工作流引擎"
        A[PDF批量处理] --> B[审批流程]
        B --> C[数据清理]
        C --> D[监控告警]
    end
    
    subgraph "Spring Boot核心服务"
        E[实时编辑器] --> F[数据库操作]
        F --> G[用户认证]
        G --> H[API服务]
    end
    
    subgraph "共享服务"
        I[消息队列] --> J[文件存储]
        J --> K[日志系统]
    end
    
    A --> I
    E --> I
    F --> J
    D --> K
```

---

## 📊 数据迁移策略

### 5.1 迁移流程设计

```java
@Service
@Slf4j
public class MigrationService {
    
    public MigrationResult migrateScale(String tableName) {
        MigrationResult result = new MigrationResult();
        
        try {
            // 1. 预迁移检查
            PreMigrationCheck check = performPreMigrationCheck(tableName);
            if (!check.isPassed()) {
                throw new MigrationException("预迁移检查失败: " + check.getIssues());
            }
            
            // 2. 创建备份
            String backupTableName = createBackup(tableName);
            result.setBackupTableName(backupTableName);
            
            // 3. 结构迁移
            migrateTableStructure(tableName);
            
            // 4. 数据迁移
            long migratedRows = migrateTableData(tableName);
            result.setMigratedRows(migratedRows);
            
            // 5. 验证迁移结果
            ValidationResult validation = validateMigration(tableName);
            if (!validation.isValid()) {
                // 回滚操作
                rollbackMigration(tableName, backupTableName);
                throw new MigrationException("迁移验证失败，已回滚");
            }
            
            // 6. 更新元数据
            updateScaleMetadata(tableName);
            
            // 7. 清理临时数据
            cleanupStagingData(tableName);
            
            result.setSuccess(true);
            result.setMessage("迁移成功完成");
            
        } catch (Exception e) {
            log.error("迁移失败: {}", tableName, e);
            result.setSuccess(false);
            result.setMessage("迁移失败: " + e.getMessage());
        }
        
        return result;
    }
}
```

### 5.2 数据一致性保证

```java
@Transactional
public class DataConsistencyService {
    
    public void ensureDataConsistency(String tableName) {
        // 1. 检查数据完整性
        checkDataIntegrity(tableName);
        
        // 2. 验证引用关系
        validateReferences(tableName);
        
        // 3. 校验计算字段
        validateCalculatedFields(tableName);
        
        // 4. 检查索引状态
        validateIndexes(tableName);
    }
}
```

---

## 🔍 监控与运维

### 6.1 监控指标设计

```yaml
监控指标:
  业务指标:
    - 量表创建成功率
    - 测试通过率
    - 迁移成功率
    - 用户活跃度
    
  技术指标:
    - 数据库连接池使用率
    - SQL执行时间
    - AI分析响应时间
    - 文件转换成功率
    
  资源指标:
    - CPU使用率
    - 内存使用率
    - 磁盘空间
    - 网络IO
```

### 6.2 告警策略

```java
@Component
public class AlertService {
    
    @EventListener
    public void handleMigrationFailure(MigrationFailureEvent event) {
        // 迁移失败告警
        sendAlert(AlertLevel.HIGH, "量表迁移失败", event.getDetails());
    }
    
    @EventListener
    public void handlePerformanceIssue(PerformanceIssueEvent event) {
        // 性能问题告警
        if (event.getResponseTime() > 5000) {
            sendAlert(AlertLevel.MEDIUM, "响应时间过长", event.getDetails());
        }
    }
}
```

---

## 🚀 部署与上线

### 7.1 部署环境配置

```yaml
部署环境:
  开发环境:
    - 单数据库
    - 简化配置
    - 完整日志
    
  临时环境:
    - 双数据库
    - 自动清理
    - 性能监控
    
  生产环境:
    - 高可用配置
    - 负载均衡
    - 备份策略
```

### 7.2 发布流程

```mermaid
graph TD
    A[代码提交] --> B[自动构建]
    B --> C[单元测试]
    C --> D[集成测试]
    D --> E[部署到临时环境]
    E --> F[功能验证]
    F --> G[性能测试]
    G --> H[安全扫描]
    H --> I[部署到生产环境]
    I --> J[健康检查]
    J --> K[监控告警]
```

---

## 📈 性能优化

### 8.1 数据库优化

```sql
-- 索引优化
CREATE INDEX CONCURRENTLY idx_scale_tests_table_name ON scale_tests(table_name);
CREATE INDEX CONCURRENTLY idx_scale_tests_created_at ON scale_tests(created_at);
CREATE INDEX CONCURRENTLY idx_scale_tests_status ON scale_tests(status);

-- 分区表设计
CREATE TABLE scale_test_logs (
    id BIGSERIAL,
    table_name VARCHAR(100),
    test_data JSONB,
    created_at TIMESTAMP
) PARTITION BY RANGE (created_at);

-- 按月分区
CREATE TABLE scale_test_logs_2025_01 PARTITION OF scale_test_logs
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 8.2 缓存策略

```java
@Service
public class ScaleCacheService {
    
    @Cacheable(value = "scale-structures", key = "#tableName")
    public ScaleStructure getScaleStructure(String tableName) {
        return databaseService.getTableStructure(tableName);
    }
    
    @CacheEvict(value = "scale-structures", key = "#tableName")
    public void invalidateScaleCache(String tableName) {
        log.info("清除量表缓存: {}", tableName);
    }
}
```

---

## 🔐 安全性设计

### 9.1 数据安全

```java
@Service
public class SecurityService {
    
    public void validateSqlSafety(String sql) {
        // SQL注入检查
        if (containsSqlInjection(sql)) {
            throw new SecurityException("检测到SQL注入风险");
        }
        
        // 危险操作检查
        if (containsDangerousOperations(sql)) {
            throw new SecurityException("包含危险操作");
        }
    }
    
    public void auditTableOperation(String operation, String tableName, String userId) {
        // 操作审计
        AuditLog auditLog = AuditLog.builder()
            .operation(operation)
            .tableName(tableName)
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();
            
        auditLogRepository.save(auditLog);
    }
}
```

### 9.2 访问控制

```java
@PreAuthorize("hasRole('SCALE_ADMIN') or hasPermission(#tableName, 'EDIT')")
public void editScale(String tableName, ScaleEditRequest request) {
    // 量表编辑权限控制
}

@PreAuthorize("hasRole('SCALE_ADMIN')")
public void migrateScale(String tableName) {
    // 迁移操作需要管理员权限
}
```

---

## 📋 实施计划

### 10.1 开发阶段

| 阶段 | 任务 | 预计时间 | 负责人 | 状态 |
|------|------|----------|--------|------|
| Phase 1 | 双数据库环境配置 | 2周 | 后端团队 | ✅ 进行中 |
| Phase 1 | AI SQL生成优化 | 1周 | AI团队 | ⏳ 待开始 |
| Phase 2 | Vue3拖拽编辑器 | 3周 | 前端团队 | ⏳ 待开始 |
| Phase 2 | 动态表单渲染 | 2周 | 前端团队 | ⏳ 待开始 |
| Phase 3 | 三阶段测试系统 | 2周 | 测试团队 | ⏳ 待开始 |
| Phase 4 | 生产部署 | 1周 | 运维团队 | ⏳ 待开始 |

### 10.2 里程碑节点

- **2025-01-16**: Phase 1完成，双数据库环境就绪
- **2025-02-06**: Phase 2完成，拖拽编辑器可用
- **2025-02-20**: Phase 3完成，测试系统上线
- **2025-02-27**: Phase 4完成，正式发布

---

## 🎯 风险评估与应对

### 11.1 技术风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| AI分析准确性 | 中 | 影响SQL生成质量 | 人工审核 + 模板库 |
| 数据迁移失败 | 高 | 数据丢失风险 | 完整备份 + 回滚机制 |
| 性能瓶颈 | 中 | 用户体验下降 | 性能测试 + 优化方案 |
| 安全漏洞 | 高 | 数据泄露风险 | 安全审计 + 权限控制 |

### 11.2 业务风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 用户接受度 | 中 | 功能使用率低 | 用户培训 + 界面优化 |
| 量表质量 | 中 | 评估结果不准确 | 专家审核 + 质量检查 |
| 系统稳定性 | 高 | 业务中断 | 监控告警 + 故障预案 |

---

## 📚 附录

### A. 技术选型对比

| 技术 | 选择方案 | 备选方案 | 选择理由 |
|------|----------|----------|----------|
| 数据库 | PostgreSQL | MySQL | JSON支持更好，性能优秀 |
| 前端框架 | Vue 3 | React | 团队熟悉度高，生态完善 |
| AI模型 | LM Studio | OpenAI API | 本地部署，数据安全 |
| 工作流 | n8n | Airflow | 可视化界面，学习成本低 |

### B. 数据库表结构设计

```sql
-- 量表元数据表
CREATE TABLE scale_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    version VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'DRAFT',
    table_name VARCHAR(100) UNIQUE NOT NULL,
    created_by UUID NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 量表测试记录表
CREATE TABLE scale_test_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scale_id UUID NOT NULL REFERENCES scale_metadata(id),
    test_phase VARCHAR(20) NOT NULL,
    test_result JSONB NOT NULL,
    passed BOOLEAN NOT NULL,
    issues TEXT[],
    tested_by UUID NOT NULL,
    tested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 迁移历史表
CREATE TABLE migration_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scale_id UUID NOT NULL REFERENCES scale_metadata(id),
    from_environment VARCHAR(20) NOT NULL,
    to_environment VARCHAR(20) NOT NULL,
    migration_result JSONB NOT NULL,
    migrated_rows BIGINT,
    backup_table_name VARCHAR(100),
    migrated_by UUID NOT NULL,
    migrated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### C. API接口设计

```yaml
# 量表管理API
/api/scales:
  POST: 创建新量表
  GET: 获取量表列表
  
/api/scales/{id}:
  GET: 获取量表详情
  PUT: 更新量表
  DELETE: 删除量表
  
/api/scales/{id}/test:
  POST: 执行量表测试
  GET: 获取测试结果
  
/api/scales/{id}/migrate:
  POST: 迁移量表到正式环境
  
/api/database/ddl:
  POST: 执行DDL语句
  
/api/ai/analyze:
  POST: AI分析量表结构
```

---

**文档结束**

*本文档将随着项目进展持续更新，请关注最新版本。*