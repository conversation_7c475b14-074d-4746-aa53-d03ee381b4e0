#!/bin/bash

# 测试超级管理员登录和权限
echo "1. 测试登录获取token..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:5274/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "tenantCode": "PLATFORM",
    "username": "superadmin",
    "password": "123456"
  }')

echo "登录响应:"
echo "$LOGIN_RESPONSE" | jq '.'

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.accessToken')
echo "提取的Token: ${TOKEN:0:50}..."

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ 登录失败或未获取到token"
  exit 1
fi

echo -e "\n2. 使用token访问系统API..."
API_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:5274/api/system/scales?page=0&size=1)

echo "API响应:"
echo "$API_RESPONSE" | jq '.'

# 检查响应状态
if echo "$API_RESPONSE" | grep -q "401"; then
  echo "❌ 仍然返回401错误"
  
  # 解析JWT token看看内容
  echo -e "\n3. 解析JWT Token内容..."
  PAYLOAD=$(echo "$TOKEN" | cut -d'.' -f2)
  # 添加padding如果需要
  PADDED=$(printf '%s' "$PAYLOAD" | sed 's/_/\//g; s/-/+/g')
  MOD=$((${#PADDED} % 4))
  if [ $MOD -ne 0 ]; then
    PADDED="${PADDED}$(printf '%.0s=' $(seq 1 $((4 - MOD))))"
  fi
  
  echo "Token Payload:"
  echo "$PADDED" | base64 -d | jq '.'
else
  echo "✅ API访问成功"
fi