#!/bin/bash

# 测试超级管理员登录和权限
echo "1. 测试登录获取token..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8181/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "tenantCode": "PLATFORM",
    "username": "superadmin",
    "password": "123456"
  }')

echo "登录响应(部分):"
echo "$LOGIN_RESPONSE" | jq '{accessToken: .accessToken[0:50], username, platformRole, isSuperAdmin}'

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.accessToken')
echo "提取的Token: ${TOKEN:0:50}..."

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ 登录失败或未获取到token"
  exit 1
fi

echo -e "\n2. 解析JWT Token内容..."
PAYLOAD=$(echo "$TOKEN" | cut -d'.' -f2)
# 添加padding如果需要
PADDED=$(printf '%s' "$PAYLOAD" | sed 's/_/\//g; s/-/+/g')
MOD=$((${#PADDED} % 4))
if [ $MOD -ne 0 ]; then
  PADDED="${PADDED}$(printf '%.0s=' $(seq 1 $((4 - MOD))))"
fi

echo "Token Payload:"
echo "$PADDED" | base64 -d | jq '.'

echo -e "\n3. 使用token访问系统API..."
API_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:8181/api/system/scales?page=0&size=1)

HTTP_STATUS=$(echo "$API_RESPONSE" | grep "HTTP_STATUS:" | cut -d':' -f2)
RESPONSE_BODY=$(echo "$API_RESPONSE" | sed '/HTTP_STATUS:/d')

echo "HTTP状态码: $HTTP_STATUS"
echo "API响应:"
echo "$RESPONSE_BODY" | jq '.' 2>/dev/null || echo "$RESPONSE_BODY"

if [ "$HTTP_STATUS" = "200" ]; then
  echo "✅ API访问成功"
else
  echo "❌ API访问失败 (HTTP $HTTP_STATUS)"
fi