# 临时数据库环境配置
# 用于量表测试和验证，支持独立的临时数据库
spring:
  application:
    name: assessment-platform-staging

  datasource:
    # 正式数据库（主数据源）
    url: *******************************************************
    username: assessment_user
    password: ${DB_PASSWORD:assessment123}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        show-sql: false

  data:
    redis:
      host: localhost
      port: 6379
      password: ${REDIS_PASSWORD:redis123}
      database: 1  # 使用不同的Redis数据库
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: -1ms

  cache:
    type: redis
    redis:
      time-to-live: 1800000  # 30分钟，临时数据缓存时间较短
      cache-null-values: false

server:
  port: 8182  # 使用不同端口，避免冲突

# 临时数据库配置
assessment:
  database:
    staging:
      url: ***************************************************
      username: assessment_user
      password: ${DB_STAGING_PASSWORD:assessment123}
      # 自动清理配置
      auto-cleanup:
        enabled: true
        retention-days: 7  # 7天后自动清理
        cleanup-schedule: "0 0 2 * * ?"  # 每天凌晨2点清理
  
  # 临时量表测试配置
  scale-testing:
    max-test-duration: 60  # 最大测试时长（分钟）
    auto-save-interval: 10  # 自动保存间隔（秒）
    validation-timeout: 300  # 验证超时时间（秒）
    
  # 量表迁移配置
  migration:
    batch-size: 100
    timeout: 600000  # 10分钟
    backup-enabled: true

# 开发工具配置
logging:
  level:
    root: INFO
    "[com.assessment]": DEBUG
    "[com.assessment.service.DatabaseService]": DEBUG
    "[com.assessment.service.ScaleTestingService]": DEBUG
  pattern:
    console: "[STAGING] %d{HH:mm:ss} - %msg%n"
    file: "[STAGING] %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/assessment-staging.log

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,staging-status
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      environment: staging

# 保持其他配置与主环境一致
minio:
  endpoint: ${MINIO_ENDPOINT:http://localhost:9000}
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  secret-key: ${MINIO_SECRET_KEY:minioadmin}
  bucket-name: assessment-staging-files  # 使用不同的存储桶
  secure: false

jwt:
  secret: ${JWT_SECRET:Ma4WhNJ/qBcz8b/VzxJN1CfFYkfVIIEys5+u1D8Y8CM=}
  expiration: 3600000  # 1小时，临时环境较短
  refresh-expiration: 86400000  # 24小时

docling:
  service:
    url: ${DOCLING_SERVICE_URL:http://localhost:8088}
    timeout: ${DOCLING_SERVICE_TIMEOUT:60}
    enabled: ${DOCLING_SERVICE_ENABLED:true}

ai:
  lmstudio:
    url: ${AI_LMSTUDIO_URL:http://*************:1234}
    model: ${AI_LMSTUDIO_MODEL:deepseek/deepseek-r1-0528-qwen3-8b}
  analysis:
    timeout: ${AI_ANALYSIS_TIMEOUT:300000}  # 5分钟，临时环境较短
    max-content-length: 100000
    enabled: ${AI_ANALYSIS_ENABLED:true}