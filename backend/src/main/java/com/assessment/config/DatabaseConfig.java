package com.assessment.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 双数据库配置类
 * 支持正式数据库和临时测试数据库
 */
@Configuration
@Slf4j
public class DatabaseConfig {

    @Value("${spring.datasource.url}")
    private String primaryUrl;
    
    @Value("${spring.datasource.username}")
    private String primaryUsername;
    
    @Value("${spring.datasource.password}")
    private String primaryPassword;
    
    // 临时数据库配置，使用同一个PostgreSQL实例的不同数据库
    @Value("${assessment.database.staging.url:***************************************************}")
    private String stagingUrl;
    
    @Value("${assessment.database.staging.username:${spring.datasource.username}}")
    private String stagingUsername;
    
    @Value("${assessment.database.staging.password:${spring.datasource.password}}")
    private String stagingPassword;

    /**
     * 正式数据库数据源（主数据源）
     */
    @Bean
    @Primary
    public DataSource primaryDataSource() {
        log.info("配置正式数据库数据源: {}", primaryUrl);
        
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(primaryUrl);
        config.setUsername(primaryUsername);
        config.setPassword(primaryPassword);
        config.setDriverClassName("org.postgresql.Driver");
        
        // 优化配置
        config.setMinimumIdle(5);
        config.setMaximumPoolSize(20);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        config.setConnectionTimeout(30000);
        config.setPoolName("PrimaryCP");
        config.setConnectionTestQuery("SELECT 1");
        config.setLeakDetectionThreshold(60000);
        
        return new HikariDataSource(config);
    }

    /**
     * 临时数据库数据源（用于测试）
     */
    @Bean("stagingDataSource")
    public DataSource stagingDataSource() {
        log.info("配置临时数据库数据源: {}", stagingUrl);
        
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(stagingUrl);
        config.setUsername(stagingUsername);
        config.setPassword(stagingPassword);
        config.setDriverClassName("org.postgresql.Driver");
        
        // 临时数据库使用较小的连接池
        config.setMinimumIdle(2);
        config.setMaximumPoolSize(10);
        config.setIdleTimeout(300000);
        config.setMaxLifetime(1800000);
        config.setConnectionTimeout(30000);
        config.setPoolName("StagingCP");
        config.setConnectionTestQuery("SELECT 1");
        config.setLeakDetectionThreshold(60000);
        
        return new HikariDataSource(config);
    }

    /**
     * 正式数据库JdbcTemplate
     */
    @Bean
    @Primary
    public JdbcTemplate primaryJdbcTemplate() {
        return new JdbcTemplate(primaryDataSource());
    }

    /**
     * 临时数据库JdbcTemplate
     */
    @Bean("stagingJdbcTemplate")
    public JdbcTemplate stagingJdbcTemplate() {
        return new JdbcTemplate(stagingDataSource());
    }

    /**
     * 正式数据库事务管理器
     */
    @Bean
    @Primary
    public PlatformTransactionManager transactionManager() {
        return new DataSourceTransactionManager(primaryDataSource());
    }

    /**
     * 临时数据库事务管理器
     */
    @Bean("stagingTransactionManager")
    public PlatformTransactionManager stagingTransactionManager() {
        return new DataSourceTransactionManager(stagingDataSource());
    }
}