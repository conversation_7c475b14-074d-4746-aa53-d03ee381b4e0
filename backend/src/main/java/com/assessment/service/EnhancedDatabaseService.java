package com.assessment.service;

import com.assessment.dto.ExecuteDDLRequest;
import com.assessment.dto.ExecuteDDLResult;
import com.assessment.dto.MigrationRequest;
import com.assessment.dto.MigrationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 增强的数据库操作服务
 * 支持双数据库环境：正式环境和临时测试环境
 */
@Service
@Slf4j
public class EnhancedDatabaseService {

    private final JdbcTemplate primaryJdbcTemplate;
    private final JdbcTemplate stagingJdbcTemplate;

    public EnhancedDatabaseService(
            JdbcTemplate primaryJdbcTemplate,
            @Qualifier("stagingJdbcTemplate") JdbcTemplate stagingJdbcTemplate) {
        this.primaryJdbcTemplate = primaryJdbcTemplate;
        this.stagingJdbcTemplate = stagingJdbcTemplate;
    }

    /**
     * 在临时数据库中执行DDL
     */
    @Transactional(transactionManager = "stagingTransactionManager")
    public ExecuteDDLResult executeDDLInStaging(final ExecuteDDLRequest request) {
        log.info("在临时数据库中执行DDL: 表名={}", request.getTableName());
        return executeDDL(request, stagingJdbcTemplate, "STAGING");
    }

    /**
     * 在正式数据库中执行DDL
     */
    @Transactional
    public ExecuteDDLResult executeDDLInProduction(final ExecuteDDLRequest request) {
        log.info("在正式数据库中执行DDL: 表名={}", request.getTableName());
        return executeDDL(request, primaryJdbcTemplate, "PRODUCTION");
    }

    /**
     * 将量表从临时环境迁移到正式环境
     */
    @Transactional
    public MigrationResult migrateScaleToProduction(final MigrationRequest request) {
        final long startTime = System.currentTimeMillis();
        final List<String> errors = new ArrayList<>();
        final List<String> warnings = new ArrayList<>();
        
        try {
            log.info("开始迁移量表: {} 从临时环境到正式环境", request.getTableName());

            // 1. 预迁移检查
            final PreMigrationCheck check = performPreMigrationCheck(request.getTableName());
            if (!check.isPassed()) {
                throw new RuntimeException("预迁移检查失败: " + String.join(", ", check.getIssues()));
            }

            // 2. 检查正式环境是否已存在表
            final boolean tableExistsInProduction = tableExists(request.getTableName(), primaryJdbcTemplate);
            String backupTableName = null;
            
            if (tableExistsInProduction) {
                if (request.getOverwriteExisting() != null && request.getOverwriteExisting()) {
                    // 创建备份
                    backupTableName = createBackupTable(request.getTableName(), primaryJdbcTemplate);
                    warnings.add("已备份原表为: " + backupTableName);
                    
                    // 删除原表
                    dropTable(request.getTableName(), primaryJdbcTemplate);
                    warnings.add("已删除原表: " + request.getTableName());
                } else {
                    throw new RuntimeException("正式环境中表 " + request.getTableName() + " 已存在，请选择覆盖或修改表名");
                }
            }

            // 3. 获取临时环境的表结构
            final String createTableSql = extractTableStructure(request.getTableName());
            
            // 4. 在正式环境中创建表
            primaryJdbcTemplate.execute(createTableSql);
            log.info("已在正式环境创建表: {}", request.getTableName());

            // 5. 迁移数据（如果需要）
            long migratedRows = 0;
            if (request.getMigrateData() != null && request.getMigrateData()) {
                migratedRows = migrateTableData(request.getTableName());
                log.info("已迁移 {} 行数据到正式环境", migratedRows);
            }

            // 6. 验证迁移结果
            final ValidationResult validation = validateMigration(request.getTableName());
            if (!validation.isValid()) {
                // 回滚操作
                rollbackMigration(request.getTableName(), backupTableName);
                throw new RuntimeException("迁移验证失败，已回滚: " + String.join(", ", validation.getIssues()));
            }

            // 7. 清理临时数据（如果配置了清理）
            if (request.getCleanupStaging() != null && request.getCleanupStaging()) {
                cleanupStagingData(request.getTableName());
                warnings.add("已清理临时环境数据");
            }

            final long executionTime = System.currentTimeMillis() - startTime;

            return MigrationResult.builder()
                    .success(true)
                    .message("迁移成功完成")
                    .tableName(request.getTableName())
                    .executionTimeMs(executionTime)
                    .migratedRows(migratedRows)
                    .backupTableName(backupTableName)
                    .warnings(warnings)
                    .errors(errors)
                    .migrationTime(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            final long executionTime = System.currentTimeMillis() - startTime;
            errors.add(e.getMessage());
            
            log.error("量表迁移失败: {}", request.getTableName(), e);

            return MigrationResult.builder()
                    .success(false)
                    .message("迁移失败: " + e.getMessage())
                    .tableName(request.getTableName())
                    .executionTimeMs(executionTime)
                    .migratedRows(0L)
                    .warnings(warnings)
                    .errors(errors)
                    .migrationTime(LocalDateTime.now())
                    .build();
        }
    }

    /**
     * 检查表是否存在
     */
    public boolean tableExistsInStaging(final String tableName) {
        return tableExists(tableName, stagingJdbcTemplate);
    }

    /**
     * 检查表在生产环境中是否存在
     * @param tableName 表名
     * @return 如果表存在返回true，否则返回false
     */
    public boolean tableExistsInProduction(final String tableName) {
        return tableExists(tableName, primaryJdbcTemplate);
    }

    /**
     * 获取表结构信息
     */
    public Map<String, Object> getTableStructureFromStaging(final String tableName) {
        return getTableStructure(tableName, stagingJdbcTemplate);
    }

    /**
     * 获取表结构信息（生产环境）
     * @param tableName 表名
     * @return 包含表结构信息的Map，包括表名、列信息等
     */
    public Map<String, Object> getTableStructureFromProduction(final String tableName) {
        return getTableStructure(tableName, primaryJdbcTemplate);
    }

    /**
     * 清理临时数据库中的过期数据
     */
    @Transactional(transactionManager = "stagingTransactionManager")
    public CleanupResult cleanupExpiredStagingData(final int retentionDays) {
        final List<String> cleanedTables = new ArrayList<>();
        final List<String> errors = new ArrayList<>();
        
        try {
            log.info("开始清理 {} 天前的临时数据", retentionDays);
            
            // 获取需要清理的表列表
            final String query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = CURRENT_SCHEMA() 
                AND table_name LIKE 'temp_%'
                """;
                
            final List<String> tempTables = stagingJdbcTemplate.queryForList(query, String.class);
            
            for (final String tableName : tempTables) {
                try {
                    // 检查表的创建时间（通过系统表）
                    if (isTableExpired(tableName, retentionDays)) {
                        dropTable(tableName, stagingJdbcTemplate);
                        cleanedTables.add(tableName);
                        log.info("已清理过期表: {}", tableName);
                    }
                } catch (Exception e) {
                    errors.add("清理表 " + tableName + " 失败: " + e.getMessage());
                    log.error("清理表失败: {}", tableName, e);
                }
            }
            
            return CleanupResult.builder()
                    .success(true)
                    .cleanedTableCount(cleanedTables.size())
                    .cleanedTables(cleanedTables)
                    .errors(errors)
                    .cleanupTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("清理临时数据失败", e);
            errors.add("清理操作失败: " + e.getMessage());
            
            return CleanupResult.builder()
                    .success(false)
                    .cleanedTableCount(0)
                    .cleanedTables(cleanedTables)
                    .errors(errors)
                    .cleanupTime(LocalDateTime.now())
                    .build();
        }
    }

    // ==================== 私有方法 ====================

    private ExecuteDDLResult executeDDL(final ExecuteDDLRequest request, 
                                        final JdbcTemplate jdbcTemplate, 
                                        final String environment) {
        final long startTime = System.currentTimeMillis();
        final List<String> errors = new ArrayList<>();
        final List<String> warnings = new ArrayList<>();
        boolean overwrittenExisting = false;
        String backupTableName = null;

        try {
            // 检查表是否存在
            final boolean tableExists = tableExists(request.getTableName(), jdbcTemplate);

            if (tableExists) {
                if (request.getOverwriteExisting() != null && request.getOverwriteExisting()) {
                    // 如果需要备份
                    if (request.getOptions() != null && request.getOptions().isBackupExistingTable()) {
                        backupTableName = createBackupTable(request.getTableName(), jdbcTemplate);
                        warnings.add("已备份原表为: " + backupTableName);
                    }

                    // 删除已存在的表
                    dropTable(request.getTableName(), jdbcTemplate);
                    overwrittenExisting = true;
                    warnings.add("已覆盖已存在的表: " + request.getTableName());
                } else {
                    throw new RuntimeException("表 " + request.getTableName() 
                        + " 在 " + environment + " 环境中已存在，请选择覆盖或修改表名");
                }
            }

            // 执行DDL语句
            log.debug("在{}环境执行SQL: {}", environment, request.getSql());
            jdbcTemplate.execute(request.getSql());

            final long executionTime = System.currentTimeMillis() - startTime;

            log.info("DDL在{}环境执行成功: 表名={}, 耗时={}ms", environment, request.getTableName(), executionTime);

            return ExecuteDDLResult.builder()
                    .success(true)
                    .message("表在" + environment + "环境创建成功")
                    .tableName(request.getTableName())
                    .executedSql(request.getSql())
                    .executionTimeMs(executionTime)
                    .executionTime(LocalDateTime.now())
                    .affectedRows(1)
                    .errors(errors)
                    .warnings(warnings)
                    .overwrittenExistingTable(overwrittenExisting)
                    .backupTableName(backupTableName)
                    .build();

        } catch (Exception e) {
            final long executionTime = System.currentTimeMillis() - startTime;
            errors.add(e.getMessage());

            log.error("DDL在{}环境执行失败: 表名={}", environment, request.getTableName(), e);

            return ExecuteDDLResult.builder()
                    .success(false)
                    .message("DDL在" + environment + "环境执行失败: " + e.getMessage())
                    .tableName(request.getTableName())
                    .executedSql(request.getSql())
                    .executionTimeMs(executionTime)
                    .executionTime(LocalDateTime.now())
                    .affectedRows(0)
                    .errors(errors)
                    .warnings(warnings)
                    .overwrittenExistingTable(overwrittenExisting)
                    .backupTableName(backupTableName)
                    .build();
        }
    }

    private boolean tableExists(final String tableName, final JdbcTemplate jdbcTemplate) {
        try {
            final String sql = "SELECT COUNT(*) FROM information_schema.tables " 
                + "WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()";
            final Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName.toLowerCase(Locale.ROOT));
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查表存在性失败: {}", tableName, e);
            return false;
        }
    }

    private Map<String, Object> getTableStructure(final String tableName, final JdbcTemplate jdbcTemplate) {
        try {
            final Map<String, Object> result = new HashMap<>();

            // 获取表信息
            final String tableInfoSql = "SELECT table_comment FROM information_schema.tables " 
                + "WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()";
            final String tableComment = jdbcTemplate.queryForObject(tableInfoSql, String.class, 
                tableName.toLowerCase(Locale.ROOT));
            result.put("tableName", tableName);
            result.put("tableComment", tableComment);

            // 获取列信息
            final String columnsSql = """
                SELECT
                    column_name,
                    data_type,
                    character_maximum_length,
                    is_nullable,
                    column_default,
                    column_comment
                FROM information_schema.columns
                WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()
                ORDER BY ordinal_position
                """;

            final List<Map<String, Object>> columns = jdbcTemplate.queryForList(columnsSql, 
                tableName.toLowerCase(Locale.ROOT));
            result.put("columns", columns);
            result.put("columnCount", columns.size());

            return result;

        } catch (Exception e) {
            log.error("获取表结构失败: {}", tableName, e);
            throw new RuntimeException("获取表结构失败: " + e.getMessage());
        }
    }

    private String createBackupTable(final String tableName, final JdbcTemplate jdbcTemplate) {
        final String backupTableName = tableName + "_backup_" + System.currentTimeMillis();
        try {
            final String sql = "CREATE TABLE " + backupTableName + " AS SELECT * FROM " + tableName;
            jdbcTemplate.execute(sql);
            log.info("已创建备份表: {}", backupTableName);
            return backupTableName;
        } catch (Exception e) {
            log.error("创建备份表失败: {}", tableName, e);
            throw new RuntimeException("创建备份表失败: " + e.getMessage());
        }
    }

    private void dropTable(final String tableName, final JdbcTemplate jdbcTemplate) {
        try {
            final String sql = "DROP TABLE IF EXISTS " + tableName + " CASCADE";
            jdbcTemplate.execute(sql);
            log.info("已删除表: {}", tableName);
        } catch (Exception e) {
            log.error("删除表失败: {}", tableName, e);
            throw new RuntimeException("删除表失败: " + e.getMessage());
        }
    }

    private PreMigrationCheck performPreMigrationCheck(final String tableName) {
        final List<String> issues = new ArrayList<>();
        
        try {
            // 检查临时环境中表是否存在
            if (!tableExists(tableName, stagingJdbcTemplate)) {
                issues.add("临时环境中表不存在: " + tableName);
            }
            
            // 检查表结构完整性
            final Map<String, Object> structure = getTableStructure(tableName, stagingJdbcTemplate);
            @SuppressWarnings("unchecked")
            final List<Map<String, Object>> columns = (List<Map<String, Object>>) structure.get("columns");
            
            if (columns == null || columns.isEmpty()) {
                issues.add("表结构为空或无法获取");
            } else {
                // 检查是否有必需字段
                final boolean hasIdField = columns.stream()
                        .anyMatch(col -> "id".equals(col.get("column_name")));
                if (!hasIdField) {
                    issues.add("缺少id主键字段");
                }
            }
            
        } catch (Exception e) {
            issues.add("预迁移检查异常: " + e.getMessage());
        }
        
        return PreMigrationCheck.builder()
                .passed(issues.isEmpty())
                .issues(issues)
                .build();
    }

    private String extractTableStructure(final String tableName) {
        // 这里应该从临时数据库中提取表的CREATE TABLE语句
        // 为简化，先返回一个基本的实现
        try {
            final String sql = """
                SELECT 
                    'CREATE TABLE ' || table_name || ' (' ||
                    string_agg(
                        column_name || ' ' || 
                        CASE 
                            WHEN data_type = 'character varying' THEN 'VARCHAR(' || character_maximum_length || ')'
                            WHEN data_type = 'text' THEN 'TEXT'
                            WHEN data_type = 'integer' THEN 'INTEGER'
                            WHEN data_type = 'bigint' THEN 'BIGINT'
                            WHEN data_type = 'boolean' THEN 'BOOLEAN'
                            WHEN data_type = 'timestamp without time zone' THEN 'TIMESTAMP'
                            WHEN data_type = 'uuid' THEN 'UUID'
                            ELSE UPPER(data_type)
                        END ||
                        CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END,
                        ', '
                    ) || ');'
                FROM information_schema.columns 
                WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()
                GROUP BY table_name
                """;
                
            return stagingJdbcTemplate.queryForObject(sql, String.class, tableName.toLowerCase(Locale.ROOT));
        } catch (Exception e) {
            log.error("提取表结构失败: {}", tableName, e);
            throw new RuntimeException("提取表结构失败: " + e.getMessage());
        }
    }

    private long migrateTableData(final String tableName) {
        try {
            // 获取临时环境数据
            final String selectSql = "SELECT * FROM " + tableName;
            final List<Map<String, Object>> data = stagingJdbcTemplate.queryForList(selectSql);
            
            if (data.isEmpty()) {
                return 0;
            }
            
            // 构建插入语句
            final Map<String, Object> firstRow = data.get(0);
            final String columns = String.join(", ", firstRow.keySet());
            final String placeholders = firstRow.keySet().stream()
                    .map(k -> "?")
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("");
                    
            final String insertSql = "INSERT INTO " + tableName + " (" + columns + ") VALUES (" + placeholders + ")";
            
            // 批量插入
            final List<Object[]> batchArgs = data.stream()
                    .map(row -> firstRow.keySet().stream()
                            .map(row::get)
                            .toArray())
                    .toList();
                    
            primaryJdbcTemplate.batchUpdate(insertSql, batchArgs);
            
            return data.size();
            
        } catch (Exception e) {
            log.error("数据迁移失败: {}", tableName, e);
            throw new RuntimeException("数据迁移失败: " + e.getMessage());
        }
    }

    private ValidationResult validateMigration(final String tableName) {
        final List<String> issues = new ArrayList<>();
        
        try {
            // 检查正式环境中表是否存在
            if (!tableExists(tableName, primaryJdbcTemplate)) {
                issues.add("正式环境中表不存在");
                return ValidationResult.builder()
                        .valid(false)
                        .issues(issues)
                        .build();
            }
            
            // 比较表结构
            final Map<String, Object> stagingStructure = getTableStructure(tableName, stagingJdbcTemplate);
            final Map<String, Object> productionStructure = getTableStructure(tableName, primaryJdbcTemplate);
            
            final Integer stagingColumnCount = (Integer) stagingStructure.get("columnCount");
            final Integer productionColumnCount = (Integer) productionStructure.get("columnCount");
            
            if (!stagingColumnCount.equals(productionColumnCount)) {
                issues.add("表结构列数不匹配: 临时环境=" + stagingColumnCount + ", 正式环境=" + productionColumnCount);
            }
            
        } catch (Exception e) {
            issues.add("迁移验证异常: " + e.getMessage());
        }
        
        return ValidationResult.builder()
                .valid(issues.isEmpty())
                .issues(issues)
                .build();
    }

    private void rollbackMigration(final String tableName, final String backupTableName) {
        try {
            // 删除新创建的表
            dropTable(tableName, primaryJdbcTemplate);
            
            // 恢复备份表（如果存在）
            if (backupTableName != null) {
                final String restoreSql = "ALTER TABLE " + backupTableName + " RENAME TO " + tableName;
                primaryJdbcTemplate.execute(restoreSql);
                log.info("已从备份恢复表: {}", tableName);
            }
            
        } catch (Exception e) {
            log.error("回滚操作失败: {}", tableName, e);
            throw new RuntimeException("回滚操作失败: " + e.getMessage());
        }
    }

    private void cleanupStagingData(final String tableName) {
        try {
            dropTable(tableName, stagingJdbcTemplate);
            log.info("已清理临时环境数据: {}", tableName);
        } catch (Exception e) {
            log.warn("清理临时环境数据失败: {}", tableName, e);
        }
    }

    private boolean isTableExpired(final String tableName, final int retentionDays) {
        // 简化实现：检查表名中的时间戳或者通过系统表查询创建时间
        // 这里先返回一个基本实现
        return tableName.startsWith("temp_") || tableName.startsWith("test_");
    }

    // ==================== 内部类 ====================
    
    @lombok.Builder
    @lombok.Data
    public static class PreMigrationCheck {
        private boolean passed;
        private List<String> issues;
    }
    
    @lombok.Builder
    @lombok.Data
    public static class ValidationResult {
        private boolean valid;
        private List<String> issues;
    }
    
    @lombok.Builder
    @lombok.Data
    public static class CleanupResult {
        private boolean success;
        private int cleanedTableCount;
        private List<String> cleanedTables;
        private List<String> errors;
        private LocalDateTime cleanupTime;
    }
}