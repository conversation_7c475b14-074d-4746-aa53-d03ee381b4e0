package com.assessment.service;

import com.assessment.dto.ScaleTestRequest;
import com.assessment.dto.ScaleTestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Executor;

/**
 * 量表三阶段测试服务
 * 提供结构测试、数据填入测试、性能测试的完整测试流程
 */
@Service
@Slf4j
public class ScaleTestingService {

    public static final int TOTAL_TEST_PHASES = 3;
    private static final int TEST_EXECUTOR_THREADS = 5;
    private static final long INSERT_BENCHMARK_MS = 5000L;
    private static final long QUERY_BENCHMARK_MS = 1000L;
    private static final long LARGE_DATA_BENCHMARK_MS = 10000L;
    private static final double MEMORY_WARNING_THRESHOLD = 0.8;
    private static final int MAX_FIELD_LENGTH = 5000;

    private final JdbcTemplate stagingJdbcTemplate;
    private final ExecutorService testExecutor;

    public ScaleTestingService(@Qualifier("stagingJdbcTemplate") JdbcTemplate stagingJdbcTemplate) {
        this.stagingJdbcTemplate = stagingJdbcTemplate;
        this.testExecutor = Executors.newFixedThreadPool(TEST_EXECUTOR_THREADS); // 测试线程池
    }

    /**
     * 执行完整的三阶段测试
     */
    public ScaleTestResult executeFullTest(final ScaleTestRequest request) {
        final long startTime = System.currentTimeMillis();
        final List<String> errors = new ArrayList<>();
        final List<String> warnings = new ArrayList<>();
        
        log.info("开始执行量表三阶段测试: {}", request.getTableName());

        try {
            // 阶段1: 结构测试
            log.info("执行阶段1: 结构测试");
            final StructureTestResult structureResult = executeStructureTest(request);
            
            if (!structureResult.isPassed()) {
                errors.addAll(structureResult.getErrors());
                return buildFailedResult(request, startTime, errors, warnings, "结构测试失败");
            }
            warnings.addAll(structureResult.getWarnings());

            // 阶段2: 数据填入测试
            log.info("执行阶段2: 数据填入测试");
            final DataFillTestResult dataResult = executeDataFillTest(request);
            
            if (!dataResult.isPassed()) {
                errors.addAll(dataResult.getErrors());
                return buildFailedResult(request, startTime, errors, warnings, "数据填入测试失败");
            }
            warnings.addAll(dataResult.getWarnings());

            // 阶段3: 性能测试
            log.info("执行阶段3: 性能测试");
            final PerformanceTestResult performanceResult = executePerformanceTest(request);
            
            if (!performanceResult.isPassed()) {
                errors.addAll(performanceResult.getErrors());
                return buildFailedResult(request, startTime, errors, warnings, "性能测试失败");
            }
            warnings.addAll(performanceResult.getWarnings());

            final long executionTime = System.currentTimeMillis() - startTime;
            
            log.info("量表三阶段测试全部通过: {}, 耗时: {}ms", request.getTableName(), executionTime);

            return ScaleTestResult.builder()
                    .success(true)
                    .message("三阶段测试全部通过")
                    .tableName(request.getTableName())
                    .executionTimeMs(executionTime)
                    .structureTestResult(structureResult)
                    .dataFillTestResult(dataResult)
                    .performanceTestResult(performanceResult)
                    .testPhasesPassed(TOTAL_TEST_PHASES)
                    .totalTestPhases(TOTAL_TEST_PHASES)
                    .errors(errors)
                    .warnings(warnings)
                    .testTime(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            errors.add("测试执行异常: " + e.getMessage());
            
            log.error("量表测试执行异常: {}", request.getTableName(), e);
            
            return buildFailedResult(request, startTime, errors, warnings, "测试执行异常: " + e.getMessage());
        }
    }

    /**
     * 执行结构测试
     */
    public StructureTestResult executeStructureTest(final ScaleTestRequest request) {
        final List<String> errors = new ArrayList<>();
        final List<String> warnings = new ArrayList<>();
        final Map<String, Object> testDetails = new HashMap<>();
        
        try {
            final String tableName = request.getTableName();
            
            // 1. 检查表是否存在
            if (!tableExists(tableName)) {
                errors.add("表不存在: " + tableName);
                return StructureTestResult.builder()
                        .passed(false)
                        .errors(errors)
                        .warnings(warnings)
                        .testDetails(testDetails)
                        .build();
            }
            
            // 2. 检查表结构
            final Map<String, Object> tableStructure = getTableStructure(tableName);
            testDetails.put("tableStructure", tableStructure);
            
            @SuppressWarnings("unchecked")
            final List<Map<String, Object>> columns = 
                (List<Map<String, Object>>) tableStructure.get("columns");
            
            if (columns == null || columns.isEmpty()) {
                errors.add("表结构为空或无法获取");
                return buildStructureTestResult(false, errors, warnings, testDetails);
            }
            
            // 3. 检查必需字段
            validateRequiredFields(columns, errors, warnings);
            
            // 4. 检查字段类型合理性
            validateFieldTypes(columns, errors, warnings);
            
            // 5. 检查索引
            validateIndexes(tableName, errors, warnings);
            
            // 6. 检查约束
            validateConstraints(tableName, errors, warnings);
            
            testDetails.put("columnCount", columns.size());
            testDetails.put("validationsPassed", errors.isEmpty());
            
            return buildStructureTestResult(errors.isEmpty(), errors, warnings, testDetails);
            
        } catch (Exception e) {
            errors.add("结构测试异常: " + e.getMessage());
            log.error("结构测试异常: {}", request.getTableName(), e);
            return buildStructureTestResult(false, errors, warnings, testDetails);
        }
    }

    /**
     * 执行数据填入测试
     */
    public DataFillTestResult executeDataFillTest(final ScaleTestRequest request) {
        final List<String> errors = new ArrayList<>();
        final List<String> warnings = new ArrayList<>();
        final Map<String, Object> testDetails = new HashMap<>();
        
        try {
            final String tableName = request.getTableName();
            
            // 1. 准备测试数据
            final List<Map<String, Object>> testData = generateTestData(tableName, request.getTestDataSize());
            testDetails.put("testDataSize", testData.size());
            
            // 2. 批量插入测试
            final long insertStartTime = System.currentTimeMillis();
            final int insertedRows = batchInsertTestData(tableName, testData);
            final long insertTime = System.currentTimeMillis() - insertStartTime;
            
            testDetails.put("insertedRows", insertedRows);
            testDetails.put("insertTimeMs", insertTime);
            
            if (insertedRows != testData.size()) {
                warnings.add("插入行数不匹配，预期: " + testData.size() + ", 实际: " + insertedRows);
            }
            
            // 3. 数据验证测试
            validateInsertedData(tableName, testData, errors, warnings);
            
            // 4. 查询测试
            final long queryStartTime = System.currentTimeMillis();
            final List<Map<String, Object>> queryResult = executeQueryTest(tableName);
            final long queryTime = System.currentTimeMillis() - queryStartTime;
            
            testDetails.put("queryTimeMs", queryTime);
            testDetails.put("queryResultCount", queryResult.size());
            
            // 5. 更新测试
            final long updateStartTime = System.currentTimeMillis();
            final int updatedRows = executeUpdateTest(tableName);
            final long updateTime = System.currentTimeMillis() - updateStartTime;
            
            testDetails.put("updatedRows", updatedRows);
            testDetails.put("updateTimeMs", updateTime);
            
            // 6. 删除测试数据
            final int deletedRows = cleanupTestData(tableName);
            testDetails.put("deletedRows", deletedRows);
            
            return DataFillTestResult.builder()
                    .passed(errors.isEmpty())
                    .errors(errors)
                    .warnings(warnings)
                    .testDetails(testDetails)
                    .insertPerformanceMs(insertTime)
                    .queryPerformanceMs(queryTime)
                    .updatePerformanceMs(updateTime)
                    .build();
            
        } catch (Exception e) {
            errors.add("数据填入测试异常: " + e.getMessage());
            log.error("数据填入测试异常: {}", request.getTableName(), e);
            return DataFillTestResult.builder()
                    .passed(false)
                    .errors(errors)
                    .warnings(warnings)
                    .testDetails(testDetails)
                    .build();
        }
    }

    /**
     * 执行性能测试
     */
    public PerformanceTestResult executePerformanceTest(final ScaleTestRequest request) {
        final List<String> errors = new ArrayList<>();
        final List<String> warnings = new ArrayList<>();
        final Map<String, Object> testDetails = new HashMap<>();
        
        try {
            final String tableName = request.getTableName();
            
            // 1. 并发插入测试
            final CompletableFuture<Long> concurrentInsertFuture = CompletableFuture.supplyAsync(() -> 
                testConcurrentInsert(tableName, request.getConcurrentUsers()), (Executor) testExecutor);
            
            // 2. 并发查询测试
            final CompletableFuture<Long> concurrentQueryFuture = CompletableFuture.supplyAsync(() -> 
                testConcurrentQuery(tableName, request.getConcurrentUsers()), (Executor) testExecutor);
            
            // 3. 大数据量测试
            final CompletableFuture<Long> largeDataFuture = CompletableFuture.supplyAsync(() -> 
                testLargeDataInsert(tableName, request.getLargeDataSize()), (Executor) testExecutor);
            
            // 等待所有测试完成
            final Long concurrentInsertTime = concurrentInsertFuture.get();
            final Long concurrentQueryTime = concurrentQueryFuture.get();
            final Long largeDataTime = largeDataFuture.get();
            
            testDetails.put("concurrentInsertTimeMs", concurrentInsertTime);
            testDetails.put("concurrentQueryTimeMs", concurrentQueryTime);
            testDetails.put("largeDataInsertTimeMs", largeDataTime);
            
            // 4. 性能基准检查
            validatePerformanceBenchmarks(concurrentInsertTime, concurrentQueryTime, largeDataTime, errors, warnings);
            
            // 5. 内存使用检查
            checkMemoryUsage(tableName, errors, warnings, testDetails);
            
            // 6. 清理测试数据
            cleanupPerformanceTestData(tableName);
            
            return PerformanceTestResult.builder()
                    .passed(errors.isEmpty())
                    .errors(errors)
                    .warnings(warnings)
                    .testDetails(testDetails)
                    .concurrentInsertTimeMs(concurrentInsertTime)
                    .concurrentQueryTimeMs(concurrentQueryTime)
                    .largeDataInsertTimeMs(largeDataTime)
                    .build();
            
        } catch (Exception e) {
            errors.add("性能测试异常: " + e.getMessage());
            log.error("性能测试异常: {}", request.getTableName(), e);
            return PerformanceTestResult.builder()
                    .passed(false)
                    .errors(errors)
                    .warnings(warnings)
                    .testDetails(testDetails)
                    .build();
        }
    }

    // ==================== 私有方法 ====================

    private boolean tableExists(final String tableName) {
        try {
            final String sql = "SELECT COUNT(*) FROM information_schema.tables " 
                + "WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()";
            final Integer count = stagingJdbcTemplate.queryForObject(sql, Integer.class, tableName.toLowerCase());
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查表存在性失败: {}", tableName, e);
            return false;
        }
    }

    private Map<String, Object> getTableStructure(final String tableName) {
        // 获取表结构信息，复用EnhancedDatabaseService中的逻辑
        final Map<String, Object> result = new HashMap<>();
        
        final String columnsSql = """
            SELECT
                column_name,
                data_type,
                character_maximum_length,
                is_nullable,
                column_default,
                column_comment
            FROM information_schema.columns
            WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()
            ORDER BY ordinal_position
            """;
            
        final List<Map<String, Object>> columns = 
            stagingJdbcTemplate.queryForList(columnsSql, tableName.toLowerCase());
        result.put("columns", columns);
        result.put("columnCount", columns.size());
        
        return result;
    }

    private void validateRequiredFields(final List<Map<String, Object>> columns, 
                                      final List<String> errors, final List<String> warnings) {
        // 检查是否有ID字段
        final boolean hasIdField = columns.stream()
                .anyMatch(col -> "id".equals(col.get("column_name")));
        if (!hasIdField) {
            errors.add("缺少id主键字段");
        }
        
        // 检查是否有审计字段
        final boolean hasCreatedAt = columns.stream()
                .anyMatch(col -> "created_at".equals(col.get("column_name")));
        final boolean hasUpdatedAt = columns.stream()
                .anyMatch(col -> "updated_at".equals(col.get("column_name")));
        
        if (!hasCreatedAt) {
            warnings.add("建议添加created_at审计字段");
        }
        if (!hasUpdatedAt) {
            warnings.add("建议添加updated_at审计字段");
        }
    }

    private void validateFieldTypes(final List<Map<String, Object>> columns,
                                  final List<String> errors, final List<String> warnings) {
        for (final Map<String, Object> column : columns) {
            final String columnName = (String) column.get("column_name");
            final String dataType = (String) column.get("data_type");
            
            // 检查文本字段长度
            if ("character varying".equals(dataType)) {
                final Integer maxLength = (Integer) column.get("character_maximum_length");
                if (maxLength != null && maxLength > MAX_FIELD_LENGTH) {
                    warnings.add("字段 " + columnName + " 长度过长: " + maxLength);
                }
            }
        }
    }

    private void validateIndexes(final String tableName, final List<String> errors, final List<String> warnings) {
        // 检查索引（简化实现）
        warnings.add("建议为常用查询字段添加索引");
    }

    private void validateConstraints(final String tableName, final List<String> errors, final List<String> warnings) {
        // 检查约束（简化实现）
        warnings.add("建议添加适当的检查约束");
    }

    private List<Map<String, Object>> generateTestData(final String tableName, final Integer testDataSize) {
        final List<Map<String, Object>> testData = new ArrayList<>();
        final int size = testDataSize != null ? testDataSize : 100;
        
        // 获取表结构以生成合适的测试数据
        final Map<String, Object> structure = getTableStructure(tableName);
        @SuppressWarnings("unchecked")
        final List<Map<String, Object>> columns = (List<Map<String, Object>>) structure.get("columns");
        
        for (int i = 0; i < size; i++) {
            final Map<String, Object> row = new HashMap<>();
            for (final Map<String, Object> column : columns) {
                final String columnName = (String) column.get("column_name");
                final String dataType = (String) column.get("data_type");
                
                // 生成测试数据
                row.put(columnName, generateTestValue(dataType, i));
            }
            testData.add(row);
        }
        
        return testData;
    }

    private Object generateTestValue(final String dataType, final int index) {
        return switch (dataType) {
            case "integer" -> index;
            case "bigint" -> (long) index;
            case "character varying", "text" -> "测试数据_" + index;
            case "boolean" -> index % 2 == 0;
            case "timestamp without time zone" -> LocalDateTime.now();
            default -> "默认值_" + index;
        };
    }

    private int batchInsertTestData(final String tableName, final List<Map<String, Object>> testData) {
        if (testData.isEmpty()) {
            return 0;
        }
        
        final Map<String, Object> firstRow = testData.get(0);
        final String columns = String.join(", ", firstRow.keySet());
        final String placeholders = firstRow.keySet().stream()
                .map(k -> "?")
                .reduce((a, b) -> a + ", " + b)
                .orElse("");
                
        final String insertSql = "INSERT INTO " + tableName + " (" + columns + ") VALUES (" + placeholders + ")";
        
        final List<Object[]> batchArgs = testData.stream()
                .map(row -> firstRow.keySet().stream()
                        .map(row::get)
                        .toArray())
                .toList();
                
        final int[] results = stagingJdbcTemplate.batchUpdate(insertSql, batchArgs);
        return results.length;
    }

    private void validateInsertedData(final String tableName, final List<Map<String, Object>> expectedData,
                                    final List<String> errors, final List<String> warnings) {
        try {
            final String countSql = "SELECT COUNT(*) FROM " + tableName;
            final Integer actualCount = stagingJdbcTemplate.queryForObject(countSql, Integer.class);
            
            if (actualCount == null || actualCount < expectedData.size()) {
                errors.add("插入数据验证失败，预期行数: " + expectedData.size() + ", 实际: " + actualCount);
            }
        } catch (Exception e) {
            errors.add("数据验证异常: " + e.getMessage());
        }
    }

    private List<Map<String, Object>> executeQueryTest(final String tableName) {
        final String sql = "SELECT * FROM " + tableName + " LIMIT 10";
        return stagingJdbcTemplate.queryForList(sql);
    }

    private int executeUpdateTest(final String tableName) {
        try {
            final String sql = "UPDATE " + tableName + " SET updated_at = CURRENT_TIMESTAMP WHERE id IS NOT NULL";
            return stagingJdbcTemplate.update(sql);
        } catch (Exception e) {
            log.warn("更新测试失败: {}", e.getMessage());
            return 0;
        }
    }

    private int cleanupTestData(final String tableName) {
        try {
            final String sql = "DELETE FROM " + tableName;
            return stagingJdbcTemplate.update(sql);
        } catch (Exception e) {
            log.warn("清理测试数据失败: {}", e.getMessage());
            return 0;
        }
    }

    private Long testConcurrentInsert(final String tableName, final Integer concurrentUsers) {
        final long startTime = System.currentTimeMillis();
        final int users = concurrentUsers != null ? concurrentUsers : 10;
        
        final List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < users; i++) {
            final int userId = i;
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    final List<Map<String, Object>> testData = generateTestData(tableName, 10);
                    batchInsertTestData(tableName, testData);
                } catch (Exception e) {
                    log.warn("并发插入测试失败 - 用户{}: {}", userId, e.getMessage());
                }
            }, testExecutor));
        }
        
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return System.currentTimeMillis() - startTime;
    }

    private Long testConcurrentQuery(final String tableName, final Integer concurrentUsers) {
        final long startTime = System.currentTimeMillis();
        final int users = concurrentUsers != null ? concurrentUsers : 10;
        
        final List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < users; i++) {
            final int userId = i;
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    executeQueryTest(tableName);
                } catch (Exception e) {
                    log.warn("并发查询测试失败 - 用户{}: {}", userId, e.getMessage());
                }
            }, testExecutor));
        }
        
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return System.currentTimeMillis() - startTime;
    }

    private Long testLargeDataInsert(final String tableName, final Integer largeDataSize) {
        final long startTime = System.currentTimeMillis();
        final int size = largeDataSize != null ? largeDataSize : 1000;
        
        try {
            final List<Map<String, Object>> largeTestData = generateTestData(tableName, size);
            batchInsertTestData(tableName, largeTestData);
        } catch (Exception e) {
            log.warn("大数据量测试失败: {}", e.getMessage());
        }
        
        return System.currentTimeMillis() - startTime;
    }

    private void validatePerformanceBenchmarks(final Long insertTime, final Long queryTime, final Long largeDataTime,
                                             final List<String> errors, final List<String> warnings) {
        // 性能基准检查（毫秒）
        final long insertBenchmark = INSERT_BENCHMARK_MS;
        final long queryBenchmark = QUERY_BENCHMARK_MS;
        final long largeDataBenchmark = LARGE_DATA_BENCHMARK_MS;
        
        if (insertTime > insertBenchmark) {
            warnings.add("并发插入性能较慢: " + insertTime + "ms (基准: " + insertBenchmark + "ms)");
        }
        
        if (queryTime > queryBenchmark) {
            warnings.add("并发查询性能较慢: " + queryTime + "ms (基准: " + queryBenchmark + "ms)");
        }
        
        if (largeDataTime > largeDataBenchmark) {
            errors.add("大数据量插入性能不达标: " + largeDataTime + "ms (基准: " + largeDataBenchmark + "ms)");
        }
    }

    private void checkMemoryUsage(final String tableName, final List<String> errors, 
                                final List<String> warnings, final Map<String, Object> testDetails) {
        // 内存使用检查（简化实现）
        final Runtime runtime = Runtime.getRuntime();
        final long freeMemory = runtime.freeMemory();
        final long totalMemory = runtime.totalMemory();
        final long usedMemory = totalMemory - freeMemory;
        
        testDetails.put("usedMemoryMB", usedMemory / (1024 * 1024));
        testDetails.put("freeMemoryMB", freeMemory / (1024 * 1024));
        
        if (usedMemory > totalMemory * MEMORY_WARNING_THRESHOLD) {
            warnings.add("内存使用率较高: " + (usedMemory * 100 / totalMemory) + "%");
        }
    }

    private void cleanupPerformanceTestData(final String tableName) {
        cleanupTestData(tableName);
    }

    private ScaleTestResult buildFailedResult(final ScaleTestRequest request, final long startTime,
                                            final List<String> errors, final List<String> warnings, 
                                            final String message) {
        final long executionTime = System.currentTimeMillis() - startTime;
        
        return ScaleTestResult.builder()
                .success(false)
                .message(message)
                .tableName(request.getTableName())
                .executionTimeMs(executionTime)
                .testPhasesPassed(0)
                .totalTestPhases(TOTAL_TEST_PHASES)
                .errors(errors)
                .warnings(warnings)
                .testTime(LocalDateTime.now())
                .build();
    }

    private StructureTestResult buildStructureTestResult(final boolean passed, final List<String> errors,
                                                       final List<String> warnings, 
                                                       final Map<String, Object> testDetails) {
        return StructureTestResult.builder()
                .passed(passed)
                .errors(errors)
                .warnings(warnings)
                .testDetails(testDetails)
                .build();
    }

    // ==================== 内部类 ====================

    @lombok.Builder
    @lombok.Data
    public static class StructureTestResult {
        private boolean passed;
        private List<String> errors;
        private List<String> warnings;
        private Map<String, Object> testDetails;
    }

    @lombok.Builder
    @lombok.Data
    public static class DataFillTestResult {
        private boolean passed;
        private List<String> errors;
        private List<String> warnings;
        private Map<String, Object> testDetails;
        private Long insertPerformanceMs;
        private Long queryPerformanceMs;
        private Long updatePerformanceMs;
    }

    @lombok.Builder
    @lombok.Data
    public static class PerformanceTestResult {
        private boolean passed;
        private List<String> errors;
        private List<String> warnings;
        private Map<String, Object> testDetails;
        private Long concurrentInsertTimeMs;
        private Long concurrentQueryTimeMs;
        private Long largeDataInsertTimeMs;
    }
}