package com.assessment.service;

import com.assessment.dto.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 增强的AI SQL生成服务
 * 专门优化针对量表的智能分析和SQL生成
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EnhancedAISQLGenerationService {

    private static final int DEFAULT_SCORE = 70;
    private static final int HIGH_SCORE = 80;
    private static final int MAX_RECOMMENDATIONS = 5;

    private final AIAnalysisService aiAnalysisService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    // SQL模板库
    private static final Map<String, String> SQL_TEMPLATES = new HashMap<>();
    
    static {
        // 评估量表模板
        SQL_TEMPLATES.put("assessment_scale", """
            CREATE TABLE %s (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                assessment_id UUID NOT NULL,
                %s
                total_score DECIMAL(10,2),
                completion_status VARCHAR(20) DEFAULT 'DRAFT',
                assessed_by UUID,
                assessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 索引优化
            CREATE INDEX idx_%s_assessment_id ON %s(assessment_id);
            CREATE INDEX idx_%s_assessed_at ON %s(assessed_at);
            CREATE INDEX idx_%s_status ON %s(completion_status);
            """);
            
        // 多维度评估模板
        SQL_TEMPLATES.put("multi_dimension_scale", """
            CREATE TABLE %s (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                assessment_id UUID NOT NULL,
                dimension_name VARCHAR(100) NOT NULL,
                %s
                dimension_score DECIMAL(10,2),
                dimension_weight DECIMAL(5,4) DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 复合索引优化
            CREATE INDEX idx_%s_assessment_dimension ON %s(assessment_id, dimension_name);
            CREATE INDEX idx_%s_score ON %s(dimension_score);
            """);
    }

    /**
     * 智能分析并生成优化的SQL
     */
    public SQLGenerationResult generateOptimizedSQL(final DocumentAnalysisRequest request) {
        final long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始智能SQL生成: 文件={}, 内容长度={}", 
                request.getFileName(), 
                request.getMarkdownContent().length());

            // 第一步：基础AI分析
            final DocumentAnalysisResult baseAnalysis = aiAnalysisService.analyzeDocument(request);
            
            if (!baseAnalysis.isSuccess()) {
                return createErrorResult("基础AI分析失败: " + baseAnalysis.getMessage(), startTime);
            }

            // 第二步：智能优化分析
            final OptimizationAnalysisResult optimization = performOptimizationAnalysis(request, baseAnalysis);
            
            // 第三步：生成优化的SQL
            final String optimizedSQL = generateOptimizedSQLFromAnalysis(baseAnalysis, optimization);
            
            // 第四步：验证SQL语法
            final SQLValidationResult validation = validateGeneratedSQL(optimizedSQL);
            
            // 第五步：生成建议和最佳实践
            final List<String> recommendations = generateRecommendations(baseAnalysis, optimization);
            
            final long executionTime = System.currentTimeMillis() - startTime;
            
            return SQLGenerationResult.builder()
                    .success(true)
                    .message("SQL生成成功")
                    .originalAnalysis(baseAnalysis)
                    .optimizedSQL(optimizedSQL)
                    .optimization(optimization)
                    .validation(validation)
                    .recommendations(recommendations)
                    .executionTimeMs(executionTime)
                    .generatedAt(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("智能SQL生成失败", e);
            return createErrorResult("SQL生成异常: " + e.getMessage(), startTime);
        }
    }

    /**
     * 执行优化分析
     */
    private OptimizationAnalysisResult performOptimizationAnalysis(
            final DocumentAnalysisRequest request, 
            final DocumentAnalysisResult baseAnalysis) {
        
        try {
            final String optimizationPrompt = buildOptimizationPrompt(request, baseAnalysis);
            final String aiResponse = aiAnalysisService.chatWithAI(optimizationPrompt, "量表SQL优化分析");
            
            return parseOptimizationResponse(aiResponse);
            
        } catch (Exception e) {
            log.warn("优化分析失败，使用默认优化策略", e);
            return createDefaultOptimization(baseAnalysis);
        }
    }

    /**
     * 构建优化分析提示词
     */
    private String buildOptimizationPrompt(final DocumentAnalysisRequest request, 
                                           final DocumentAnalysisResult baseAnalysis) {
        return String.format("""
            # 量表数据库优化分析任务
            
            你是一位数据库性能优化专家。请分析已有的量表设计，提供优化建议。
            
            ## 原始量表分析结果
            表名: %s
            字段数量: %d
            
            ## 原始字段列表
            %s
            
            ## 优化分析要求
            
            请严格按照以下JSON格式输出分析结果：
            
            ```json
            {
              "scaleType": "assessment_scale|multi_dimension_scale|custom_scale",
              "complexityLevel": "simple|medium|complex",
              "recommendedTemplate": "模板名称",
              "indexStrategy": {
                "primaryIndexes": ["字段1", "字段2"],
                "compositeIndexes": [
                  {"name": "索引名", "columns": ["字段1", "字段2"], "unique": false}
                ],
                "performanceIndexes": ["高频查询字段"]
              },
              "dataTypeOptimizations": [
                {"field": "字段名", "currentType": "当前类型", "recommendedType": "推荐类型", "reason": "优化原因"}
              ],
              "partitionStrategy": {
                "enabled": false,
                "type": "range|hash|list",
                "column": "分区字段",
                "strategy": "分区策略说明"
              },
              "constraintRecommendations": [
                {"type": "check|foreign_key|unique", "field": "字段名", "constraint": "约束表达式", "reason": "约束原因"}
              ],
              "performanceScore": 85,
              "optimizationSummary": "详细的优化总结"
            }
            ```
            
            ## 优化重点
            1. 识别量表类型（单维度/多维度评估）
            2. 优化字段数据类型
            3. 设计高效索引策略
            4. 评估是否需要分区
            5. 建议数据约束
            6. 评估性能得分
            """, 
            baseAnalysis.getTableName(),
            baseAnalysis.getFields().size(),
            formatFieldsForPrompt(baseAnalysis.getFields())
        );
    }

    /**
     * 格式化字段信息用于提示词
     */
    private String formatFieldsForPrompt(final List<DocumentAnalysisResult.DatabaseField> fields) {
        final StringBuilder sb = new StringBuilder();
        for (final DocumentAnalysisResult.DatabaseField field : fields) {
            sb.append(String.format("- %s (%s): %s\n", 
                field.getName(), 
                field.getType(), 
                field.getComment()));
        }
        return sb.toString();
    }

    /**
     * 解析优化响应
     */
    private OptimizationAnalysisResult parseOptimizationResponse(final String aiResponse) {
        try {
            final String jsonString = extractJSON(aiResponse);
            if (jsonString.isEmpty()) {
                throw new RuntimeException("未找到有效的优化分析JSON");
            }
            
            final JsonNode rootNode = objectMapper.readTree(jsonString);
            
            return OptimizationAnalysisResult.builder()
                    .scaleType(getStringValue(rootNode, "scaleType", "custom_scale"))
                    .complexityLevel(getStringValue(rootNode, "complexityLevel", "medium"))
                    .recommendedTemplate(getStringValue(rootNode, "recommendedTemplate", "assessment_scale"))
                    .indexStrategy(parseIndexStrategy(rootNode.get("indexStrategy")))
                    .dataTypeOptimizations(parseDataTypeOptimizations(rootNode.get("dataTypeOptimizations")))
                    .partitionStrategy(parsePartitionStrategy(rootNode.get("partitionStrategy")))
                    .constraintRecommendations(
                        parseConstraintRecommendations(rootNode.get("constraintRecommendations")))
                    .performanceScore(getIntValue(rootNode, "performanceScore", DEFAULT_SCORE))
                    .optimizationSummary(getStringValue(rootNode, "optimizationSummary", "标准优化"))
                    .build();
                    
        } catch (JsonProcessingException e) {
            log.error("解析优化分析JSON失败", e);
            throw new RuntimeException("解析优化分析失败: " + e.getMessage());
        }
    }

    /**
     * 创建默认优化策略
     */
    private OptimizationAnalysisResult createDefaultOptimization(final DocumentAnalysisResult baseAnalysis) {
        return OptimizationAnalysisResult.builder()
                .scaleType("assessment_scale")
                .complexityLevel("medium")
                .recommendedTemplate("assessment_scale")
                .indexStrategy(OptimizationAnalysisResult.IndexStrategy.builder()
                    .primaryIndexes(Arrays.asList("id", "assessment_id"))
                    .performanceIndexes(Arrays.asList("created_at", "updated_at"))
                    .build())
                .performanceScore(DEFAULT_SCORE)
                .optimizationSummary("使用默认优化策略")
                .build();
    }

    /**
     * 根据分析结果生成优化的SQL
     */
    private String generateOptimizedSQLFromAnalysis(
            final DocumentAnalysisResult baseAnalysis, 
            final OptimizationAnalysisResult optimization) {
        
        final String templateName = optimization.getRecommendedTemplate();
        final String template = SQL_TEMPLATES.get(templateName);
        
        if (template == null) {
            log.warn("未找到模板: {}, 使用默认模板", templateName);
            return generateCustomSQL(baseAnalysis, optimization);
        }
        
        // 生成字段定义
        final String fieldDefinitions = generateFieldDefinitions(baseAnalysis.getFields(), optimization);
        
        // 应用模板
        final String tableName = baseAnalysis.getTableName();
        final String sql = String.format(template, 
            tableName,           // 表名
            fieldDefinitions,    // 字段定义
            tableName,           // 索引中的表名
            tableName,           // 索引中的表名
            tableName,           // 索引中的表名
            tableName,           // 索引中的表名
            tableName,           // 索引中的表名
            tableName            // 索引中的表名
        );
        
        // 添加额外的索引和约束
        final StringBuilder finalSQL = new StringBuilder(sql);
        finalSQL.append("\n\n-- 额外优化索引\n");
        finalSQL.append(generateAdditionalIndexes(tableName, optimization));
        
        if (optimization.getConstraintRecommendations() != null) {
            finalSQL.append("\n\n-- 数据约束\n");
            finalSQL.append(generateConstraints(tableName, optimization.getConstraintRecommendations()));
        }
        
        // 添加注释
        finalSQL.append("\n\n-- 生成时间: ").append(LocalDateTime.now());
        finalSQL.append("\n-- 优化等级: ").append(optimization.getPerformanceScore()).append("/100");
        finalSQL.append("\n-- 复杂度: ").append(optimization.getComplexityLevel());
        
        return finalSQL.toString();
    }

    /**
     * 生成字段定义
     */
    private String generateFieldDefinitions(
            final List<DocumentAnalysisResult.DatabaseField> fields,
            final OptimizationAnalysisResult optimization) {
        
        final StringBuilder sb = new StringBuilder();
        
        for (final DocumentAnalysisResult.DatabaseField field : fields) {
            // 跳过系统字段，它们在模板中已包含
            if (isSystemField(field.getName())) {
                continue;
            }
            
            // 应用数据类型优化
            String optimizedType = applyDataTypeOptimization(field, optimization);
            
            sb.append("    ").append(field.getName())
              .append(" ").append(optimizedType);
              
            // 添加约束
            if (!field.getNullable()) {
                sb.append(" NOT NULL");
            }
            
            if (field.getDefaultValue() != null && !field.getDefaultValue().isEmpty()) {
                sb.append(" DEFAULT ").append(field.getDefaultValue());
            }
            
            // 添加注释
            if (field.getComment() != null && !field.getComment().isEmpty()) {
                sb.append(" -- ").append(field.getComment());
            }
            
            sb.append(",\n");
        }
        
        return sb.toString();
    }

    /**
     * 应用数据类型优化
     */
    private String applyDataTypeOptimization(
            final DocumentAnalysisResult.DatabaseField field,
            final OptimizationAnalysisResult optimization) {
        
        if (optimization.getDataTypeOptimizations() != null) {
            for (final OptimizationAnalysisResult.DataTypeOptimization opt : optimization.getDataTypeOptimizations()) {
                if (field.getName().equals(opt.getField())) {
                    log.info("优化字段 {} 类型: {} -> {}, 原因: {}", 
                        field.getName(), opt.getCurrentType(), opt.getRecommendedType(), opt.getReason());
                    return opt.getRecommendedType();
                }
            }
        }
        
        return field.getType();
    }

    /**
     * 生成额外的索引
     */
    private String generateAdditionalIndexes(final String tableName, final OptimizationAnalysisResult optimization) {
        final StringBuilder sb = new StringBuilder();
        
        if (optimization.getIndexStrategy() != null) {
            final OptimizationAnalysisResult.IndexStrategy strategy = optimization.getIndexStrategy();
            
            // 复合索引
            if (strategy.getCompositeIndexes() != null) {
                for (final OptimizationAnalysisResult.CompositeIndex index : strategy.getCompositeIndexes()) {
                    sb.append(String.format("CREATE%s INDEX %s ON %s(%s);\n",
                        index.isUnique() ? " UNIQUE" : "",
                        index.getName(),
                        tableName,
                        String.join(", ", index.getColumns())
                    ));
                }
            }
            
            // 性能索引
            if (strategy.getPerformanceIndexes() != null) {
                for (final String column : strategy.getPerformanceIndexes()) {
                    sb.append(String.format("CREATE INDEX idx_%s_%s ON %s(%s);\n",
                        tableName, column, tableName, column));
                }
            }
        }
        
        return sb.toString();
    }

    /**
     * 生成约束
     */
    private String generateConstraints(final String tableName, 
            final List<OptimizationAnalysisResult.ConstraintRecommendation> constraints) {
        
        final StringBuilder sb = new StringBuilder();
        
        for (final OptimizationAnalysisResult.ConstraintRecommendation constraint : constraints) {
            switch (constraint.getType().toLowerCase()) {
                case "check":
                    sb.append(String.format("ALTER TABLE %s ADD CONSTRAINT chk_%s_%s CHECK (%s);\n",
                        tableName, tableName, constraint.getField(), constraint.getConstraint()));
                    break;
                case "unique":
                    sb.append(String.format("ALTER TABLE %s ADD CONSTRAINT uk_%s_%s UNIQUE (%s);\n",
                        tableName, tableName, constraint.getField(), constraint.getField()));
                    break;
                case "foreign_key":
                    sb.append(String.format("ALTER TABLE %s ADD CONSTRAINT fk_%s_%s " 
                        + "FOREIGN KEY (%s) REFERENCES %s;\n",
                        tableName, tableName, constraint.getField(), 
                        constraint.getField(), constraint.getConstraint()));
                    break;
            }
        }
        
        return sb.toString();
    }

    /**
     * 生成自定义SQL（当没有合适模板时）
     */
    private String generateCustomSQL(final DocumentAnalysisResult baseAnalysis, 
                                     final OptimizationAnalysisResult optimization) {
        final StringBuilder sb = new StringBuilder();
        
        sb.append("CREATE TABLE ").append(baseAnalysis.getTableName()).append(" (\n");
        sb.append("    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n");
        
        // 添加业务字段
        for (final DocumentAnalysisResult.DatabaseField field : baseAnalysis.getFields()) {
            if (!isSystemField(field.getName())) {
                sb.append("    ").append(field.getName())
                  .append(" ").append(field.getType());
                  
                if (!field.getNullable()) {
                    sb.append(" NOT NULL");
                }
                
                if (field.getDefaultValue() != null) {
                    sb.append(" DEFAULT ").append(field.getDefaultValue());
                }
                
                sb.append(",\n");
            }
        }
        
        sb.append("    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n");
        sb.append("    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n");
        sb.append(");\n");
        
        return sb.toString();
    }

    /**
     * 验证生成的SQL
     */
    private SQLValidationResult validateGeneratedSQL(final String sql) {
        final List<String> errors = new ArrayList<>();
        final List<String> warnings = new ArrayList<>();
        
        // 基本语法检查
        if (sql == null || sql.trim().isEmpty()) {
            errors.add("SQL为空");
            return SQLValidationResult.builder()
                .valid(false)
                .errors(errors)
                .warnings(warnings)
                .build();
        }
        
        // 检查CREATE TABLE语句
        if (!sql.toUpperCase().contains("CREATE TABLE")) {
            errors.add("缺少CREATE TABLE语句");
        }
        
        // 检查主键
        if (!sql.toUpperCase().contains("PRIMARY KEY")) {
            warnings.add("建议添加主键");
        }
        
        // 检查时间戳字段
        if (!sql.toLowerCase().contains("created_at") || !sql.toLowerCase().contains("updated_at")) {
            warnings.add("建议添加审计字段(created_at, updated_at)");
        }
        
        // 检查索引
        if (!sql.toUpperCase().contains("CREATE INDEX")) {
            warnings.add("建议添加适当的索引以提高查询性能");
        }
        
        return SQLValidationResult.builder()
                .valid(errors.isEmpty())
                .errors(errors)
                .warnings(warnings)
                .score(calculateValidationScore(errors, warnings))
                .build();
    }

    /**
     * 生成建议和最佳实践
     */
    private List<String> generateRecommendations(final DocumentAnalysisResult baseAnalysis, 
            final OptimizationAnalysisResult optimization) {
        
        final List<String> recommendations = new ArrayList<>();
        
        // 基于复杂度的建议
        if ("complex".equals(optimization.getComplexityLevel())) {
            recommendations.add("考虑将复杂量表拆分为多个相关表，提高查询性能");
            recommendations.add("建议实施分区策略以处理大量历史数据");
            recommendations.add("考虑使用读写分离来提高并发性能");
        }
        
        // 基于字段数量的建议
        if (baseAnalysis.getFields().size() > 20) {
            recommendations.add("字段较多，建议考虑垂直分表策略");
            recommendations.add("对于不经常使用的字段，考虑使用JSONB存储");
        }
        
        // 基于性能得分的建议
        if (optimization.getPerformanceScore() < HIGH_SCORE) {
            recommendations.add("当前设计性能得分较低，建议优化索引策略");
            recommendations.add("考虑添加适当的数据约束以确保数据质量");
        }
        
        // 通用最佳实践
        recommendations.add("建议定期分析表统计信息以保持最佳查询性能");
        recommendations.add("考虑实施数据归档策略以控制表大小");
        recommendations.add("建议在生产环境中监控查询性能并根据需要调整索引");
        
        return recommendations;
    }

    // ==================== 工具方法 ====================

    private boolean isSystemField(final String fieldName) {
        return Arrays.asList("id", "created_at", "updated_at", "assessment_id", 
                           "total_score", "completion_status", "assessed_by", "assessed_at")
                     .contains(fieldName.toLowerCase());
    }

    private String extractJSON(final String response) {
        final Pattern pattern = Pattern.compile("```json\\s*([\\s\\S]*?)\\s*```");
        final Matcher matcher = pattern.matcher(response);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return "";
    }

    private String getStringValue(final JsonNode node, final String fieldName, final String defaultValue) {
        final JsonNode valueNode = node.get(fieldName);
        return (valueNode != null && !valueNode.isNull()) ? valueNode.asText() : defaultValue;
    }

    private Integer getIntValue(final JsonNode node, final String fieldName, final Integer defaultValue) {
        final JsonNode valueNode = node.get(fieldName);
        try {
            if (valueNode != null && !valueNode.isNull() && valueNode.canConvertToInt()) {
                return valueNode.asInt();
            }
            return defaultValue;
        } catch (Exception e) {
            log.warn("无法将字段 '{}' 的值解析为整数", fieldName);
            return defaultValue;
        }
    }

    private OptimizationAnalysisResult.IndexStrategy parseIndexStrategy(final JsonNode node) {
        if (node == null || node.isNull()) {
            return OptimizationAnalysisResult.IndexStrategy.builder().build();
        }
        
        final OptimizationAnalysisResult.IndexStrategy.IndexStrategyBuilder builder = 
            OptimizationAnalysisResult.IndexStrategy.builder();
        
        // 解析主要索引
        final JsonNode primaryIndexes = node.get("primaryIndexes");
        if (primaryIndexes != null && primaryIndexes.isArray()) {
            final List<String> indexes = new ArrayList<>();
            primaryIndexes.forEach(index -> indexes.add(index.asText()));
            builder.primaryIndexes(indexes);
        }
        
        // 解析性能索引
        final JsonNode performanceIndexes = node.get("performanceIndexes");
        if (performanceIndexes != null && performanceIndexes.isArray()) {
            final List<String> indexes = new ArrayList<>();
            performanceIndexes.forEach(index -> indexes.add(index.asText()));
            builder.performanceIndexes(indexes);
        }
        
        // 解析复合索引
        final JsonNode compositeIndexes = node.get("compositeIndexes");
        if (compositeIndexes != null && compositeIndexes.isArray()) {
            final List<OptimizationAnalysisResult.CompositeIndex> indexes = new ArrayList<>();
            for (final JsonNode indexNode : compositeIndexes) {
                final OptimizationAnalysisResult.CompositeIndex.CompositeIndexBuilder indexBuilder = 
                    OptimizationAnalysisResult.CompositeIndex.builder()
                        .name(getStringValue(indexNode, "name", ""))
                        .unique(indexNode.get("unique") != null && indexNode.get("unique").asBoolean());
                
                final JsonNode columns = indexNode.get("columns");
                if (columns != null && columns.isArray()) {
                    final List<String> columnList = new ArrayList<>();
                    columns.forEach(col -> columnList.add(col.asText()));
                    indexBuilder.columns(columnList);
                }
                
                indexes.add(indexBuilder.build());
            }
            builder.compositeIndexes(indexes);
        }
        
        return builder.build();
    }

    private List<OptimizationAnalysisResult.DataTypeOptimization> parseDataTypeOptimizations(final JsonNode node) {
        final List<OptimizationAnalysisResult.DataTypeOptimization> optimizations = new ArrayList<>();
        
        if (node != null && node.isArray()) {
            for (final JsonNode optNode : node) {
                optimizations.add(OptimizationAnalysisResult.DataTypeOptimization.builder()
                    .field(getStringValue(optNode, "field", ""))
                    .currentType(getStringValue(optNode, "currentType", ""))
                    .recommendedType(getStringValue(optNode, "recommendedType", ""))
                    .reason(getStringValue(optNode, "reason", ""))
                    .build());
            }
        }
        
        return optimizations;
    }

    private OptimizationAnalysisResult.PartitionStrategy parsePartitionStrategy(final JsonNode node) {
        if (node == null || node.isNull()) {
            return OptimizationAnalysisResult.PartitionStrategy.builder()
                .enabled(false)
                .build();
        }
        
        return OptimizationAnalysisResult.PartitionStrategy.builder()
            .enabled(node.get("enabled") != null && node.get("enabled").asBoolean())
            .type(getStringValue(node, "type", "range"))
            .column(getStringValue(node, "column", ""))
            .strategy(getStringValue(node, "strategy", ""))
            .build();
    }

    private List<OptimizationAnalysisResult.ConstraintRecommendation> parseConstraintRecommendations(
            final JsonNode node) {
        final List<OptimizationAnalysisResult.ConstraintRecommendation> constraints = new ArrayList<>();
        
        if (node != null && node.isArray()) {
            for (final JsonNode constraintNode : node) {
                constraints.add(OptimizationAnalysisResult.ConstraintRecommendation.builder()
                    .type(getStringValue(constraintNode, "type", ""))
                    .field(getStringValue(constraintNode, "field", ""))
                    .constraint(getStringValue(constraintNode, "constraint", ""))
                    .reason(getStringValue(constraintNode, "reason", ""))
                    .build());
            }
        }
        
        return constraints;
    }

    private int calculateValidationScore(final List<String> errors, final List<String> warnings) {
        int score = 100;
        score -= errors.size() * 20;  // 每个错误扣20分
        score -= warnings.size() * MAX_RECOMMENDATIONS; // 每个警告扣5分
        return Math.max(0, score);
    }

    private SQLGenerationResult createErrorResult(final String message, final long startTime) {
        return SQLGenerationResult.builder()
                .success(false)
                .message(message)
                .executionTimeMs(System.currentTimeMillis() - startTime)
                .generatedAt(LocalDateTime.now())
                .build();
    }
}