package com.assessment.dto;

import com.assessment.service.ScaleTestingService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 量表测试结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleTestResult {

    /**
     * 测试是否成功
     */
    private Boolean success;

    /**
     * 测试消息
     */
    private String message;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 执行时间（毫秒）
     */
    private Long executionTimeMs;

    /**
     * 通过的测试阶段数
     */
    private Integer testPhasesPassed;

    /**
     * 总测试阶段数
     */
    private Integer totalTestPhases;

    /**
     * 错误信息列表
     */
    private List<String> errors;

    /**
     * 警告信息列表
     */
    private List<String> warnings;

    /**
     * 测试时间
     */
    private LocalDateTime testTime;

    /**
     * 结构测试结果
     */
    private ScaleTestingService.StructureTestResult structureTestResult;

    /**
     * 数据填入测试结果
     */
    private ScaleTestingService.DataFillTestResult dataFillTestResult;

    /**
     * 性能测试结果
     */
    private ScaleTestingService.PerformanceTestResult performanceTestResult;

    /**
     * 测试详细信息
     */
    private Map<String, Object> testDetails;

    /**
     * 测试阶段详情
     */
    private List<TestPhaseDetail> phaseDetails;

    /**
     * 测试阶段详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TestPhaseDetail {
        private String phaseName;
        private Boolean passed;
        private Long executionTimeMs;
        private List<String> errors;
        private List<String> warnings;
        private Map<String, Object> details;
    }

    /**
     * 获取测试通过率
     */
    public Double getPassRate() {
        if (totalTestPhases == null || totalTestPhases == 0) {
            return 0.0;
        }
        return (double) testPhasesPassed / totalTestPhases * 100;
    }

    /**
     * 是否有警告
     */
    public Boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }

    /**
     * 是否有错误
     */
    public Boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }

    /**
     * 获取测试状态描述
     */
    public String getStatusDescription() {
        if (Boolean.TRUE.equals(success)) {
            if (hasWarnings()) {
                return "测试通过（有警告）";
            }
            return "测试通过";
        } else {
            return "测试失败";
        }
    }
}