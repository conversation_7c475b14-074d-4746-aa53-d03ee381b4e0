package com.assessment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 优化分析结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OptimizationAnalysisResult {

    /** 量表类型 */
    private String scaleType;

    /** 复杂度级别 */
    private String complexityLevel;

    /** 推荐的模板 */
    private String recommendedTemplate;

    /** 索引策略 */
    private IndexStrategy indexStrategy;

    /** 数据类型优化建议 */
    private List<DataTypeOptimization> dataTypeOptimizations;

    /** 分区策略 */
    private PartitionStrategy partitionStrategy;

    /** 约束建议 */
    private List<ConstraintRecommendation> constraintRecommendations;

    /** 性能得分 */
    private Integer performanceScore;

    /** 优化总结 */
    private String optimizationSummary;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexStrategy {
        /** 主要索引 */
        private List<String> primaryIndexes;
        
        /** 复合索引 */
        private List<CompositeIndex> compositeIndexes;
        
        /** 性能索引 */
        private List<String> performanceIndexes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompositeIndex {
        /** 索引名称 */
        private String name;
        
        /** 索引列 */
        private List<String> columns;
        
        /** 是否唯一 */
        private boolean unique;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataTypeOptimization {
        /** 字段名 */
        private String field;
        
        /** 当前类型 */
        private String currentType;
        
        /** 推荐类型 */
        private String recommendedType;
        
        /** 优化原因 */
        private String reason;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PartitionStrategy {
        /** 是否启用分区 */
        private boolean enabled;
        
        /** 分区类型 */
        private String type;
        
        /** 分区列 */
        private String column;
        
        /** 分区策略说明 */
        private String strategy;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConstraintRecommendation {
        /** 约束类型 */
        private String type;
        
        /** 字段名 */
        private String field;
        
        /** 约束表达式 */
        private String constraint;
        
        /** 约束原因 */
        private String reason;
    }
}