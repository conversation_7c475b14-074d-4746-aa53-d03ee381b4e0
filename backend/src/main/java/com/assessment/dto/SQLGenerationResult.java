package com.assessment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SQL生成结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SQLGenerationResult {

    /** 是否成功 */
    private boolean success;

    /** 消息 */
    private String message;

    /** 原始AI分析结果 */
    private DocumentAnalysisResult originalAnalysis;

    /** 优化后的SQL */
    private String optimizedSQL;

    /** 优化分析结果 */
    private OptimizationAnalysisResult optimization;

    /** SQL验证结果 */
    private SQLValidationResult validation;

    /** 建议列表 */
    private List<String> recommendations;

    /** 执行时间（毫秒） */
    private Long executionTimeMs;

    /** 生成时间 */
    private LocalDateTime generatedAt;

    /** 生成统计信息 */
    private GenerationStatistics statistics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GenerationStatistics {
        /** 原始字段数量 */
        private int originalFieldCount;
        
        /** 优化后字段数量 */
        private int optimizedFieldCount;
        
        /** 生成的索引数量 */
        private int indexCount;
        
        /** 约束数量 */
        private int constraintCount;
        
        /** 优化应用数量 */
        private int optimizationCount;
        
        /** 性能提升百分比 */
        private double performanceImprovement;
    }
}