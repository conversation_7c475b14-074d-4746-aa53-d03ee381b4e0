package com.assessment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SQL验证结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SQLValidationResult {

    /** 是否验证通过 */
    private boolean valid;

    /** 错误信息列表 */
    private List<String> errors;

    /** 警告信息列表 */
    private List<String> warnings;

    /** 验证得分 (0-100) */
    private Integer score;

    /** 验证详情 */
    private ValidationDetails details;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationDetails {
        /** 语法检查结果 */
        private boolean syntaxValid;
        
        /** 表结构检查结果 */
        private boolean structureValid;
        
        /** 性能检查结果 */
        private boolean performanceOptimal;
        
        /** 安全检查结果 */
        private boolean securityCompliant;
        
        /** 最佳实践检查结果 */
        private boolean bestPracticesFollowed;
        
        /** 检查项目详情 */
        private List<ValidationItem> items;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationItem {
        /** 检查项目名称 */
        private String name;
        
        /** 检查结果 */
        private boolean passed;
        
        /** 检查信息 */
        private String message;
        
        /** 严重级别 */
        private ValidationLevel level;
    }

    public enum ValidationLevel {
        INFO,
        WARNING,
        ERROR,
        CRITICAL
    }
}