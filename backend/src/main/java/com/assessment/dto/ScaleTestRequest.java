package com.assessment.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 量表测试请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleTestRequest {

    /**
     * 表名
     */
    @NotBlank(message = "表名不能为空")
    private String tableName;

    /**
     * 测试阶段
     * structure: 仅结构测试
     * data: 结构+数据填入测试
     * performance: 完整三阶段测试
     * full: 完整三阶段测试（同performance）
     */
    @NotNull(message = "测试阶段不能为空")
    private TestPhase testPhase;

    /**
     * 测试数据大小（用于数据填入测试）
     */
    @Min(value = 1, message = "测试数据大小至少为1")
    @Builder.Default
    private Integer testDataSize = 100;

    /**
     * 并发用户数（用于性能测试）
     */
    @Min(value = 1, message = "并发用户数至少为1")
    @Builder.Default
    private Integer concurrentUsers = 10;

    /**
     * 大数据量测试大小（用于性能测试）
     */
    @Min(value = 100, message = "大数据量测试大小至少为100")
    @Builder.Default
    private Integer largeDataSize = 1000;

    /**
     * 是否执行清理（测试后清理数据）
     */
    @Builder.Default
    private Boolean cleanupAfterTest = true;

    /**
     * 性能基准配置
     */
    private PerformanceBenchmark performanceBenchmark;

    /**
     * 测试选项
     */
    private TestOptions options;

    /**
     * 测试阶段枚举
     */
    public enum TestPhase {
        STRUCTURE("structure", "结构测试"),
        DATA("data", "数据填入测试"),
        PERFORMANCE("performance", "性能测试"),
        FULL("full", "完整测试");

        private final String code;
        private final String description;

        TestPhase(final String code, final String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 性能基准配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformanceBenchmark {
        /**
         * 插入性能基准（毫秒）
         */
        @Builder.Default
        private Long insertBenchmarkMs = 5000L;

        /**
         * 查询性能基准（毫秒）
         */
        @Builder.Default
        private Long queryBenchmarkMs = 1000L;

        /**
         * 大数据量操作基准（毫秒）
         */
        @Builder.Default
        private Long largeDataBenchmarkMs = 10000L;

        /**
         * 内存使用基准（百分比）
         */
        @Builder.Default
        private Integer memoryUsageBenchmark = 80;
    }

    /**
     * 测试选项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TestOptions {
        /**
         * 跳过结构验证
         */
        @Builder.Default
        private Boolean skipStructureValidation = false;

        /**
         * 跳过数据完整性检查
         */
        @Builder.Default
        private Boolean skipDataIntegrityCheck = false;

        /**
         * 跳过性能基准检查
         */
        @Builder.Default
        private Boolean skipPerformanceBenchmark = false;

        /**
         * 详细日志模式
         */
        @Builder.Default
        private Boolean verboseLogging = false;

        /**
         * 并行执行测试
         */
        @Builder.Default
        private Boolean parallelExecution = true;

        /**
         * 自定义测试超时（秒）
         */
        @Builder.Default
        private Integer testTimeoutSeconds = 300;
    }
}