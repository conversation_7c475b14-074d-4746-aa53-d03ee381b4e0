package com.assessment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * 量表迁移请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationRequest {

    /** 表名 */
    @NotBlank(message = "表名不能为空")
    private String tableName;

    /** 是否覆盖已存在的表 */
    private Boolean overwriteExisting;

    /** 是否迁移数据 */
    private Boolean migrateData;

    /** 是否清理临时环境数据 */
    private Boolean cleanupStaging;

    /** 迁移备注 */
    private String remarks;

    /** 执行人ID */
    private String operatorId;

    /** 迁移选项 */
    private MigrationOptions options;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MigrationOptions {
        /** 是否创建备份 */
        @Builder.Default
        private boolean createBackup = true;
        
        /** 批量大小 */
        @Builder.Default
        private int batchSize = 1000;
        
        /** 是否验证数据完整性 */
        @Builder.Default
        private boolean validateDataIntegrity = true;
        
        /** 超时时间（毫秒） */
        @Builder.Default
        private long timeoutMs = 600000; // 10分钟
    }
}