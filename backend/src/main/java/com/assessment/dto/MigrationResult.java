package com.assessment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 量表迁移结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationResult {

    /** 是否成功 */
    private boolean success;

    /** 消息 */
    private String message;

    /** 表名 */
    private String tableName;

    /** 执行时间（毫秒） */
    private Long executionTimeMs;

    /** 迁移的行数 */
    private Long migratedRows;

    /** 备份表名 */
    private String backupTableName;

    /** 警告信息 */
    private List<String> warnings;

    /** 错误信息 */
    private List<String> errors;

    /** 迁移时间 */
    private LocalDateTime migrationTime;

    /** 迁移详情 */
    private MigrationDetails details;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MigrationDetails {
        /** 源环境 */
        private String sourceEnvironment;
        
        /** 目标环境 */
        private String targetEnvironment;
        
        /** 迁移的表数量 */
        private int migratedTableCount;
        
        /** 数据验证结果 */
        private boolean dataValidationPassed;
        
        /** 结构验证结果 */
        private boolean structureValidationPassed;
        
        /** 性能测试结果 */
        private PerformanceMetrics performanceMetrics;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformanceMetrics {
        /** 插入性能（毫秒） */
        private Long insertPerformanceMs;
        
        /** 查询性能（毫秒） */
        private Long queryPerformanceMs;
        
        /** 内存使用量（MB） */
        private Double memoryUsageMb;
        
        /** 磁盘使用量（MB） */
        private Double diskUsageMb;
    }
}