package com.assessment.controller;

import com.assessment.dto.DocumentAnalysisRequest;
import com.assessment.dto.SQLGenerationResult;
import com.assessment.service.EnhancedAISQLGenerationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 增强的AI SQL生成控制器
 * 提供智能量表分析和优化SQL生成功能
 */
@RestController
@RequestMapping("/api/ai-sql-generation")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "智能SQL生成", description = "AI驱动的量表SQL智能生成API")
public class EnhancedAISQLController {

    private final EnhancedAISQLGenerationService sqlGenerationService;

    @Operation(summary = "智能生成优化SQL", description = "基于量表文档内容，使用AI智能分析并生成优化的数据库SQL")
    @PostMapping("/generate-optimized-sql")
    @PreAuthorize("hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<SQLGenerationResult> generateOptimizedSQL(
            @Valid @RequestBody DocumentAnalysisRequest request) {
        
        log.info("收到智能SQL生成请求: 文件={}, 内容长度={}", 
            request.getFileName(), 
            request.getMarkdownContent() != null ? request.getMarkdownContent().length() : 0);
        
        try {
            final SQLGenerationResult result = sqlGenerationService.generateOptimizedSQL(request);
            
            if (result.isSuccess()) {
                log.info("智能SQL生成成功: 表名={}, 耗时={}ms", 
                    result.getOriginalAnalysis() != null ? result.getOriginalAnalysis().getTableName() : "未知",
                    result.getExecutionTimeMs());
                return ResponseEntity.ok(result);
            } else {
                log.warn("智能SQL生成失败: {}", result.getMessage());
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("智能SQL生成异常: {}", request.getFileName(), e);
            
            final SQLGenerationResult errorResult = SQLGenerationResult.builder()
                    .success(false)
                    .message("SQL生成异常: " + e.getMessage())
                    .build();
                    
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "快速SQL生成", description = "快速生成基础SQL，无复杂优化分析")
    @PostMapping("/generate-quick-sql")
    @PreAuthorize("hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> generateQuickSQL(
            @Valid @RequestBody DocumentAnalysisRequest request) {
        
        log.info("收到快速SQL生成请求: 文件={}", request.getFileName());
        
        try {
            // 使用简化版本，只做基础分析
            final SQLGenerationResult result = sqlGenerationService.generateOptimizedSQL(request);
            
            if (result.isSuccess() && result.getOptimizedSQL() != null) {
                final Map<String, Object> quickResult = Map.of(
                    "success", true,
                    "tableName", result.getOriginalAnalysis() != null 
                        ? result.getOriginalAnalysis().getTableName() 
                        : "unknown_table",
                    "sql", result.getOptimizedSQL(),
                    "fieldCount", result.getOriginalAnalysis() != null 
                        ? result.getOriginalAnalysis().getFields().size() 
                        : 0,
                    "executionTimeMs", result.getExecutionTimeMs(),
                    "message", "快速SQL生成成功"
                );
                
                return ResponseEntity.ok(quickResult);
            } else {
                final Map<String, Object> errorResult = Map.of(
                    "success", false,
                    "message", result.getMessage(),
                    "error", "快速SQL生成失败"
                );
                
                return ResponseEntity.badRequest().body(errorResult);
            }
            
        } catch (Exception e) {
            log.error("快速SQL生成异常: {}", request.getFileName(), e);
            
            final Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", e.getMessage(),
                "error", "快速SQL生成异常"
            );
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "SQL优化建议", description = "为已有SQL提供优化建议")
    @PostMapping("/optimize-existing-sql")
    @PreAuthorize("hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> optimizeExistingSQL(
            @Parameter(description = "现有SQL语句") @RequestParam String existingSQL,
            @Parameter(description = "表名") @RequestParam String tableName) {
        
        log.info("收到SQL优化请求: 表名={}", tableName);
        
        try {
            // 这里可以实现SQL优化逻辑
            // 目前返回一个基础的响应
            final Map<String, Object> optimizationResult = Map.of(
                "success", true,
                "tableName", tableName,
                "originalSQL", existingSQL,
                "suggestions", java.util.List.of(
                    "建议添加适当的索引以提高查询性能",
                    "考虑添加created_at和updated_at审计字段",
                    "建议使用UUID作为主键类型",
                    "考虑添加数据约束以确保数据完整性"
                ),
                "message", "SQL优化建议生成成功"
            );
            
            return ResponseEntity.ok(optimizationResult);
            
        } catch (Exception e) {
            log.error("SQL优化建议生成异常: 表名={}", tableName, e);
            
            final Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", e.getMessage(),
                "error", "SQL优化建议生成异常"
            );
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "获取SQL模板", description = "获取可用的SQL模板列表")
    @GetMapping("/sql-templates")
    @PreAuthorize("hasRole('SCALE_VIEWER') or hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getSQLTemplates() {
        
        log.info("获取SQL模板列表");
        
        try {
            final Map<String, Object> templates = Map.of(
                "success", true,
                "templates", Map.of(
                    "assessment_scale", Map.of(
                        "name", "标准评估量表",
                        "description", "适用于一般的评估量表，包含基础字段和索引",
                        "complexity", "simple",
                        "recommendedFor", java.util.List.of("能力评估", "健康评估", "满意度调查")
                    ),
                    "multi_dimension_scale", Map.of(
                        "name", "多维度评估量表", 
                        "description", "适用于多维度、多层级的复杂评估",
                        "complexity", "medium",
                        "recommendedFor", java.util.List.of("综合能力评估", "多维度量表", "复杂评分系统")
                    ),
                    "custom_scale", Map.of(
                        "name", "自定义量表",
                        "description", "完全根据AI分析结果生成的自定义表结构",
                        "complexity", "variable",
                        "recommendedFor", java.util.List.of("特殊需求", "新型量表", "实验性评估")
                    )
                ),
                "message", "SQL模板获取成功"
            );
            
            return ResponseEntity.ok(templates);
            
        } catch (Exception e) {
            log.error("获取SQL模板异常", e);
            
            final Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", e.getMessage(),
                "error", "获取SQL模板异常"
            );
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "验证SQL语法", description = "验证生成的SQL语法是否正确")
    @PostMapping("/validate-sql")
    @PreAuthorize("hasRole('SCALE_VIEWER') or hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> validateSQL(
            @Parameter(description = "SQL语句") @RequestParam String sql) {
        
        log.info("收到SQL验证请求");
        
        try {
            final SQLValidationResult validation = performSQLValidation(sql);
            final Map<String, Object> validationResult = buildValidationResponse(validation);
            
            return ResponseEntity.ok(validationResult);
            
        } catch (Exception e) {
            log.error("SQL验证异常", e);
            
            final Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", e.getMessage(),
                "error", "SQL验证异常"
            );
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    private SQLValidationResult performSQLValidation(final String sql) {
        final String upperSQL = sql.toUpperCase();
        final boolean hasCreateTable = upperSQL.contains("CREATE TABLE");
        final boolean hasPrimaryKey = upperSQL.contains("PRIMARY KEY");
        final boolean hasValidSyntax = sql.trim().endsWith(";") || sql.trim().endsWith(")");
        
        final java.util.List<String> warnings = new java.util.ArrayList<>();
        final java.util.List<String> errors = new java.util.ArrayList<>();
        
        if (!hasCreateTable) {
            errors.add("缺少CREATE TABLE语句");
        }
        
        if (!hasPrimaryKey) {
            warnings.add("建议添加主键");
        }
        
        if (!hasValidSyntax) {
            warnings.add("SQL语法可能不完整");
        }
        
        return new SQLValidationResult(hasCreateTable, hasPrimaryKey, hasValidSyntax, errors, warnings);
    }

    private Map<String, Object> buildValidationResponse(final SQLValidationResult validation) {
        final boolean isValid = validation.errors().isEmpty();
        final int score = Math.max(0, 100 - validation.errors().size() * 20 - validation.warnings().size() * 5);
        
        return Map.of(
            "success", true,
            "valid", isValid,
            "score", score,
            "errors", validation.errors(),
            "warnings", validation.warnings(),
            "checks", Map.of(
                "hasCreateTable", validation.hasCreateTable(),
                "hasPrimaryKey", validation.hasPrimaryKey(),
                "hasValidSyntax", validation.hasValidSyntax()
            ),
            "message", isValid ? "SQL验证通过" : "SQL验证发现问题"
        );
    }

    private record SQLValidationResult(
        boolean hasCreateTable,
        boolean hasPrimaryKey,
        boolean hasValidSyntax,
        List<String> errors,
        List<String> warnings
    ) { }
}