package com.assessment.controller;

import com.assessment.dto.ExecuteDDLRequest;
import com.assessment.dto.ExecuteDDLResult;
import com.assessment.dto.MigrationRequest;
import com.assessment.dto.MigrationResult;
import com.assessment.service.EnhancedDatabaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 量表管理控制器
 * 处理双数据库环境的量表操作
 */
@RestController
@RequestMapping("/api/scale-management")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "量表管理", description = "智能量表管理API")
public class ScaleManagementController {

    private final EnhancedDatabaseService enhancedDatabaseService;

    @Operation(summary = "在临时环境创建量表", description = "在临时数据库中执行DDL语句创建量表，用于测试验证")
    @PostMapping("/staging/create-table")
    @PreAuthorize("hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<ExecuteDDLResult> createTableInStaging(
            @Valid @RequestBody ExecuteDDLRequest request) {
        
        log.info("收到临时环境建表请求: {}", request.getTableName());
        
        try {
            final ExecuteDDLResult result = enhancedDatabaseService.executeDDLInStaging(request);
            
            if (Boolean.TRUE.equals(result.getSuccess())) {
                log.info("临时环境建表成功: {}", request.getTableName());
                return ResponseEntity.ok(result);
            } else {
                log.warn("临时环境建表失败: {}, 错误: {}", request.getTableName(), result.getErrors());
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("临时环境建表异常: {}", request.getTableName(), e);
            
            final ExecuteDDLResult errorResult = ExecuteDDLResult.builder()
                    .success(false)
                    .message("建表异常: " + e.getMessage())
                    .tableName(request.getTableName())
                    .build();
                    
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "在正式环境创建量表", description = "在正式数据库中执行DDL语句创建量表")
    @PostMapping("/production/create-table")
    @PreAuthorize("hasRole('SCALE_ADMIN') or hasRole('ADMIN')")
    public ResponseEntity<ExecuteDDLResult> createTableInProduction(
            @Valid @RequestBody ExecuteDDLRequest request) {
        
        log.info("收到正式环境建表请求: {}", request.getTableName());
        
        try {
            final ExecuteDDLResult result = enhancedDatabaseService.executeDDLInProduction(request);
            
            if (Boolean.TRUE.equals(result.getSuccess())) {
                log.info("正式环境建表成功: {}", request.getTableName());
                return ResponseEntity.ok(result);
            } else {
                log.warn("正式环境建表失败: {}, 错误: {}", request.getTableName(), result.getErrors());
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("正式环境建表异常: {}", request.getTableName(), e);
            
            final ExecuteDDLResult errorResult = ExecuteDDLResult.builder()
                    .success(false)
                    .message("建表异常: " + e.getMessage())
                    .tableName(request.getTableName())
                    .build();
                    
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "迁移量表到正式环境", description = "将经过测试验证的量表从临时环境迁移到正式环境")
    @PostMapping("/migrate-to-production")
    @PreAuthorize("hasRole('SCALE_ADMIN') or hasRole('ADMIN')")
    public ResponseEntity<MigrationResult> migrateToProduction(
            @Valid @RequestBody MigrationRequest request) {
        
        log.info("收到量表迁移请求: {} -> 正式环境", request.getTableName());
        
        try {
            final MigrationResult result = enhancedDatabaseService.migrateScaleToProduction(request);
            
            if (result.isSuccess()) {
                log.info("量表迁移成功: {}", request.getTableName());
                return ResponseEntity.ok(result);
            } else {
                log.warn("量表迁移失败: {}, 错误: {}", request.getTableName(), result.getErrors());
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("量表迁移异常: {}", request.getTableName(), e);
            
            final MigrationResult errorResult = MigrationResult.builder()
                    .success(false)
                    .message("迁移异常: " + e.getMessage())
                    .tableName(request.getTableName())
                    .build();
                    
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "检查表是否存在", description = "检查指定表在临时或正式环境中是否存在")
    @GetMapping("/table-exists/{tableName}")
    @PreAuthorize("hasRole('SCALE_VIEWER') or hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Boolean>> checkTableExists(
            @Parameter(description = "表名") @PathVariable String tableName,
            @Parameter(description = "环境类型：staging 或 production") 
            @RequestParam(defaultValue = "both") String environment) {
        
        log.info("检查表是否存在: {} 在环境: {}", tableName, environment);
        
        try {
            final boolean stagingExists = "production".equals(environment) 
                ? Boolean.FALSE 
                : enhancedDatabaseService.tableExistsInStaging(tableName);
            final boolean productionExists = "staging".equals(environment) 
                ? Boolean.FALSE 
                : enhancedDatabaseService.tableExistsInProduction(tableName);
            
            final Map<String, Boolean> result = java.util.Map.of(
                "stagingExists", stagingExists,
                "productionExists", productionExists
            );
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("检查表存在性异常: {}", tableName, e);
            final Map<String, Boolean> errorResult = java.util.Map.of();
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "获取表结构", description = "获取指定表的结构信息")
    @GetMapping("/table-structure/{tableName}")
    @PreAuthorize("hasRole('SCALE_VIEWER') or hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getTableStructure(
            @Parameter(description = "表名") @PathVariable String tableName,
            @Parameter(description = "环境类型：staging 或 production") @RequestParam String environment) {
        
        log.info("获取表结构: {} 从环境: {}", tableName, environment);
        
        try {
            final Map<String, Object> structure = "staging".equals(environment) 
                ? enhancedDatabaseService.getTableStructureFromStaging(tableName)
                : enhancedDatabaseService.getTableStructureFromProduction(tableName);
            
            return ResponseEntity.ok(structure);
            
        } catch (Exception e) {
            log.error("获取表结构异常: {} 从环境: {}", tableName, environment, e);
            return ResponseEntity.internalServerError().body(Map.of(
                "error", true,
                "message", e.getMessage()
            ));
        }
    }

    @Operation(summary = "清理过期的临时数据", description = "清理临时数据库中超过指定天数的过期数据")
    @PostMapping("/staging/cleanup")
    @PreAuthorize("hasRole('SCALE_ADMIN') or hasRole('ADMIN')")
    public ResponseEntity<EnhancedDatabaseService.CleanupResult> cleanupExpiredStagingData(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "7") int retentionDays) {
        
        log.info("开始清理过期临时数据，保留天数: {}", retentionDays);
        
        try {
            final EnhancedDatabaseService.CleanupResult result = 
                enhancedDatabaseService.cleanupExpiredStagingData(retentionDays);
            
            if (result.isSuccess()) {
                log.info("清理操作完成，清理了 {} 个表", result.getCleanedTableCount());
                return ResponseEntity.ok(result);
            } else {
                log.warn("清理操作失败，错误: {}", result.getErrors());
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("清理操作异常", e);
            
            final EnhancedDatabaseService.CleanupResult errorResult = 
                EnhancedDatabaseService.CleanupResult.builder()
                    .success(false)
                    .cleanedTableCount(0)
                    .errors(java.util.List.of("清理异常: " + e.getMessage()))
                    .build();
                    
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @Operation(summary = "获取环境状态", description = "获取双数据库环境的状态信息")
    @GetMapping("/environment-status")
    @PreAuthorize("hasRole('SCALE_VIEWER') or hasRole('SCALE_EDITOR') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getEnvironmentStatus() {
        
        log.info("获取环境状态");
        
        try {
            // 这里可以添加数据库连接状态检查、表数量统计等
            final Map<String, Object> status = Map.of(
                "stagingAvailable", true, // 实际实现中应该检查连接状态
                "productionAvailable", true,
                "timestamp", java.time.LocalDateTime.now(),
                "message", "环境状态正常"
            );
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            log.error("获取环境状态异常", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "error", true,
                "message", e.getMessage()
            ));
        }
    }
}