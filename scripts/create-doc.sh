#!/bin/bash

# 智慧养老评估平台 - 文档自动生成工具
# 根据开发需求自动创建标准格式的文档

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo "=== 智慧养老评估平台文档生成工具 ==="

# 检查参数
if [ $# -lt 2 ]; then
    echo -e "${RED}❌ 用法: $0 <文档类型> <文档名称> [可选参数]${NC}"
    echo ""
    echo -e "${BLUE}📋 支持的文档类型:${NC}"
    echo "  feature       - 功能文档 (在docs/features/目录)"
    echo "  architecture  - 架构文档 (在docs/architecture/目录)"
    echo "  quality       - 质量文档 (在docs/quality/目录)"
    echo "  backend       - 后端文档 (在backend/目录)"
    echo "  frontend      - 前端文档 (在frontend/目录)"
    echo "  operation     - 运维文档 (在docs/operations/目录)"
    echo ""
    echo -e "${YELLOW}💡 示例:${NC}"
    echo "  $0 feature user-management"
    echo "  $0 architecture database-v2"
    echo "  $0 quality checkstyle-update"
    echo "  $0 backend auth-service"
    echo "  $0 frontend mobile-app"
    exit 1
fi

DOC_TYPE=$1
DOC_NAME=$2
AUTHOR=${3:-"开发团队"}
VERSION=${4:-"v1.0"}

# 当前日期
CURRENT_DATE=$(date '+%Y年%m月%d日')
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# 确保在项目根目录
if [ ! -d "docs" ] || [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 创建功能文档
create_feature_doc() {
    local feature_name=$1
    local feature_dir="docs/features/${feature_name}"
    
    echo -e "\n${BLUE}📂 创建功能文档: ${feature_name}${NC}"
    
    # 创建目录
    mkdir -p "$feature_dir"
    
    # 功能概览文档
    cat > "${feature_dir}/index.md" << EOF
# ${feature_name} 功能模块

## 📋 功能概述

${feature_name}是智慧养老评估平台的核心功能模块，提供[功能描述]。

## 🎯 功能特性

### 核心功能
- [ ] 功能点1：[描述]
- [ ] 功能点2：[描述]
- [ ] 功能点3：[描述]

### 技术特性
- [ ] 技术特性1：[描述]
- [ ] 技术特性2：[描述]
- [ ] 技术特性3：[描述]

## 🏗️ 架构设计

### 系统架构
\`\`\`
[架构图描述]
前端应用 → API网关 → 业务服务 → 数据存储
\`\`\`

### 技术栈
- **前端**: Vue 3 + TypeScript + Pinia
- **后端**: Spring Boot 3.5.3 + JPA
- **数据库**: PostgreSQL + Redis
- **其他**: [相关技术]

## 📚 相关文档

- [实现细节](./implementation.md) - 详细技术实现
- [API文档](./api.md) - 接口说明和示例
- [测试指南](./testing.md) - 测试方法和标准
- [故障排查](./troubleshooting.md) - 常见问题解决

## 🚀 快速开始

### 环境准备
\`\`\`bash
# 确保基础服务运行
docker-compose up -d

# 启动后端服务
./mvnw spring-boot:run

# 启动前端服务
cd frontend && npm run dev
\`\`\`

### 功能验证
\`\`\`bash
# 验证功能是否正常
curl -X GET "http://localhost:8080/api/${feature_name}/health"
\`\`\`

---

**文档版本**: ${VERSION}  
**创建日期**: ${CURRENT_DATE}  
**维护团队**: ${AUTHOR}  

*本文档随功能开发同步更新*
EOF

    # 实现细节文档
    cat > "${feature_dir}/implementation.md" << EOF
# ${feature_name} 实现细节

## 🛠️ 技术实现

### 后端实现

#### 核心类结构
\`\`\`java
// Controller层
@RestController
@RequestMapping("/api/${feature_name}")
public class ${feature_name^}Controller {
    // 控制器实现
}

// Service层
@Service
public class ${feature_name^}Service {
    // 业务逻辑实现
}

// Repository层
@Repository
public interface ${feature_name^}Repository extends JpaRepository<${feature_name^}, Long> {
    // 数据访问实现
}
\`\`\`

#### 数据模型
\`\`\`java
@Entity
@Table(name = "${feature_name}")
public class ${feature_name^} {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    // 其他字段
}
\`\`\`

### 前端实现

#### 组件结构
\`\`\`typescript
// 主组件
interface ${feature_name^}Props {
  // 属性定义
}

// 状态管理
interface ${feature_name^}State {
  // 状态定义
}
\`\`\`

#### API调用
\`\`\`typescript
// API服务
export const ${feature_name}Api = {
  getList: () => request.get('/api/${feature_name}'),
  create: (data: any) => request.post('/api/${feature_name}', data),
  update: (id: number, data: any) => request.put(\`/api/${feature_name}/\${id}\`, data),
  delete: (id: number) => request.delete(\`/api/${feature_name}/\${id}\`)
}
\`\`\`

## 🔧 关键技术点

### 多租户支持
- 自动租户上下文过滤
- 数据隔离机制
- 权限验证

### 性能优化
- Redis缓存策略
- 数据库查询优化
- 前端懒加载

### 安全措施
- JWT认证验证
- 输入参数校验
- SQL注入防护

## 📝 开发注意事项

### 编码规范
- 遵循Checkstyle规范
- 使用final参数声明
- 避免魔术数字

### 测试要求
- 单元测试覆盖率 ≥ 80%
- 集成测试验证
- API接口测试

---

**文档版本**: ${VERSION}  
**最后更新**: ${CURRENT_DATE}  
**更新内容**: 初始版本创建  
EOF

    # API文档
    cat > "${feature_dir}/api.md" << EOF
# ${feature_name} API文档

## 📋 API概览

${feature_name}模块提供RESTful API接口，支持CRUD操作和业务功能。

## 🌐 基础信息

- **Base URL**: \`http://localhost:8080/api/${feature_name}\`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📡 API接口

### 获取列表
\`\`\`http
GET /api/${feature_name}
Authorization: Bearer {token}
Content-Type: application/json
\`\`\`

**查询参数**:
- \`page\` (int): 页码，默认0
- \`size\` (int): 每页数量，默认20
- \`sort\` (string): 排序字段

**响应示例**:
\`\`\`json
{
  "code": 200,
  "message": "成功",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "示例名称",
        "status": "active",
        "createdAt": "2025-07-01T10:00:00Z"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "number": 0,
    "size": 20
  }
}
\`\`\`

### 创建记录
\`\`\`http
POST /api/${feature_name}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "新记录名称",
  "description": "描述信息"
}
\`\`\`

### 更新记录
\`\`\`http
PUT /api/${feature_name}/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "更新后名称",
  "description": "更新后描述"
}
\`\`\`

### 删除记录
\`\`\`http
DELETE /api/${feature_name}/{id}
Authorization: Bearer {token}
\`\`\`

## 🔧 错误处理

### 常见错误码
- \`400\`: 请求参数错误
- \`401\`: 未授权访问
- \`403\`: 权限不足
- \`404\`: 资源不存在
- \`500\`: 服务器内部错误

### 错误响应格式
\`\`\`json
{
  "code": 400,
  "message": "参数校验失败",
  "errors": [
    {
      "field": "name",
      "message": "名称不能为空"
    }
  ],
  "timestamp": "2025-07-01T10:00:00Z"
}
\`\`\`

## 📝 使用示例

### JavaScript/TypeScript
\`\`\`typescript
// 获取列表
const response = await fetch('/api/${feature_name}', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});
const data = await response.json();

// 创建记录
const createResponse = await fetch('/api/${feature_name}', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: '新记录',
    description: '描述'
  })
});
\`\`\`

### curl
\`\`\`bash
# 获取列表
curl -X GET "http://localhost:8080/api/${feature_name}" \\
  -H "Authorization: Bearer \${TOKEN}" \\
  -H "Content-Type: application/json"

# 创建记录
curl -X POST "http://localhost:8080/api/${feature_name}" \\
  -H "Authorization: Bearer \${TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{"name":"新记录","description":"描述"}'
\`\`\`

---

**文档版本**: ${VERSION}  
**最后更新**: ${CURRENT_DATE}  
**API版本**: v1.0  
EOF

    # 测试指南
    cat > "${feature_dir}/testing.md" << EOF
# ${feature_name} 测试指南

## 🧪 测试策略

### 测试层次
- **单元测试**: Service层和Repository层
- **集成测试**: Controller层和数据库交互
- **端到端测试**: 完整业务流程
- **性能测试**: 并发和负载测试

## 🔧 测试环境配置

### 测试数据库
\`\`\`yaml
# application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
\`\`\`

### 测试依赖
\`\`\`xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>postgresql</artifactId>
    <scope>test</scope>
</dependency>
\`\`\`

## 🧪 单元测试

### Service层测试
\`\`\`java
@ExtendWith(MockitoExtension.class)
class ${feature_name^}ServiceTest {
    
    @Mock
    private ${feature_name^}Repository repository;
    
    @InjectMocks
    private ${feature_name^}Service service;
    
    @Test
    @DisplayName("创建${feature_name} - 成功场景")
    void create${feature_name^}_Success() {
        // Given
        final Create${feature_name^}Request request = new Create${feature_name^}Request();
        request.setName("测试名称");
        
        final ${feature_name^} entity = new ${feature_name^}();
        entity.setId(1L);
        entity.setName("测试名称");
        
        when(repository.save(any(${feature_name^}.class))).thenReturn(entity);
        
        // When
        final ${feature_name^}Response result = service.create(request);
        
        // Then
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getName()).isEqualTo("测试名称");
        verify(repository).save(any(${feature_name^}.class));
    }
}
\`\`\`

### Repository层测试
\`\`\`java
@DataJpaTest
class ${feature_name^}RepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private ${feature_name^}Repository repository;
    
    @Test
    @DisplayName("按名称查找${feature_name}")
    void findByName() {
        // Given
        final ${feature_name^} entity = new ${feature_name^}();
        entity.setName("测试名称");
        entityManager.persistAndFlush(entity);
        
        // When
        final Optional<${feature_name^}> result = repository.findByName("测试名称");
        
        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getName()).isEqualTo("测试名称");
    }
}
\`\`\`

## 🌐 集成测试

### Controller层测试
\`\`\`java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
class ${feature_name^}ControllerIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private ${feature_name^}Repository repository;
    
    @Test
    @DisplayName("创建${feature_name} - 集成测试")
    void create${feature_name^}_Integration() {
        // Given
        final Create${feature_name^}Request request = new Create${feature_name^}Request();
        request.setName("集成测试");
        
        // When
        final ResponseEntity<${feature_name^}Response> response = restTemplate.postForEntity(
            "/api/${feature_name}", request, ${feature_name^}Response.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().getName()).isEqualTo("集成测试");
        
        // 验证数据库
        final Optional<${feature_name^}> saved = repository.findById(response.getBody().getId());
        assertThat(saved).isPresent();
    }
}
\`\`\`

## 🚀 前端测试

### 组件测试
\`\`\`typescript
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ${feature_name^}Component from './${feature_name^}Component.vue'

describe('${feature_name^}Component', () => {
  it('渲染基本内容', () => {
    const wrapper = mount(${feature_name^}Component, {
      props: {
        title: '测试标题'
      }
    })
    
    expect(wrapper.text()).toContain('测试标题')
  })
  
  it('处理用户交互', async () => {
    const wrapper = mount(${feature_name^}Component)
    
    await wrapper.find('button').trigger('click')
    
    expect(wrapper.emitted()).toHaveProperty('submit')
  })
})
\`\`\`

### API测试
\`\`\`typescript
import { describe, it, expect, vi } from 'vitest'
import { ${feature_name}Api } from '../api/${feature_name}Api'

describe('${feature_name}Api', () => {
  it('获取列表数据', async () => {
    // Mock API响应
    const mockData = {
      content: [{ id: 1, name: '测试' }],
      totalElements: 1
    }
    
    vi.spyOn(${feature_name}Api, 'getList').mockResolvedValue(mockData)
    
    const result = await ${feature_name}Api.getList()
    
    expect(result.content).toHaveLength(1)
    expect(result.content[0].name).toBe('测试')
  })
})
\`\`\`

## 📊 测试执行

### 运行测试
\`\`\`bash
# 后端测试
./mvnw test
./mvnw test -Dtest=${feature_name^}ServiceTest

# 前端测试
cd frontend
npm run test
npm run test:coverage

# 端到端测试
npm run test:e2e
\`\`\`

### 测试报告
\`\`\`bash
# 生成测试覆盖率报告
./mvnw jacoco:report

# 查看报告
open target/site/jacoco/index.html
\`\`\`

## ✅ 验收标准

### 功能验收
- [ ] 所有业务功能正常工作
- [ ] 错误处理正确
- [ ] 性能满足要求
- [ ] 安全验证有效

### 质量标准
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 集成测试覆盖主要流程
- [ ] 所有测试用例通过
- [ ] 代码质量检查通过

---

**文档版本**: ${VERSION}  
**最后更新**: ${CURRENT_DATE}  
**测试负责人**: QA团队  
EOF

    # 故障排查文档
    cat > "${feature_dir}/troubleshooting.md" << EOF
# ${feature_name} 故障排查指南

## 🚨 常见问题

### 问题1: 功能无法访问
**症状**: 访问${feature_name}功能时返回404错误

**可能原因**:
- 服务未启动
- 路由配置错误
- 权限不足

**解决方案**:
\`\`\`bash
# 1. 检查服务状态
curl http://localhost:8080/actuator/health

# 2. 检查日志
tail -f logs/application.log | grep ${feature_name}

# 3. 验证权限
# 确保JWT token包含必要的权限
\`\`\`

### 问题2: 数据保存失败
**症状**: 创建或更新数据时返回500错误

**可能原因**:
- 数据库连接问题
- 数据验证失败
- 事务回滚

**解决方案**:
\`\`\`bash
# 1. 检查数据库连接
docker-compose ps postgres

# 2. 查看详细错误日志
grep -A 10 -B 10 "ERROR" logs/application.log

# 3. 验证数据格式
# 确保请求数据符合验证规则
\`\`\`

### 问题3: 性能问题
**症状**: 响应时间过长或超时

**可能原因**:
- 数据库查询慢
- 缓存失效
- 并发问题

**解决方案**:
\`\`\`bash
# 1. 检查数据库性能
# 查看慢查询日志

# 2. 检查Redis缓存
redis-cli ping
redis-cli info memory

# 3. 查看系统资源
top
iostat
\`\`\`

## 🔧 调试工具

### 日志分析
\`\`\`bash
# 实时查看应用日志
tail -f logs/application.log

# 搜索特定错误
grep -i "error\|exception" logs/application.log

# 查看${feature_name}相关日志
grep "${feature_name}" logs/application.log | tail -20
\`\`\`

### 数据库调试
\`\`\`sql
-- 检查${feature_name}表数据
SELECT * FROM ${feature_name} ORDER BY created_at DESC LIMIT 10;

-- 检查索引使用情况
EXPLAIN ANALYZE SELECT * FROM ${feature_name} WHERE name = '测试';

-- 检查表状态
SELECT 
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del
FROM pg_stat_user_tables 
WHERE tablename = '${feature_name}';
\`\`\`

### API调试
\`\`\`bash
# 测试API连通性
curl -v http://localhost:8080/api/${feature_name}/health

# 测试认证
curl -X GET "http://localhost:8080/api/${feature_name}" \\
  -H "Authorization: Bearer \${TOKEN}" \\
  -v

# 测试创建功能
curl -X POST "http://localhost:8080/api/${feature_name}" \\
  -H "Authorization: Bearer \${TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{"name":"调试测试"}' \\
  -v
\`\`\`

## 📊 监控指标

### 关键指标
- **响应时间**: 平均响应时间 < 500ms
- **错误率**: 错误率 < 1%
- **并发数**: 支持并发请求数
- **内存使用**: JVM内存使用率 < 80%

### 监控命令
\`\`\`bash
# 查看JVM状态
jstat -gc [PID]

# 查看线程状态
jstack [PID]

# 查看内存使用
jmap -histo [PID]
\`\`\`

## 🆘 紧急处理

### 服务无响应
\`\`\`bash
# 1. 重启应用
./dev-stop.sh && ./dev-start-m4.sh

# 2. 如果问题持续，重启数据库
docker-compose restart postgres redis

# 3. 清理缓存
redis-cli FLUSHALL
\`\`\`

### 数据问题
\`\`\`bash
# 1. 备份当前数据
pg_dump assessment_db > backup_\$(date +%Y%m%d_%H%M%S).sql

# 2. 回滚到上一个版本
./mvnw flyway:undo

# 3. 重新应用迁移
./mvnw flyway:migrate
\`\`\`

## 📞 联系支持

### 内部支持
- **开发团队**: <EMAIL>
- **运维团队**: <EMAIL>
- **值班电话**: [紧急联系电话]

### 报告问题
提供以下信息：
1. 问题描述和重现步骤
2. 错误日志截图
3. 系统环境信息
4. 相关配置文件

---

**文档版本**: ${VERSION}  
**最后更新**: ${CURRENT_DATE}  
**维护团队**: 运维团队 + ${AUTHOR}  
EOF

    echo -e "${GREEN}✅ 功能文档创建完成: ${feature_dir}${NC}"
    echo -e "${BLUE}📁 创建的文件:${NC}"
    echo -e "   - index.md (功能概览)"
    echo -e "   - implementation.md (实现细节)"
    echo -e "   - api.md (API文档)"
    echo -e "   - testing.md (测试指南)"
    echo -e "   - troubleshooting.md (故障排查)"
}

# 创建架构文档
create_architecture_doc() {
    local arch_name=$1
    local arch_dir="docs/architecture/${arch_name}"
    
    echo -e "\n${BLUE}🏗️ 创建架构文档: ${arch_name}${NC}"
    
    mkdir -p "$arch_dir"
    
    cat > "${arch_dir}/design.md" << EOF
# ${arch_name} 架构设计

## 📋 设计概述

${arch_name}架构设计旨在[设计目标和原则]。

## 🎯 设计目标

- **可扩展性**: 支持水平和垂直扩展
- **高可用性**: 99.9%以上可用性
- **高性能**: 满足业务性能要求
- **安全性**: 保障数据和系统安全

## 🏗️ 架构图

### 整体架构
\`\`\`
┌─────────────────────────────────────┐
│            前端层                    │
├─────────────────────────────────────┤
│            应用层                    │
├─────────────────────────────────────┤
│           业务逻辑层                  │
├─────────────────────────────────────┤
│           数据访问层                  │
├─────────────────────────────────────┤
│           基础设施层                  │
└─────────────────────────────────────┘
\`\`\`

### 技术架构
- **展示层**: Vue 3 + TypeScript
- **应用层**: Spring Boot 3.5.3
- **业务层**: Domain Services
- **数据层**: PostgreSQL + Redis
- **基础设施**: Docker + Kubernetes

## 🔧 技术选型

### 技术栈
| 层次 | 技术选择 | 版本 | 选择理由 |
|------|----------|------|----------|
| 前端 | Vue 3 | 3.4+ | 响应式设计，组件化 |
| 后端 | Spring Boot | 3.5.3 | 成熟稳定，生态丰富 |
| 数据库 | PostgreSQL | 15 | 高性能，支持JSON |
| 缓存 | Redis | 7 | 高性能内存缓存 |
| 容器 | Docker | Latest | 容器化部署 |

### 关键组件
- **API网关**: Spring Cloud Gateway
- **服务发现**: Consul/Eureka
- **配置中心**: Spring Cloud Config
- **消息队列**: RabbitMQ/Kafka
- **监控告警**: Prometheus + Grafana

## 📊 设计原则

### SOLID原则
- **单一职责**: 每个组件有明确的职责
- **开闭原则**: 对扩展开放，对修改关闭
- **里氏替换**: 子类可以替换父类
- **接口隔离**: 接口职责单一
- **依赖倒置**: 依赖抽象而非具体

### DDD设计
- **领域驱动**: 基于业务领域建模
- **聚合根**: 明确聚合边界
- **值对象**: 封装业务概念
- **领域服务**: 处理跨聚合逻辑

## 🚀 部署架构

### 环境分层
- **开发环境**: 单机部署，快速迭代
- **测试环境**: 模拟生产，完整测试
- **预生产**: 生产数据，性能验证
- **生产环境**: 高可用，负载均衡

### 容器化部署
\`\`\`yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    image: assessment/${arch_name}:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - postgres
      - redis
\`\`\`

## 📈 性能设计

### 缓存策略
- **多级缓存**: 应用缓存 + Redis缓存
- **缓存模式**: Cache-Aside + Write-Through
- **失效策略**: TTL + 主动刷新

### 数据库优化
- **读写分离**: 主从复制
- **分库分表**: 水平分片
- **索引优化**: 复合索引
- **连接池**: HikariCP优化

## 🔒 安全设计

### 认证授权
- **JWT认证**: 无状态认证
- **RBAC权限**: 基于角色的访问控制
- **多因子认证**: 增强安全性

### 数据安全
- **数据加密**: 传输加密 + 存储加密
- **输入验证**: 参数校验 + SQL注入防护
- **审计日志**: 操作记录 + 安全监控

## 📋 运维设计

### 监控体系
- **应用监控**: Spring Actuator
- **系统监控**: Prometheus
- **日志监控**: ELK Stack
- **性能监控**: APM工具

### 容灾备份
- **数据备份**: 定时备份 + 异地备份
- **服务容灾**: 多活部署
- **故障恢复**: 自动故障转移

---

**文档版本**: ${VERSION}  
**设计日期**: ${CURRENT_DATE}  
**架构师**: ${AUTHOR}  
**状态**: 设计阶段  
EOF

    echo -e "${GREEN}✅ 架构文档创建完成: ${arch_dir}/design.md${NC}"
}

# 创建质量文档
create_quality_doc() {
    local quality_name=$1
    local quality_dir="docs/quality/${quality_name}"
    
    echo -e "\n${BLUE}📏 创建质量文档: ${quality_name}${NC}"
    
    mkdir -p "$quality_dir"
    
    cat > "${quality_dir}/report.md" << EOF
# ${quality_name} 质量报告

## 📊 质量概述

${quality_name}质量检查报告，包含代码质量、测试覆盖率、性能指标等。

## 🎯 质量指标

### 代码质量
- **Checkstyle**: 100% 通过率
- **PMD**: 0个高级别问题
- **SpotBugs**: 0个Bug发现
- **SonarQube**: 质量门禁通过

### 测试覆盖率
- **单元测试**: ≥ 80%
- **集成测试**: ≥ 70%
- **端到端测试**: ≥ 60%
- **代码分支覆盖**: ≥ 75%

### 性能指标
- **响应时间**: P95 < 500ms
- **吞吐量**: ≥ 1000 TPS
- **资源使用**: CPU < 70%, Memory < 80%
- **错误率**: < 0.1%

## 📋 检查详情

### Checkstyle检查
\`\`\`bash
# 运行Checkstyle检查
./mvnw checkstyle:check

# 结果概览
Total files checked: [文件数]
Violations found: 0
Errors: 0
Warnings: 0
\`\`\`

### 测试覆盖率
\`\`\`bash
# 运行测试并生成覆盖率报告
./mvnw clean test jacoco:report

# 覆盖率统计
Line Coverage: [百分比]%
Branch Coverage: [百分比]%
Method Coverage: [百分比]%
Class Coverage: [百分比]%
\`\`\`

### 性能测试
\`\`\`bash
# JMeter性能测试
jmeter -n -t performance-test.jmx -l results.jtl

# 结果分析
Average Response Time: [毫秒]ms
95th Percentile: [毫秒]ms
Throughput: [数量] requests/second
Error Rate: [百分比]%
\`\`\`

## 🔧 问题修复

### 已修复问题
1. **[问题类型]**: [问题描述]
   - 修复方案: [具体修复措施]
   - 影响范围: [影响评估]
   - 修复时间: ${CURRENT_DATE}

### 待修复问题
1. **[问题类型]**: [问题描述]
   - 优先级: [高/中/低]
   - 计划修复时间: [预期时间]
   - 负责人: [责任人]

## 📈 趋势分析

### 质量趋势
- 代码质量持续改善
- 测试覆盖率稳步提升
- 性能指标保持稳定
- Bug修复及时有效

### 改进建议
1. 持续优化测试用例
2. 加强代码审查
3. 完善性能监控
4. 提升自动化水平

## ✅ 质量门禁

### 通过标准
- [ ] 所有Checkstyle检查通过
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 集成测试全部通过
- [ ] 性能测试满足要求
- [ ] 安全扫描无高危问题
- [ ] 代码审查完成

### 发布准入
满足以上所有标准，方可进入发布流程。

---

**报告版本**: ${VERSION}  
**生成时间**: ${CURRENT_DATE}  
**质量负责人**: QA团队  
**下次检查**: [下次检查日期]  
EOF

    echo -e "${GREEN}✅ 质量文档创建完成: ${quality_dir}/report.md${NC}"
}

# 创建后端技术文档
create_backend_doc() {
    local module_name=$1
    local backend_dir="backend/${module_name}"
    
    echo -e "\n${BLUE}⚙️ 创建后端文档: ${module_name}${NC}"
    
    mkdir -p "$backend_dir"
    
    cat > "${backend_dir}/README.md" << EOF
# ${module_name} 后端模块

## 📋 模块概述

${module_name}是智慧养老评估平台的后端模块，负责[模块功能描述]。

## 🏗️ 架构设计

### 技术栈
- **框架**: Spring Boot 3.5.3
- **数据访问**: Spring Data JPA
- **缓存**: Spring Cache + Redis
- **安全**: Spring Security + JWT
- **验证**: Hibernate Validator

### 模块结构
\`\`\`
${module_name}/
├── src/main/java/com/assessment/${module_name}/
│   ├── controller/          # REST控制器
│   ├── service/             # 业务服务层
│   ├── repository/          # 数据访问层
│   ├── entity/              # JPA实体类
│   ├── dto/                 # 数据传输对象
│   ├── config/              # 配置类
│   └── exception/           # 异常处理
├── src/main/resources/
│   ├── application.yml      # 配置文件
│   └── db/migration/        # 数据库迁移
└── src/test/java/           # 测试代码
\`\`\`

## 🛠️ 核心功能

### 主要特性
- [x] 功能特性1: [描述]
- [x] 功能特性2: [描述]
- [ ] 计划特性3: [描述]

### API接口
- \`GET /${module_name}\` - 获取列表
- \`POST /${module_name}\` - 创建记录
- \`PUT /${module_name}/{id}\` - 更新记录
- \`DELETE /${module_name}/{id}\` - 删除记录

## 🔧 开发指南

### 环境配置
\`\`\`bash
# 1. 启动依赖服务
docker-compose up -d postgres redis

# 2. 运行数据库迁移
./mvnw flyway:migrate

# 3. 启动应用
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
\`\`\`

### 代码规范
- 遵循Checkstyle规范
- 使用final参数声明
- 添加适当的注释和文档
- 编写单元测试

### 测试指南
\`\`\`bash
# 运行单元测试
./mvnw test -Dtest=${module_name}*Test

# 运行集成测试
./mvnw test -Dtest=${module_name}*IntegrationTest

# 生成覆盖率报告
./mvnw jacoco:report
\`\`\`

## 📊 性能优化

### 缓存策略
- 使用Redis缓存热点数据
- 配置合理的缓存过期时间
- 实现缓存更新策略

### 数据库优化
- 优化SQL查询
- 添加必要的索引
- 使用分页查询

## 🔒 安全考虑

### 认证授权
- JWT token验证
- 基于角色的权限控制
- 多租户数据隔离

### 输入验证
- 参数校验注解
- 自定义验证器
- SQL注入防护

## 🐛 常见问题

### 问题1: [问题描述]
**解决方案**: [解决步骤]

### 问题2: [问题描述]
**解决方案**: [解决步骤]

## 📚 相关文档

- [API文档](./API.md) - 详细的API接口说明
- [数据库设计](./DATABASE.md) - 数据库表结构
- [部署指南](./DEPLOYMENT.md) - 部署和运维
- [故障排查](./TROUBLESHOOTING.md) - 问题诊断

---

**模块版本**: ${VERSION}  
**最后更新**: ${CURRENT_DATE}  
**维护团队**: ${AUTHOR}  
**技术负责人**: [技术负责人]  
EOF

    echo -e "${GREEN}✅ 后端文档创建完成: ${backend_dir}/README.md${NC}"
}

# 创建前端文档
create_frontend_doc() {
    local app_name=$1
    local frontend_dir="frontend/packages/${app_name}"
    
    echo -e "\n${BLUE}🎨 创建前端文档: ${app_name}${NC}"
    
    mkdir -p "$frontend_dir"
    
    cat > "${frontend_dir}/README.md" << EOF
# ${app_name} 前端应用

## 📋 应用概述

${app_name}是智慧养老评估平台的前端应用，基于Vue 3 + TypeScript开发。

## 🛠️ 技术栈

### 核心技术
- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.0+
- **构建**: Vite 6.0+
- **状态管理**: Pinia 3.0+
- **路由**: Vue Router 4+
- **UI框架**: DaisyUI + TailwindCSS

### 开发工具
- **代码质量**: ESLint + Prettier
- **测试**: Vitest + Vue Test Utils
- **类型检查**: TypeScript strict mode
- **包管理**: npm + Turbo (Monorepo)

## 📁 项目结构

\`\`\`
${app_name}/
├── src/
│   ├── components/          # 公共组件
│   │   ├── common/         # 通用组件
│   │   ├── forms/          # 表单组件
│   │   └── layout/         # 布局组件
│   ├── views/              # 页面组件
│   │   ├── auth/           # 认证页面
│   │   ├── dashboard/      # 仪表板
│   │   └── ${app_name}/    # 功能页面
│   ├── composables/        # 组合式函数
│   ├── stores/             # Pinia状态管理
│   ├── api/                # API接口
│   ├── types/              # TypeScript类型
│   ├── utils/              # 工具函数
│   ├── assets/             # 静态资源
│   ├── styles/             # 样式文件
│   ├── router/             # 路由配置
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── public/                 # 公共静态文件
├── tests/                  # 测试文件
├── package.json            # 项目配置
├── vite.config.ts          # Vite配置
├── tsconfig.json           # TypeScript配置
├── tailwind.config.js      # TailwindCSS配置
└── README.md               # 本文档
\`\`\`

## 🚀 开发指南

### 环境启动
\`\`\`bash
# 安装依赖 (在根目录执行)
npm install

# 启动开发服务器
npm run dev:${app_name}

# 或者在应用目录内
cd frontend/packages/${app_name}
npm run dev
\`\`\`

### 构建部署
\`\`\`bash
# 构建生产版本
npm run build:${app_name}

# 预览构建结果
npm run preview

# 类型检查
npm run type-check

# 代码检查
npm run lint
npm run lint:fix
\`\`\`

## 🎨 组件开发

### 组件规范
\`\`\`vue
<!-- 标准组件模板 -->
<template>
  <div class="[组件名称]-container">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 类型定义
interface Props {
  title: string
  disabled?: boolean
}

interface Emits {
  submit: [data: any]
  cancel: []
}

// Props和Emits
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)

// 计算属性
const displayTitle = computed(() => 
  props.title || '默认标题'
)

// 方法
const handleSubmit = () => {
  emit('submit', { /* 数据 */ })
}
</script>

<style scoped>
.组件名称-container {
  @apply p-4 rounded-lg bg-base-100;
}
</style>
\`\`\`

### 状态管理
\`\`\`typescript
// stores/${app_name}Store.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const use${app_name^}Store = defineStore('${app_name}', () => {
  // 状态
  const items = ref<${app_name^}Item[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const itemCount = computed(() => items.value.length)

  // 操作
  const fetchItems = async () => {
    loading.value = true
    try {
      const response = await ${app_name}Api.getList()
      items.value = response.data
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const addItem = async (item: Create${app_name^}Request) => {
    const response = await ${app_name}Api.create(item)
    items.value.push(response.data)
  }

  return {
    items,
    loading,
    error,
    itemCount,
    fetchItems,
    addItem
  }
})
\`\`\`

### API接口
\`\`\`typescript
// api/${app_name}Api.ts
import { request } from '@/utils/request'

export interface ${app_name^}Item {
  id: number
  name: string
  status: string
  createdAt: string
}

export interface Create${app_name^}Request {
  name: string
  description?: string
}

export const ${app_name}Api = {
  // 获取列表
  getList: (params?: any) => 
    request.get<${app_name^}Item[]>('/api/${app_name}', { params }),

  // 创建
  create: (data: Create${app_name^}Request) =>
    request.post<${app_name^}Item>('/api/${app_name}', data),

  // 更新
  update: (id: number, data: Partial<${app_name^}Item>) =>
    request.put<${app_name^}Item>(\`/api/${app_name}/\${id}\`, data),

  // 删除
  delete: (id: number) =>
    request.delete(\`/api/${app_name}/\${id}\`)
}
\`\`\`

## 🧪 测试指南

### 组件测试
\`\`\`typescript
// tests/components/${app_name^}Component.test.ts
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import ${app_name^}Component from '@/components/${app_name^}Component.vue'

describe('${app_name^}Component', () => {
  it('渲染正确的内容', () => {
    const wrapper = mount(${app_name^}Component, {
      props: {
        title: '测试标题'
      }
    })

    expect(wrapper.text()).toContain('测试标题')
  })

  it('处理用户交互', async () => {
    const wrapper = mount(${app_name^}Component)
    
    await wrapper.find('[data-testid="submit-btn"]').trigger('click')
    
    expect(wrapper.emitted()).toHaveProperty('submit')
  })
})
\`\`\`

### Store测试
\`\`\`typescript
// tests/stores/${app_name}Store.test.ts
import { describe, it, expect, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { use${app_name^}Store } from '@/stores/${app_name}Store'

describe('${app_name}Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('初始状态正确', () => {
    const store = use${app_name^}Store()
    
    expect(store.items).toEqual([])
    expect(store.loading).toBe(false)
    expect(store.error).toBe(null)
  })

  it('fetchItems正常工作', async () => {
    const store = use${app_name^}Store()
    
    // Mock API
    vi.mock('@/api/${app_name}Api', () => ({
      ${app_name}Api: {
        getList: vi.fn().mockResolvedValue({
          data: [{ id: 1, name: '测试' }]
        })
      }
    }))

    await store.fetchItems()

    expect(store.items).toHaveLength(1)
    expect(store.loading).toBe(false)
  })
})
\`\`\`

## 🎨 UI设计规范

### 设计系统
- **主色**: \`#5357A0\` (长春花蓝)
- **辅色**: \`#7C3AED\` (紫色)
- **强调色**: \`#fed81f\` (黄色)
- **字体**: PingFang SC, Microsoft YaHei

### 组件库
使用DaisyUI组件，保持设计一致性：
\`\`\`vue
<template>
  <!-- 按钮 -->
  <button class="btn btn-primary">主要按钮</button>
  <button class="btn btn-secondary">次要按钮</button>
  
  <!-- 表单 -->
  <div class="form-control">
    <label class="label">
      <span class="label-text">标签</span>
    </label>
    <input type="text" class="input input-bordered" />
  </div>
  
  <!-- 卡片 -->
  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title">卡片标题</h2>
      <p>卡片内容</p>
    </div>
  </div>
</template>
\`\`\`

## 📱 响应式设计

### 断点规范
- **mobile**: < 768px
- **tablet**: 768px - 1024px
- **desktop**: > 1024px

### 适配策略
\`\`\`css
/* 移动端优先 */
.container {
  @apply px-4;
}

/* 平板端 */
@screen md {
  .container {
    @apply px-6;
  }
}

/* 桌面端 */
@screen lg {
  .container {
    @apply px-8;
  }
}
\`\`\`

## 🔧 性能优化

### 代码分割
\`\`\`typescript
// 路由懒加载
const routes = [
  {
    path: '/${app_name}',
    component: () => import('@/views/${app_name^}/Index.vue')
  }
]
\`\`\`

### 缓存策略
- 合理使用computed缓存
- 避免不必要的响应式数据
- 使用v-memo优化列表渲染

## 📚 相关文档

- [Monorepo架构说明](../../README.md)
- [共享组件库](../shared/README.md)
- [开发规范](../../../docs/development/frontend-guide.md)
- [部署指南](./DEPLOYMENT.md)

---

**应用版本**: ${VERSION}  
**最后更新**: ${CURRENT_DATE}  
**维护团队**: 前端团队  
**技术负责人**: [前端技术负责人]  
EOF

    echo -e "${GREEN}✅ 前端文档创建完成: ${frontend_dir}/README.md${NC}"
}

# 创建运维文档
create_operation_doc() {
    local op_name=$1
    local op_dir="docs/operations/${op_name}"
    
    echo -e "\n${BLUE}🚀 创建运维文档: ${op_name}${NC}"
    
    mkdir -p "$op_dir"
    
    cat > "${op_dir}/guide.md" << EOF
# ${op_name} 运维指南

## 📋 运维概述

${op_name}运维指南，包含部署、监控、故障处理等运维操作。

## 🛠️ 部署配置

### 环境要求
- **操作系统**: Linux (CentOS 7+/Ubuntu 18+)
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB以上SSD
- **网络**: 1Gbps带宽

### 软件依赖
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Java**: OpenJDK 21
- **Node.js**: 20.0+
- **PostgreSQL**: 15+
- **Redis**: 7+

## 🚀 部署流程

### 1. 环境准备
\`\`\`bash
# 安装Docker
curl -fsSL https://get.docker.com | sh
sudo systemctl enable docker
sudo systemctl start docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-linux-x86_64" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 创建应用目录
sudo mkdir -p /opt/assessment
sudo chown \$USER:docker /opt/assessment
\`\`\`

### 2. 应用部署
\`\`\`bash
# 克隆代码
cd /opt/assessment
git clone [repository-url] .

# 配置环境变量
cp .env.example .env
vim .env

# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
\`\`\`

### 3. 数据库初始化
\`\`\`bash
# 等待数据库启动
sleep 30

# 运行数据库迁移
docker-compose exec app ./mvnw flyway:migrate

# 初始化基础数据
docker-compose exec app ./scripts/init-data.sh
\`\`\`

## 📊 监控配置

### 应用监控
\`\`\`yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'assessment-app'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
\`\`\`

### 系统监控
\`\`\`bash
# 安装node_exporter
wget https://github.com/prometheus/node_exporter/releases/latest/download/node_exporter-linux-amd64.tar.gz
tar xvfz node_exporter-linux-amd64.tar.gz
sudo mv node_exporter /usr/local/bin/
sudo systemctl enable node_exporter
sudo systemctl start node_exporter
\`\`\`

### 日志监控
\`\`\`yaml
# filebeat.yml
filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /opt/assessment/logs/*.log
    fields:
      service: assessment
      environment: production

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
\`\`\`

## 🔧 运维操作

### 服务管理
\`\`\`bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
\`\`\`

### 备份操作
\`\`\`bash
# 数据库备份
docker-compose exec postgres pg_dump -U assessment assessment_db > backup_\$(date +%Y%m%d_%H%M%S).sql

# 应用备份
tar -czf app_backup_\$(date +%Y%m%d_%H%M%S).tar.gz /opt/assessment

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/backup/assessment"
DATE=\$(date +%Y%m%d_%H%M%S)

mkdir -p \$BACKUP_DIR

# 数据库备份
docker-compose exec postgres pg_dump -U assessment assessment_db > \$BACKUP_DIR/db_\$DATE.sql

# 删除7天前的备份
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete
\`\`\`

### 版本更新
\`\`\`bash
# 1. 备份当前版本
./scripts/backup.sh

# 2. 拉取最新代码
git pull origin main

# 3. 构建新镜像
docker-compose build

# 4. 滚动更新
docker-compose up -d --no-deps app

# 5. 运行数据库迁移
docker-compose exec app ./mvnw flyway:migrate

# 6. 验证服务
curl http://localhost:8080/actuator/health
\`\`\`

## 🚨 故障处理

### 常见故障

#### 1. 应用无法启动
**症状**: 容器启动失败或立即退出

**排查步骤**:
\`\`\`bash
# 查看容器日志
docker-compose logs app

# 检查配置文件
docker-compose config

# 检查端口占用
netstat -tlnp | grep 8080

# 检查资源使用
docker stats
\`\`\`

#### 2. 数据库连接失败
**症状**: 应用日志显示数据库连接错误

**排查步骤**:
\`\`\`bash
# 检查数据库状态
docker-compose exec postgres pg_isready

# 检查网络连接
docker-compose exec app ping postgres

# 检查数据库日志
docker-compose logs postgres
\`\`\`

#### 3. 性能问题
**症状**: 响应时间长，CPU/内存使用率高

**排查步骤**:
\`\`\`bash
# 查看系统资源
top
iostat
free -h

# 查看应用指标
curl http://localhost:8080/actuator/metrics

# 分析慢查询
docker-compose exec postgres psql -U assessment -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"
\`\`\`

### 应急预案

#### 服务完全不可用
1. 立即切换到备用环境
2. 检查主要故障原因
3. 修复问题或回滚版本
4. 恢复服务并验证

#### 数据库故障
1. 停止写入操作
2. 从备份恢复数据
3. 验证数据完整性
4. 恢复应用连接

## 📈 性能调优

### JVM调优
\`\`\`bash
# 环境变量配置
JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
\`\`\`

### 数据库调优
\`\`\`sql
-- PostgreSQL优化配置
ALTER SYSTEM SET shared_buffers = '1GB';
ALTER SYSTEM SET effective_cache_size = '4GB';
ALTER SYSTEM SET work_mem = '16MB';
ALTER SYSTEM SET maintenance_work_mem = '256MB';

-- 重启生效
SELECT pg_reload_conf();
\`\`\`

### 缓存优化
\`\`\`bash
# Redis配置优化
redis-cli CONFIG SET maxmemory 1gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
\`\`\`

## 📋 运维检查清单

### 日常检查 (每日)
- [ ] 检查服务运行状态
- [ ] 查看系统资源使用
- [ ] 检查错误日志
- [ ] 验证备份是否成功
- [ ] 检查监控告警

### 周期检查 (每周)
- [ ] 检查磁盘空间使用
- [ ] 清理过期日志文件
- [ ] 检查安全更新
- [ ] 验证灾备系统
- [ ] 性能指标分析

### 月度检查 (每月)
- [ ] 系统安全扫描
- [ ] 容量规划评估
- [ ] 备份恢复测试
- [ ] 文档更新
- [ ] 运维流程优化

---

**指南版本**: ${VERSION}  
**最后更新**: ${CURRENT_DATE}  
**运维团队**: DevOps团队  
**联系方式**: <EMAIL>  
EOF

    echo -e "${GREEN}✅ 运维文档创建完成: ${op_dir}/guide.md${NC}"
}

# 主函数处理不同类型的文档创建
case "$DOC_TYPE" in
    "feature")
        create_feature_doc "$DOC_NAME"
        ;;
    "architecture")
        create_architecture_doc "$DOC_NAME"
        ;;
    "quality")
        create_quality_doc "$DOC_NAME"
        ;;
    "backend")
        create_backend_doc "$DOC_NAME"
        ;;
    "frontend")
        create_frontend_doc "$DOC_NAME"
        ;;
    "operation")
        create_operation_doc "$DOC_NAME"
        ;;
    *)
        echo -e "${RED}❌ 不支持的文档类型: ${DOC_TYPE}${NC}"
        exit 1
        ;;
esac

# 自动同步到VitePress门户
echo -e "\n${BLUE}🔄 自动同步文档到VitePress门户...${NC}"
if [ -f "./scripts/sync-docs.sh" ]; then
    ./scripts/sync-docs.sh > /dev/null 2>&1
    echo -e "${GREEN}✅ 文档同步完成${NC}"
else
    echo -e "${YELLOW}⚠️ 同步脚本不存在，请手动运行 ./scripts/sync-docs.sh${NC}"
fi

echo -e "\n${PURPLE}🎉 文档创建完成！${NC}"
echo -e "${BLUE}📖 访问VitePress门户查看: http://localhost:3005${NC}"
echo -e "${YELLOW}💡 下一步: 根据实际需求完善文档内容${NC}"