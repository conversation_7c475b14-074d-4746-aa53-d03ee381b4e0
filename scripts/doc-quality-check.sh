#!/bin/bash

# 智慧养老评估平台 - 文档质量检查工具
# 检查文档覆盖率、有效性、规范性等质量指标

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo "=== 智慧养老评估平台文档质量检查工具 ==="

# 确保在项目根目录
if [ ! -d "docs" ] || [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 创建报告目录
REPORT_DIR="quality-reports/doc-quality-$(date '+%Y%m%d_%H%M%S')"
mkdir -p "$REPORT_DIR"

echo -e "\n${BLUE}📊 开始文档质量检查...${NC}"

# 1. 文档覆盖率检查
check_doc_coverage() {
    echo -e "\n${BLUE}📋 检查文档覆盖率...${NC}"
    
    # 统计代码文件
    java_controllers=$(find backend -name "*Controller.java" | wc -l)
    java_services=$(find backend -name "*Service.java" | wc -l)
    vue_components=$(find frontend -name "*.vue" | grep -v node_modules | wc -l)
    
    # 统计文档文件
    feature_docs=$(find docs/features -name "index.md" 2>/dev/null | wc -l)
    api_docs=$(find docs/features -name "api.md" 2>/dev/null | wc -l)
    arch_docs=$(find docs/architecture -name "*.md" | wc -l)
    
    # 计算覆盖率
    if [ $java_controllers -gt 0 ]; then
        feature_coverage=$((feature_docs * 100 / java_controllers))
    else
        feature_coverage=100
    fi
    
    if [ $java_controllers -gt 0 ]; then
        api_coverage=$((api_docs * 100 / java_controllers))
    else
        api_coverage=100
    fi
    
    echo "Controller数量: $java_controllers"
    echo "Service数量: $java_services"
    echo "Vue组件数量: $vue_components"
    echo ""
    echo "功能文档覆盖率: $feature_coverage% ($feature_docs/$java_controllers)"
    echo "API文档覆盖率: $api_coverage% ($api_docs/$java_controllers)"
    echo "架构文档数量: $arch_docs"
    
    # 生成覆盖率报告
    cat > "$REPORT_DIR/coverage-report.md" << EOF
# 文档覆盖率报告

## 统计概览

### 代码统计
- Java Controllers: $java_controllers
- Java Services: $java_services  
- Vue组件: $vue_components

### 文档统计
- 功能文档: $feature_docs
- API文档: $api_docs
- 架构文档: $arch_docs

### 覆盖率分析
- 功能文档覆盖率: **$feature_coverage%** ($feature_docs/$java_controllers)
- API文档覆盖率: **$api_coverage%** ($api_docs/$java_controllers)

### 建议
$(if [ $feature_coverage -lt 80 ]; then echo "- 🔴 功能文档覆盖率偏低，建议补充"; else echo "- ✅ 功能文档覆盖率良好"; fi)
$(if [ $api_coverage -lt 70 ]; then echo "- 🔴 API文档覆盖率偏低，建议补充"; else echo "- ✅ API文档覆盖率良好"; fi)

---
生成时间: $(date '+%Y-%m-%d %H:%M:%S')
EOF
    
    # 检查缺失的功能文档
    echo -e "\n🔍 检查缺失的功能文档..."
    missing_docs=0
    find backend -name "*Controller.java" | while read controller; do
        # 提取功能名称
        feature_name=$(basename "$controller" | sed 's/Controller\.java$//' | tr '[:upper:]' '[:lower:]')
        
        # 检查是否存在对应文档
        if [ ! -f "docs/features/$feature_name/index.md" ]; then
            echo "⚠️  缺失功能文档: $feature_name"
            echo "   建议运行: ./scripts/create-doc.sh feature $feature_name"
            missing_docs=$((missing_docs + 1))
        fi
    done
    
    echo "缺失功能文档数量: $missing_docs"
}

# 2. 文档链接有效性检查
check_doc_links() {
    echo -e "\n${BLUE}🔗 检查文档链接有效性...${NC}"
    
    broken_links=0
    total_links=0
    
    # 检查markdown文件中的内部链接
    find docs-config/docs-src -name "*.md" | while read doc; do
        # 提取markdown链接
        grep -o '\[.*\]([^)]*\.md[^)]*)' "$doc" 2>/dev/null | while read link; do
            total_links=$((total_links + 1))
            
            # 提取链接路径
            link_path=$(echo "$link" | sed 's/.*](\([^)]*\)).*/\1/')
            
            # 转换相对路径为绝对路径
            if [[ "$link_path" == ../* ]]; then
                # 相对路径
                doc_dir=$(dirname "$doc")
                full_path="$doc_dir/$link_path"
            elif [[ "$link_path" == ./* ]]; then
                # 当前目录
                doc_dir=$(dirname "$doc")
                full_path="$doc_dir/${link_path#./}"
            else
                # 绝对路径
                full_path="docs-config/docs-src/$link_path"
            fi
            
            # 检查文件是否存在
            if [ ! -f "$full_path" ]; then
                echo "❌ 断开的链接: $doc -> $link_path"
                broken_links=$((broken_links + 1))
            fi
        done
    done
    
    echo "检查链接总数: $total_links"
    echo "断开链接数量: $broken_links"
    
    if [ $broken_links -eq 0 ]; then
        echo -e "${GREEN}✅ 所有链接检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️ 发现 $broken_links 个断开链接${NC}"
    fi
}

# 3. 文档格式规范检查
check_doc_format() {
    echo -e "\n${BLUE}📝 检查文档格式规范...${NC}"
    
    format_issues=0
    total_docs=0
    
    find docs-config/docs-src -name "*.md" | while read doc; do
        total_docs=$((total_docs + 1))
        doc_issues=0
        
        # 检查是否有中文标题
        if ! grep -q '^# .*[\u4e00-\u9fff]' "$doc"; then
            echo "⚠️  $doc: 建议使用中文标题"
            doc_issues=$((doc_issues + 1))
        fi
        
        # 检查核心文档是否有版本信息
        if [[ "$doc" == *"/index.md" ]] && ! grep -q "文档版本:" "$doc"; then
            echo "⚠️  $doc: 建议添加版本信息"
            doc_issues=$((doc_issues + 1))
        fi
        
        # 检查是否有空标题
        if grep -q '^#[[:space:]]*$' "$doc"; then
            echo "❌ $doc: 发现空标题"
            doc_issues=$((doc_issues + 1))
        fi
        
        # 检查标题层级是否正确
        heading_levels=$(grep '^#' "$doc" | sed 's/^\(#*\).*/\1/' | wc -c)
        if [ $heading_levels -gt 0 ]; then
            # 检查是否跳级（简化检查）
            if grep -q '^###' "$doc" && ! grep -q '^##' "$doc"; then
                echo "⚠️  $doc: 标题层级可能跳级"
                doc_issues=$((doc_issues + 1))
            fi
        fi
        
        format_issues=$((format_issues + doc_issues))
    done
    
    echo "检查文档总数: $total_docs"
    echo "格式问题数量: $format_issues"
    
    if [ $format_issues -eq 0 ]; then
        echo -e "${GREEN}✅ 文档格式检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️ 发现 $format_issues 个格式问题${NC}"
    fi
}

# 4. 文档新鲜度检查
check_doc_freshness() {
    echo -e "\n${BLUE}📅 检查文档新鲜度...${NC}"
    
    old_docs=0
    total_docs=0
    cutoff_date=$(date -d '30 days ago' '+%Y-%m-%d')
    
    find docs-config/docs-src -name "*.md" | while read doc; do
        total_docs=$((total_docs + 1))
        
        # 获取文件最后修改时间
        file_date=$(stat -f "%Sm" -t "%Y-%m-%d" "$doc" 2>/dev/null || stat -c "%y" "$doc" 2>/dev/null | cut -d' ' -f1)
        
        if [[ "$file_date" < "$cutoff_date" ]]; then
            echo "📅 可能过时的文档: $doc (最后修改: $file_date)"
            old_docs=$((old_docs + 1))
        fi
    done
    
    echo "检查文档总数: $total_docs"
    echo "可能过时文档: $old_docs (超过30天未更新)"
    
    if [ $old_docs -eq 0 ]; then
        echo -e "${GREEN}✅ 文档新鲜度良好${NC}"
    else
        echo -e "${YELLOW}⚠️ 有 $old_docs 个文档可能需要更新${NC}"
    fi
}

# 5. 生成质量评分
calculate_quality_score() {
    echo -e "\n${BLUE}📊 计算文档质量评分...${NC}"
    
    # 读取各项指标（简化计算）
    feature_coverage=${feature_coverage:-50}
    api_coverage=${api_coverage:-50}
    
    # 权重计算质量分数
    coverage_score=$((feature_coverage * 40 / 100 + api_coverage * 30 / 100))
    format_score=$((format_issues == 0 ? 20 : format_issues < 5 ? 15 : 10))
    link_score=$((broken_links == 0 ? 10 : broken_links < 3 ? 7 : 5))
    
    total_score=$((coverage_score + format_score + link_score))
    
    echo "质量评分构成:"
    echo "  - 文档覆盖率: $coverage_score/70"
    echo "  - 格式规范性: $format_score/20"  
    echo "  - 链接有效性: $link_score/10"
    echo ""
    echo "总体质量评分: $total_score/100"
    
    if [ $total_score -ge 85 ]; then
        echo -e "${GREEN}🏆 优秀 (≥85分)${NC}"
        quality_level="优秀"
    elif [ $total_score -ge 70 ]; then
        echo -e "${BLUE}👍 良好 (70-84分)${NC}"
        quality_level="良好"
    elif [ $total_score -ge 55 ]; then
        echo -e "${YELLOW}⚠️ 一般 (55-69分)${NC}"
        quality_level="一般"
    else
        echo -e "${RED}❌ 需要改进 (<55分)${NC}"
        quality_level="需要改进"
    fi
    
    # 生成质量报告
    cat > "$REPORT_DIR/quality-summary.md" << EOF
# 文档质量评估报告

## 评分概览

**总体质量评分**: $total_score/100 (**$quality_level**)

### 评分详情
- 文档覆盖率: $coverage_score/70
  - 功能文档覆盖率: $feature_coverage%
  - API文档覆盖率: $api_coverage%
- 格式规范性: $format_score/20
  - 格式问题数量: $format_issues
- 链接有效性: $link_score/10
  - 断开链接数量: $broken_links

## 改进建议

$(if [ $feature_coverage -lt 80 ]; then echo "### 🔴 文档覆盖率"; echo "- 当前功能文档覆盖率为 $feature_coverage%，建议补充缺失的功能文档"; echo "- 运行 \`./scripts/create-doc.sh feature [功能名]\` 创建新文档"; echo ""; fi)

$(if [ $api_coverage -lt 70 ]; then echo "### 🔴 API文档"; echo "- 当前API文档覆盖率为 $api_coverage%，建议为每个Controller添加API文档"; echo ""; fi)

$(if [ $format_issues -gt 0 ]; then echo "### 🔴 格式规范"; echo "- 发现 $format_issues 个格式问题，建议统一文档格式"; echo "- 确保所有文档使用中文标题"; echo "- 为核心文档添加版本信息"; echo ""; fi)

$(if [ $broken_links -gt 0 ]; then echo "### 🔴 链接有效性"; echo "- 发现 $broken_links 个断开链接，需要修复或更新"; echo ""; fi)

## 质量趋势

$(if [ $total_score -ge 85 ]; then echo "✅ 文档质量优秀，继续保持"; elif [ $total_score -ge 70 ]; then echo "👍 文档质量良好，可进一步优化"; elif [ $total_score -ge 55 ]; then echo "⚠️ 文档质量一般，建议重点改进覆盖率"; else echo "❌ 文档质量需要显著改进"; fi)

---
**报告生成时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**检查工具版本**: v1.0  
**下次建议检查**: $(date -d '+1 week' '+%Y-%m-%d')
EOF
}

# 6. 生成改进行动计划
generate_action_plan() {
    echo -e "\n${BLUE}📋 生成改进行动计划...${NC}"
    
    cat > "$REPORT_DIR/action-plan.md" << EOF
# 文档质量改进行动计划

## 🎯 改进目标

基于当前质量评分 $total_score/100，制定以下改进计划：

## 📅 短期计划 (1-2周)

### 高优先级任务
$(if [ $broken_links -gt 0 ]; then echo "- [ ] 修复 $broken_links 个断开的文档链接"; fi)
$(if [ $format_issues -gt 5 ]; then echo "- [ ] 修复主要的文档格式问题"; fi)
$(if [ $feature_coverage -lt 60 ]; then echo "- [ ] 为缺失的核心功能补充文档"; fi)

### 中优先级任务
$(if [ $feature_coverage -lt 80 ]; then echo "- [ ] 提升功能文档覆盖率至80%以上"; fi)
$(if [ $api_coverage -lt 70 ]; then echo "- [ ] 补充API文档，达到70%覆盖率"; fi)
- [ ] 统一文档格式和版本标记

## 📈 中期计划 (1个月)

### 质量提升
- [ ] 建立文档更新机制
- [ ] 完善Git Hooks自动检查
- [ ] 设置文档质量监控

### 流程优化
- [ ] 制定文档维护责任制
- [ ] 建立定期文档审查机制
- [ ] 完善文档模板和规范

## 🚀 长期规划 (3个月)

### 自动化建设
- [ ] 集成CI/CD文档检查
- [ ] 建立文档质量仪表板
- [ ] 实现文档变更通知机制

### 团队协作
- [ ] 开展文档质量培训
- [ ] 建立文档最佳实践分享
- [ ] 设立文档质量奖励机制

## 📊 成功指标

- **总体质量评分**: 达到85分以上
- **文档覆盖率**: 功能文档90%+，API文档80%+
- **断开链接**: 保持0个
- **格式问题**: 少于3个
- **更新及时性**: 代码变更后7天内更新文档

## 🏃‍♂️ 立即行动

### 今日可执行
\`\`\`bash
# 1. 修复断开链接
# [具体断开链接列表]

# 2. 创建缺失的功能文档
$(find backend -name "*Controller.java" | head -3 | while read controller; do
    feature_name=$(basename "$controller" | sed 's/Controller\.java$//' | tr '[:upper:]' '[:lower:]')
    if [ ! -f "docs/features/$feature_name/index.md" ]; then
        echo "./scripts/create-doc.sh feature $feature_name"
    fi
done)

# 3. 运行文档同步
./scripts/sync-docs.sh
\`\`\`

### 本周目标
- 完成高优先级任务的50%
- 提升质量评分至少10分
- 建立定期检查机制

---
**计划制定时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**计划负责人**: 开发团队  
**审查周期**: 每周检查进度
EOF
}

# 执行所有检查
echo -e "\n${PURPLE}🔍 开始执行文档质量检查...${NC}"

check_doc_coverage
check_doc_links  
check_doc_format
check_doc_freshness
calculate_quality_score
generate_action_plan

# 生成综合报告
echo -e "\n${BLUE}📋 生成综合质量报告...${NC}"

cat > "$REPORT_DIR/README.md" << EOF
# 文档质量检查报告

**检查时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**质量评分**: $total_score/100 (**$quality_level**)

## 📊 报告文件

- [质量评分摘要](./quality-summary.md) - 总体质量评分和建议
- [覆盖率报告](./coverage-report.md) - 文档覆盖率分析
- [改进行动计划](./action-plan.md) - 具体改进措施

## 🎯 关键发现

- 功能文档覆盖率: $feature_coverage%
- API文档覆盖率: $api_coverage%  
- 格式问题数量: $format_issues
- 断开链接数量: $broken_links

## 📈 下一步行动

$(if [ $total_score -lt 70 ]; then echo "🔴 **立即行动**: 质量评分偏低，需要重点改进"; elif [ $total_score -lt 85 ]; then echo "🟡 **持续改进**: 质量良好，可进一步优化"; else echo "🟢 **保持现状**: 质量优秀，定期维护即可"; fi)

详细的改进计划请查看 [action-plan.md](./action-plan.md)

---
*报告由文档质量检查工具自动生成*
EOF

echo -e "\n${GREEN}🎉 文档质量检查完成！${NC}"
echo -e "${BLUE}📋 报告保存位置: $REPORT_DIR${NC}"
echo -e "${PURPLE}📊 总体质量评分: $total_score/100 ($quality_level)${NC}"

if [ $total_score -lt 70 ]; then
    echo -e "${YELLOW}💡 建议查看改进行动计划: $REPORT_DIR/action-plan.md${NC}"
fi

echo -e "\n${BLUE}🔗 快速访问报告:${NC}"
echo -e "   cat $REPORT_DIR/quality-summary.md"
echo -e "   cat $REPORT_DIR/action-plan.md"