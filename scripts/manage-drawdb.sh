#!/bin/bash

# DrawDB服务管理脚本
# 提供启动、停止、重启、查看状态等功能

DOCKER_COMPOSE_FILE="$(dirname "$0")/../docker/docker-compose.drawdb.yml"

show_help() {
    echo "DrawDB服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动DrawDB服务"
    echo "  stop      停止DrawDB服务"
    echo "  restart   重启DrawDB服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  clean     清理服务数据"
    echo "  update    更新DrawDB镜像"
    echo "  help      显示此帮助信息"
}

start_service() {
    echo "🚀 启动DrawDB服务..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    echo "⏳ 等待服务就绪..."
    timeout=60
    count=0
    
    while [ $count -lt $timeout ]; do
        if curl -f http://localhost:3001/health >/dev/null 2>&1; then
            echo "✅ DrawDB服务启动成功！"
            echo "🌐 访问地址: http://localhost:3001"
            return 0
        fi
        sleep 2
        count=$((count + 2))
    done
    
    echo "❌ 服务启动超时"
    return 1
}

stop_service() {
    echo "🛑 停止DrawDB服务..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    echo "✅ 服务已停止"
}

restart_service() {
    echo "🔄 重启DrawDB服务..."
    stop_service
    sleep 2
    start_service
}

show_status() {
    echo "📊 DrawDB服务状态:"
    docker-compose -f "$DOCKER_COMPOSE_FILE" ps
    
    echo ""
    echo "🌐 网络检查:"
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        echo "✅ DrawDB Web服务: 正常"
    else
        echo "❌ DrawDB Web服务: 异常"
    fi
    
    if nc -z localhost 6380 2>/dev/null; then
        echo "✅ Redis存储服务: 正常"
    else
        echo "❌ Redis存储服务: 异常"
    fi
}

show_logs() {
    echo "📋 DrawDB服务日志:"
    docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f --tail=50
}

clean_data() {
    echo "⚠️  警告: 这将删除所有DrawDB数据!"
    read -p "确定要继续吗? (y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo "🧹 清理服务数据..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down -v
        docker volume rm $(docker volume ls -q | grep drawdb) 2>/dev/null || echo "没有找到相关数据卷"
        echo "✅ 数据清理完成"
    else
        echo "❌ 操作已取消"
    fi
}

update_service() {
    echo "🔄 更新DrawDB镜像..."
    
    # 停止服务
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # 重新构建镜像
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    # 启动服务
    start_service
    
    echo "✅ 更新完成"
}

# 主逻辑
case "${1:-help}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    clean)
        clean_data
        ;;
    update)
        update_service
        ;;
    help|*)
        show_help
        ;;
esac