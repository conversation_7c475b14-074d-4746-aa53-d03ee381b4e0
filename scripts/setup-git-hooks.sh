#!/bin/bash

# 智慧养老评估平台 - Git Hooks 设置脚本
# 自动设置Git钩子来确保文档同步和质量检查

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "=== Git Hooks 设置工具 ==="

# 确保在项目根目录
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ 请在Git项目根目录运行此脚本${NC}"
    exit 1
fi

# 创建hooks目录
mkdir -p .git/hooks

echo -e "\n${BLUE}📋 创建Git Hooks...${NC}"

# Pre-commit hook - 提交前检查
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

# 文档质量检查和自动同步

echo "🔍 运行 pre-commit 检查..."

# 检查是否有新增功能但缺少文档
check_missing_docs() {
    echo "📋 检查文档覆盖率..."
    
    # 检查是否有新增的Controller但没有对应功能文档
    new_controllers=$(git diff --cached --name-only | grep -E 'Controller\.java$' | head -5)
    if [ -n "$new_controllers" ]; then
        echo "🔍 发现新增Controller，检查对应文档..."
        for controller in $new_controllers; do
            # 提取功能名称
            feature_name=$(basename "$controller" | sed 's/Controller\.java$//' | tr '[:upper:]' '[:lower:]')
            
            # 检查是否存在对应的功能文档
            if [ ! -d "docs/features/$feature_name" ]; then
                echo "⚠️  建议为新功能 $feature_name 创建文档:"
                echo "   ./scripts/create-doc.sh feature $feature_name"
            fi
        done
    fi
    
    # 检查是否有新增的Vue组件但没有对应文档
    new_components=$(git diff --cached --name-only | grep -E 'frontend/.*\.vue$' | head -5)
    if [ -n "$new_components" ]; then
        echo "🔍 检查前端组件文档..."
        # 可以添加组件文档检查逻辑
    fi
}

# 验证文档格式
validate_docs() {
    echo "📝 验证文档格式..."
    
    # 检查暂存的markdown文件
    staged_docs=$(git diff --cached --name-only | grep '\.md$')
    
    if [ -n "$staged_docs" ]; then
        for doc in $staged_docs; do
            if [ -f "$doc" ]; then
                # 检查是否包含中文标题
                if ! grep -q '^# .*[\u4e00-\u9fff]' "$doc"; then
                    echo "⚠️  $doc 建议使用中文标题"
                fi
                
                # 检查是否有版本信息（对于核心文档）
                if [[ "$doc" == docs/*/index.md ]] && ! grep -q "文档版本:" "$doc"; then
                    echo "⚠️  $doc 建议添加版本信息"
                fi
            fi
        done
    fi
}

# 运行Checkstyle检查（如果有Java文件变更）
run_checkstyle() {
    java_files=$(git diff --cached --name-only | grep '\.java$')
    if [ -n "$java_files" ]; then
        echo "📏 运行 Checkstyle 检查..."
        if ! ./mvnw checkstyle:check -q; then
            echo "❌ Checkstyle 检查失败，请修复后重新提交"
            exit 1
        fi
        echo "✅ Checkstyle 检查通过"
    fi
}

# 运行前端代码检查（如果有前端文件变更）
run_frontend_lint() {
    frontend_files=$(git diff --cached --name-only | grep -E '\.(vue|ts|js)$' | grep frontend)
    if [ -n "$frontend_files" ]; then
        echo "🎨 运行前端代码检查..."
        cd frontend
        if ! npm run lint > /dev/null 2>&1; then
            echo "❌ 前端代码检查失败，请修复后重新提交"
            exit 1
        fi
        cd ..
        echo "✅ 前端代码检查通过"
    fi
}

# 执行检查
check_missing_docs
validate_docs
run_checkstyle
run_frontend_lint

echo "✅ Pre-commit 检查完成"
EOF

# Post-commit hook - 提交后自动同步文档
cat > .git/hooks/post-commit << 'EOF'
#!/bin/bash

echo "🔄 运行 post-commit 处理..."

# 检查是否有文档变更
doc_changes=$(git diff HEAD~1 HEAD --name-only | grep -E '\.(md)$')

if [ -n "$doc_changes" ]; then
    echo "📚 检测到文档变更，自动同步到VitePress..."
    
    # 运行文档同步脚本
    if [ -f "./scripts/sync-docs.sh" ]; then
        ./scripts/sync-docs.sh > /dev/null 2>&1
        echo "✅ 文档同步完成"
    else
        echo "⚠️  同步脚本不存在"
    fi
    
    # 生成文档变更报告
    echo "📋 文档变更报告:"
    for doc in $doc_changes; do
        echo "   - $doc"
    done
fi

echo "✅ Post-commit 处理完成"
EOF

# Post-merge hook - 合并后处理
cat > .git/hooks/post-merge << 'EOF'
#!/bin/bash

echo "🔀 运行 post-merge 处理..."

# 检查合并是否包含文档变更
doc_changes=$(git diff HEAD@{1} HEAD --name-only | grep -E '\.(md)$')

if [ -n "$doc_changes" ]; then
    echo "📚 合并包含文档变更，执行同步..."
    
    # 运行文档同步
    if [ -f "./scripts/sync-docs.sh" ]; then
        ./scripts/sync-docs.sh > /dev/null 2>&1
        echo "✅ 文档同步完成"
    fi
    
    # 检查文档链接有效性
    echo "🔗 检查文档链接..."
    # 可以添加链接检查逻辑
fi

# 检查依赖更新
if git diff HEAD@{1} HEAD --name-only | grep -q "package-lock.json\|pom.xml"; then
    echo "📦 检测到依赖变更，建议运行安装命令"
fi

echo "✅ Post-merge 处理完成"
EOF

# Prepare-commit-msg hook - 准备提交信息
cat > .git/hooks/prepare-commit-msg << 'EOF'
#!/bin/bash

# 根据变更内容自动建议commit message前缀

COMMIT_MSG_FILE=$1
COMMIT_SOURCE=$2

# 只处理用户输入的提交（不是merge、cherry-pick等）
if [ "$COMMIT_SOURCE" = "" ]; then
    # 分析暂存的文件变更
    staged_files=$(git diff --cached --name-only)
    
    # 根据文件类型建议前缀
    if echo "$staged_files" | grep -q '\.md$'; then
        if [ -z "$(head -1 "$COMMIT_MSG_FILE" | grep -E '^(feat|fix|docs|style|refactor|test|chore):')" ]; then
            echo "docs: " > "$COMMIT_MSG_FILE.tmp"
            cat "$COMMIT_MSG_FILE" >> "$COMMIT_MSG_FILE.tmp"
            mv "$COMMIT_MSG_FILE.tmp" "$COMMIT_MSG_FILE"
        fi
    elif echo "$staged_files" | grep -q '\.java$'; then
        if [ -z "$(head -1 "$COMMIT_MSG_FILE" | grep -E '^(feat|fix|docs|style|refactor|test|chore):')" ]; then
            echo "feat: " > "$COMMIT_MSG_FILE.tmp"
            cat "$COMMIT_MSG_FILE" >> "$COMMIT_MSG_FILE.tmp"
            mv "$COMMIT_MSG_FILE.tmp" "$COMMIT_MSG_FILE"
        fi
    fi
fi
EOF

# 设置执行权限
chmod +x .git/hooks/pre-commit
chmod +x .git/hooks/post-commit
chmod +x .git/hooks/post-merge
chmod +x .git/hooks/prepare-commit-msg

echo -e "${GREEN}✅ Git Hooks 设置完成${NC}"

echo -e "\n${BLUE}📋 已创建的 Hooks:${NC}"
echo -e "   - pre-commit: 提交前质量检查和文档验证"
echo -e "   - post-commit: 提交后自动同步文档"
echo -e "   - post-merge: 合并后处理文档和依赖"
echo -e "   - prepare-commit-msg: 自动建议commit消息前缀"

echo -e "\n${YELLOW}💡 Hook 功能说明:${NC}"
echo -e "   🔍 自动检查新增功能是否有对应文档"
echo -e "   📏 运行 Checkstyle 和前端代码检查"
echo -e "   📝 验证文档格式和中文规范"
echo -e "   🔄 自动同步文档到 VitePress 门户"
echo -e "   📋 生成文档变更报告"

echo -e "\n${GREEN}🎉 现在每次 git commit 时会自动检查和同步文档！${NC}"