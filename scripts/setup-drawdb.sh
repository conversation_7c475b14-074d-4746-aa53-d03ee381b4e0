#!/bin/bash

# DrawDB集成环境搭建脚本
# 用于快速部署DrawDB到本地开发环境

set -e

echo "🚀 开始设置DrawDB集成环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ 端口 $port 已被占用，请先关闭占用该端口的程序"
        exit 1
    else
        echo "✅ 端口 $port 可用"
    fi
}

echo "🔍 检查端口可用性..."
check_port 3001

# 创建项目网络（如果不存在）
echo "🌐 创建Docker网络..."
docker network create assessment_network 2>/dev/null || echo "网络已存在，跳过创建"

# 构建并启动DrawDB服务
echo "🔨 构建DrawDB镜像..."
cd "$(dirname "$0")/../docker"

# 构建DrawDB
echo "📦 构建DrawDB容器..."
docker-compose -f docker-compose.drawdb.yml build

echo "🚀 启动DrawDB服务..."
docker-compose -f docker-compose.drawdb.yml up -d

# 等待服务启动
echo "⏳ 等待DrawDB服务启动..."
timeout=60
count=0

while [ $count -lt $timeout ]; do
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        echo "✅ DrawDB服务启动成功！"
        break
    fi
    echo "等待中... ($count/$timeout)"
    sleep 2
    count=$((count + 2))
done

if [ $count -ge $timeout ]; then
    echo "❌ DrawDB服务启动超时"
    echo "📋 查看日志:"
    docker-compose -f docker-compose.drawdb.yml logs drawdb
    exit 1
fi

# 验证服务状态
echo "🔍 验证服务状态..."
echo "DrawDB状态: $(docker-compose -f docker-compose.drawdb.yml ps drawdb --format 'table {{.Service}}\t{{.Status}}')"

echo ""
echo "🎉 DrawDB环境设置完成！"
echo ""
echo "📊 服务信息:"
echo "  - DrawDB Web界面: http://localhost:3001"
echo "  - 健康检查: http://localhost:3001/health"
echo "  - Redis存储: localhost:6380"
echo ""
echo "🔧 管理命令:"
echo "  查看日志: docker-compose -f docker-compose.drawdb.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.drawdb.yml down"
echo "  重启服务: docker-compose -f docker-compose.drawdb.yml restart"
echo ""
echo "💡 下一步:"
echo "  1. 启动前端开发服务器: cd frontend/packages/admin && npm run dev"
echo "  2. 访问数据库设计页面: http://localhost:5173/assessment/database-design"
echo "  3. 开始使用拖拽式数据库设计器！"