#!/bin/bash

# 智慧养老评估平台 - 简化文档生成工具

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "=== 文档生成工具 ==="

if [ $# -lt 2 ]; then
    echo -e "${RED}用法: $0 <类型> <名称>${NC}"
    echo "类型: feature, architecture, quality, backend, frontend"
    exit 1
fi

DOC_TYPE=$1
DOC_NAME=$2
CURRENT_DATE=$(date '+%Y年%m月%d日')

create_feature_doc() {
    local name=$1
    local dir="docs/features/${name}"
    
    echo -e "${BLUE}创建功能文档: ${name}${NC}"
    mkdir -p "$dir"
    
    cat > "${dir}/index.md" << EOF
# ${name} 功能模块

## 📋 功能概述

${name}功能模块的详细说明。

## 🎯 主要特性

- 特性1: 描述
- 特性2: 描述
- 特性3: 描述

## 🛠️ 技术实现

### 后端实现
- Controller: \`${name}Controller\`
- Service: \`${name}Service\`
- Repository: \`${name}Repository\`

### 前端实现
- 组件: \`${name}Component\`
- Store: \`${name}Store\`
- API: \`${name}Api\`

## 📚 相关文档

- [API文档](./api.md)
- [测试指南](./testing.md)
- [故障排查](./troubleshooting.md)

---

**文档版本**: v1.0  
**创建日期**: ${CURRENT_DATE}  
**维护团队**: 开发团队
EOF

    cat > "${dir}/api.md" << EOF
# ${name} API文档

## 📡 API接口

### 获取列表
\`\`\`http
GET /api/${name}
Authorization: Bearer {token}
\`\`\`

### 创建记录
\`\`\`http
POST /api/${name}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "示例",
  "description": "描述"
}
\`\`\`

---

**最后更新**: ${CURRENT_DATE}
EOF

    cat > "${dir}/testing.md" << EOF
# ${name} 测试指南

## 🧪 测试策略

### 单元测试
- Service层测试
- Repository层测试

### 集成测试
- Controller层测试
- API接口测试

### 测试命令
\`\`\`bash
# 运行测试
./mvnw test -Dtest=${name}*Test

# 生成覆盖率报告
./mvnw jacoco:report
\`\`\`

---

**最后更新**: ${CURRENT_DATE}
EOF

    echo -e "${GREEN}✅ 功能文档创建完成: ${dir}${NC}"
}

case "$DOC_TYPE" in
    "feature")
        create_feature_doc "$DOC_NAME"
        ;;
    *)
        echo -e "${RED}暂不支持的类型: ${DOC_TYPE}${NC}"
        exit 1
        ;;
esac

# 自动同步
if [ -f "./scripts/sync-docs.sh" ]; then
    echo -e "${BLUE}同步文档到VitePress...${NC}"
    ./scripts/sync-docs.sh > /dev/null 2>&1
    echo -e "${GREEN}✅ 同步完成${NC}"
fi

echo -e "${GREEN}🎉 文档创建完成！${NC}"