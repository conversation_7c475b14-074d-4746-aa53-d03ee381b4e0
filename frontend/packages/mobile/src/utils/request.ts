/**
 * 网络请求工具
 * 基于uni.request封装，提供统一的请求处理、错误处理、拦截器等功能
 */

// 类型定义
interface RequestConfig {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
  showLoading?: boolean
  loadingText?: string
  showError?: boolean
  url?: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'OPTIONS' | 'HEAD' | 'TRACE' | 'CONNECT'
  data?: any
  params?: any
  filePath?: string
  name?: string
  formData?: Record<string, any>
  onProgress?: (progress: any) => void
  responseType?: string
}

interface ResponseData<T = any> {
  code: number
  message: string
  data: T
  success?: boolean
}

interface RequestError {
  code: number
  message: string
  data: any
  originalError?: any
  originalResponse?: any
}

interface Interceptor<T> {
  fulfilled?: (config: T) => T | Promise<T>
  rejected?: (error: any) => any | Promise<any>
}

interface HttpMethods {
  get: (url: string, params?: any, options?: RequestConfig) => Promise<any>
  post: (url: string, data?: any, options?: RequestConfig) => Promise<any>
  put: (url: string, data?: any, options?: RequestConfig) => Promise<any>
  delete: (url: string, params?: any, options?: RequestConfig) => Promise<any>
}

interface UniResponse {
  data: any
  statusCode: number
  header: Record<string, string>
  errMsg?: string
}

// 请求配置
const config: RequestConfig = {
  // 基础URL，开发环境使用Vite代理，生产环境使用完整URL
  baseURL: import.meta.env.VITE_API_BASE_URL || 
    ((import.meta.env as any)?.MODE === 'development' ? '' : 'https://api.assessment.com'),

  // 请求超时时间
  timeout: 30000,

  // 默认请求头
  headers: {
    'Content-Type': 'application/json'
  },

  // 是否显示loading
  showLoading: true,

  // loading文本
  loadingText: '请求中...',

  // 是否显示错误提示
  showError: true
}

// 请求拦截器队列
const requestInterceptors: Interceptor<RequestConfig>[] = []
// 响应拦截器队列
const responseInterceptors: Interceptor<UniResponse>[] = []

/**
 * 添加请求拦截器
 * @param {Function} fulfilled 成功回调
 * @param {Function} rejected 失败回调
 */
function addRequestInterceptor(
  fulfilled?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>,
  rejected?: (error: any) => any | Promise<any>
): void {
  requestInterceptors.push({ fulfilled, rejected })
}

/**
 * 添加响应拦截器
 * @param {Function} fulfilled 成功回调
 * @param {Function} rejected 失败回调
 */
function addResponseInterceptor(
  fulfilled?: (response: UniResponse) => UniResponse | Promise<UniResponse>,
  rejected?: (error: any) => any | Promise<any>
): void {
  responseInterceptors.push({ fulfilled, rejected })
}

/**
 * 执行请求拦截器
 * @param {Object} config 请求配置
 * @returns {Object} 处理后的配置
 */
async function executeRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
  let processedConfig = config

  for (const interceptor of requestInterceptors) {
    try {
      if (interceptor.fulfilled) {
        processedConfig = await interceptor.fulfilled(processedConfig)
      }
    } catch (error) {
      if (interceptor.rejected) {
        return await interceptor.rejected(error)
      }
      throw error
    }
  }

  return processedConfig
}

/**
 * 执行响应拦截器
 * @param {Object} response 响应数据
 * @returns {Object} 处理后的响应
 */
async function executeResponseInterceptors(response: UniResponse): Promise<UniResponse> {
  let processedResponse = response

  for (const interceptor of responseInterceptors) {
    try {
      if (interceptor.fulfilled) {
        processedResponse = await interceptor.fulfilled(processedResponse)
      }
    } catch (error) {
      if (interceptor.rejected) {
        return await interceptor.rejected(error)
      }
      throw error
    }
  }

  return processedResponse
}

/**
 * 显示loading
 * @param {String} title loading文本
 */
function showLoading(title = config.loadingText): void {
  uni.showLoading({
    title: title || '请求中...',
    mask: true
  })
}

/**
 * 隐藏loading
 */
function hideLoading(): void {
  uni.hideLoading()
}

/**
 * 显示错误提示
 * @param {String} message 错误信息
 */
function showError(message: string): void {
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  })
}

/**
 * 处理请求错误
 * @param {Object} error 错误对象
 * @param {Object} requestConfig 请求配置
 */
function handleRequestError(error: any, requestConfig: RequestConfig): Promise<never> {
  console.error('Request Error:', error)

  let errorMessage = '网络请求失败'

  if (error.errMsg) {
    if (error.errMsg.includes('timeout')) {
      errorMessage = '请求超时，请检查网络连接'
    } else if (error.errMsg.includes('fail')) {
      errorMessage = '网络连接失败，请检查网络设置'
    }
  }

  if (requestConfig.showError !== false) {
    showError(errorMessage)
  }

  return Promise.reject({
    code: -1,
    message: errorMessage,
    data: null,
    originalError: error
  })
}

/**
 * 处理响应错误
 * @param {Object} response 响应对象
 * @param {Object} requestConfig 请求配置
 */
function handleResponseError(response: UniResponse, requestConfig: RequestConfig): Promise<never> {
  console.error('Response Error:', response)

  let errorMessage = '服务器响应异常'

  // 根据状态码处理不同错误
  switch (response.statusCode) {
    case 400:
      errorMessage = '请求参数错误'
      break
    case 401:
      errorMessage = '未授权，请重新登录'
      // 可以在这里处理登录过期逻辑
      // store.dispatch('user/logout')
      break
    case 403:
      errorMessage = '拒绝访问'
      break
    case 404:
      errorMessage = '请求的资源不存在'
      break
    case 500:
      errorMessage = '服务器内部错误'
      break
    case 502:
      errorMessage = '网关错误'
      break
    case 503:
      errorMessage = '服务不可用'
      break
    case 504:
      errorMessage = '网关超时'
      break
    default:
      errorMessage = `服务器错误 (${response.statusCode})`
  }

  // 如果响应中有错误信息，优先使用
  if (response.data && response.data.message) {
    errorMessage = response.data.message
  }

  if (requestConfig.showError !== false) {
    showError(errorMessage)
  }

  return Promise.reject({
    code: response.statusCode,
    message: errorMessage,
    data: response.data,
    originalResponse: response
  })
}

/**
 * 核心请求方法
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
async function request(options: RequestConfig = {}): Promise<any> {
  // 合并配置
  let requestConfig: RequestConfig = {
    ...config,
    ...options,
    headers: {
      ...config.headers,
      ...options.headers
    }
  }

  // 处理URL
  if (requestConfig.url && !requestConfig.url.startsWith('http')) {
    requestConfig.url = requestConfig.baseURL + requestConfig.url
  }

  try {
    // 执行请求拦截器
    requestConfig = await executeRequestInterceptors(requestConfig)

    // 显示loading
    if (requestConfig.showLoading !== false) {
      showLoading(requestConfig.loadingText)
    }

    // 发起请求
    const response: UniResponse = await new Promise((resolve, reject) => {
      uni.request({
        url: requestConfig.url!,
        method: requestConfig.method || 'GET',
        data: requestConfig.data,
        header: requestConfig.headers,
        timeout: requestConfig.timeout,
        success: resolve,
        fail: reject
      })
    })

    // 隐藏loading
    if (requestConfig.showLoading !== false) {
      hideLoading()
    }

    // 检查响应状态
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // 执行响应拦截器
      const processedResponse = await executeResponseInterceptors(response)

      // 检查业务状态码
      if (processedResponse.data && processedResponse.data.code !== undefined) {
        if (processedResponse.data.code === 0 || processedResponse.data.code === 200) {
          return processedResponse.data
        } else {
          // 业务错误
          const errorMessage = processedResponse.data.message || '业务处理失败'
          if (requestConfig.showError !== false) {
            showError(errorMessage)
          }
          return Promise.reject({
            code: processedResponse.data.code,
            message: errorMessage,
            data: processedResponse.data.data,
            originalResponse: processedResponse
          })
        }
      }

      return processedResponse.data
    } else {
      return handleResponseError(response, requestConfig)
    }
  } catch (error) {
    // 隐藏loading
    if (requestConfig.showLoading !== false) {
      hideLoading()
    }

    return handleRequestError(error, requestConfig)
  }
}

// 便捷方法
const http: HttpMethods = {
  get(url: string, params: any = {}, options: RequestConfig = {}) {
    return request({
      url,
      method: 'GET',
      data: params,
      ...options
    })
  },

  post(url: string, data: any = {}, options: RequestConfig = {}) {
    return request({
      url,
      method: 'POST',
      data,
      ...options
    })
  },

  put(url: string, data: any = {}, options: RequestConfig = {}) {
    return request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  },

  delete(url: string, params: any = {}, options: RequestConfig = {}) {
    return request({
      url,
      method: 'DELETE',
      data: params,
      ...options
    })
  },

}

/**
 * 文件上传
 * @param {Object} options 上传选项
 * @returns {Promise} 上传Promise
 */
function upload(options: RequestConfig = {}): Promise<any> {
  const uploadConfig: RequestConfig = {
    ...config,
    ...options,
    headers: {
      ...config.headers,
      ...options.headers
    }
  }

  // 处理URL
  if (uploadConfig.url && !uploadConfig.url.startsWith('http')) {
    uploadConfig.url = uploadConfig.baseURL + uploadConfig.url
  }

  return new Promise((resolve, reject) => {
    // 显示loading
    if (uploadConfig.showLoading !== false) {
      showLoading('上传中...')
    }

    const uploadTask = uni.uploadFile({
      url: uploadConfig.url!,
      filePath: uploadConfig.filePath!,
      name: uploadConfig.name || 'file',
      formData: uploadConfig.formData,
      header: uploadConfig.headers,
      success: (response: any) => {
        // 隐藏loading
        if (uploadConfig.showLoading !== false) {
          hideLoading()
        }

        try {
          const data = JSON.parse(response.data)
          if (data.code === 0 || data.code === 200) {
            resolve(data)
          } else {
            const errorMessage = data.message || '上传失败'
            if (uploadConfig.showError !== false) {
              showError(errorMessage)
            }
            reject({
              code: data.code,
              message: errorMessage,
              data: data.data
            })
          }
        } catch (error) {
          if (uploadConfig.showError !== false) {
            showError('上传响应解析失败')
          }
          reject({
            code: -1,
            message: '上传响应解析失败',
            data: null
          })
        }
      },
      fail: (error: any) => {
        // 隐藏loading
        if (uploadConfig.showLoading !== false) {
          hideLoading()
        }

        const errorMessage = '上传失败，请检查网络连接'
        if (uploadConfig.showError !== false) {
          showError(errorMessage)
        }
        reject({
          code: -1,
          message: errorMessage,
          data: null,
          originalError: error
        })
      }
    })

    // 监听上传进度
    if (uploadConfig.onProgress) {
      uploadTask.onProgressUpdate(uploadConfig.onProgress)
    }
  })
}

/**
 * 文件下载
 * @param {Object} options 下载选项
 * @returns {Promise} 下载Promise
 */
function download(options: RequestConfig = {}): Promise<any> {
  const downloadConfig: RequestConfig = {
    ...config,
    ...options,
    headers: {
      ...config.headers,
      ...options.headers
    }
  }

  // 处理URL
  if (downloadConfig.url && !downloadConfig.url.startsWith('http')) {
    downloadConfig.url = downloadConfig.baseURL + downloadConfig.url
  }

  return new Promise((resolve, reject) => {
    // 显示loading
    if (downloadConfig.showLoading !== false) {
      showLoading('下载中...')
    }

    const downloadTask = uni.downloadFile({
      url: downloadConfig.url!,
      header: downloadConfig.headers,
      success: (response: any) => {
        // 隐藏loading
        if (downloadConfig.showLoading !== false) {
          hideLoading()
        }

        if (response.statusCode === 200) {
          resolve(response)
        } else {
          const errorMessage = '下载失败'
          if (downloadConfig.showError !== false) {
            showError(errorMessage)
          }
          reject({
            code: response.statusCode,
            message: errorMessage,
            data: null
          })
        }
      },
      fail: (error: any) => {
        // 隐藏loading
        if (downloadConfig.showLoading !== false) {
          hideLoading()
        }

        const errorMessage = '下载失败，请检查网络连接'
        if (downloadConfig.showError !== false) {
          showError(errorMessage)
        }
        reject({
          code: -1,
          message: errorMessage,
          data: null,
          originalError: error
        })
      }
    })

    // 监听下载进度
    if (downloadConfig.onProgress) {
      downloadTask.onProgressUpdate(downloadConfig.onProgress)
    }
  })
}

// 导出
export default {
  request,
  get: http.get,
  post: http.post,
  put: http.put,
  delete: http.delete,
  upload,
  download,
  addRequestInterceptor,
  addResponseInterceptor,
  config
}

// 也可以单独导出各个方法
export { request, upload, download, addRequestInterceptor, addResponseInterceptor, config }

export { http }