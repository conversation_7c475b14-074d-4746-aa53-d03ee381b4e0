<template>
  <PageContainer :title="isEdit ? '编辑用户' : '新增用户'" :show-back="true">
    <view class="user-form">
      <form @submit="handleSubmit">
        <!-- 基本信息 -->
        <Card title="基本信息" class="form-section">
          <view class="avatar-section">
            <view class="avatar-upload">
              <image
                v-if="formData.avatar"
                :src="formData.avatar"
                mode="aspectFill"
                @click="previewAvatar"
              />
              <view v-else class="avatar-placeholder" @click="chooseAvatar">
                <text class="upload-icon">📷</text>
                <text class="upload-text">上传头像</text>
              </view>

              <view v-if="formData.avatar" class="avatar-actions">
                <Button type="text" size="small" @click="chooseAvatar">更换</Button>
                <Button type="text" size="small" @click="removeAvatar">删除</Button>
              </view>
            </view>
          </view>

          <FormItem label="姓名" required>
            <Input
              v-model="formData.name"
              placeholder="请输入姓名"
              :error="errors.name"
              @blur="validateField('name')"
            />
          </FormItem>

          <FormItem label="工号">
            <Input
              v-model="formData.employeeId"
              placeholder="请输入工号"
              :error="errors.employeeId"
              @blur="validateField('employeeId')"
            />
          </FormItem>

          <FormItem label="手机号码" required>
            <Input
              v-model="formData.phone"
              placeholder="请输入手机号码"
              type="number"
              :error="errors.phone"
              @blur="validateField('phone')"
            />
          </FormItem>

          <FormItem label="邮箱地址">
            <Input
              v-model="formData.email"
              placeholder="请输入邮箱地址"
              type="email"
              :error="errors.email"
              @blur="validateField('email')"
            />
          </FormItem>
        </Card>

        <!-- 职位信息 -->
        <Card title="职位信息" class="form-section">
          <FormItem label="用户角色" required>
            <Picker
              v-model="formData.role"
              :options="roleOptions"
              placeholder="请选择用户角色"
              :error="errors.role"
              @change="handleRoleChange"
            />
          </FormItem>

          <FormItem label="所属部门">
            <Picker
              v-model="formData.department"
              :options="departmentOptions"
              placeholder="请选择所属部门"
              :error="errors.department"
            />
          </FormItem>

          <FormItem label="职位">
            <Input v-model="formData.position" placeholder="请输入职位" :error="errors.position" />
          </FormItem>

          <FormItem label="用户状态">
            <Picker
              v-model="formData.status"
              :options="statusOptions"
              placeholder="请选择用户状态"
              :error="errors.status"
            />
          </FormItem>
        </Card>

        <!-- 权限设置 -->
        <Card title="权限设置" class="form-section">
          <FormItem label="数据权限">
            <Picker
              v-model="formData.dataScope"
              :options="dataScopeOptions"
              placeholder="请选择数据权限范围"
              :error="errors.dataScope"
            />
          </FormItem>

          <FormItem label="特殊权限">
            <view class="permission-checkboxes">
              <view
                v-for="permission in permissionOptions"
                :key="permission.value"
                class="permission-item"
                @click="togglePermission(permission.value)"
              >
                <view
                  class="checkbox"
                  :class="{ checked: formData.permissions.includes(permission.value) }"
                >
                  <text v-if="formData.permissions.includes(permission.value)">✓</text>
                </view>
                <text class="permission-label">{{ permission.label }}</text>
              </view>
            </view>
          </FormItem>
        </Card>

        <!-- 登录设置 -->
        <Card v-if="!isEdit" title="登录设置" class="form-section">
          <FormItem label="初始密码">
            <Input
              v-model="formData.password"
              placeholder="留空则系统自动生成"
              type="password"
              :error="errors.password"
            />
          </FormItem>

          <FormItem label="密码发送方式">
            <view class="password-send-options">
              <view
                v-for="option in passwordSendOptions"
                :key="option.value"
                class="send-option"
                :class="{ active: formData.passwordSendMethod === option.value }"
                @click="formData.passwordSendMethod = option.value"
              >
                <text>{{ option.label }}</text>
              </view>
            </view>
          </FormItem>

          <FormItem>
            <view class="checkbox-item" @click="toggleForcePasswordChange">
              <view class="checkbox" :class="{ checked: formData.forcePasswordChange }">
                <text v-if="formData.forcePasswordChange">✓</text>
              </view>
              <text class="checkbox-label">首次登录强制修改密码</text>
            </view>
          </FormItem>
        </Card>

        <!-- 备注信息 -->
        <Card title="备注信息" class="form-section">
          <FormItem label="备注">
            <Input
              v-model="formData.remark"
              placeholder="请输入备注信息"
              type="textarea"
              :rows="3"
            />
          </FormItem>
        </Card>
      </form>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <Button type="default" :disabled="submitting" @click="handleCancel"> 取消 </Button>

      <Button type="primary" :loading="submitting" @click="handleSubmit">
        {{ isEdit ? '保存' : '创建' }}
      </Button>
    </view>
  </PageContainer>
</template>

<script>
import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import FormItem from '@/components/Form/FormItem.vue'
import Input from '@/components/Form/Input.vue'
import Picker from '@/components/Form/Picker.vue'
import Button from '@/components/Common/Button.vue'

export default {
  name: 'UserCreate',

  components: {
    PageContainer,
    Card,
    FormItem,
    Input,
    Picker,
    Button
  },
  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const elderlyStore = useElderlyStore()
    const scaleStore = useScaleStore()
    const configStore = useConfigStore()

    return {
      userStore,
      assessmentStore,
      elderlyStore,
      scaleStore,
      configStore
    }
  },

  data() {
    return {
      isEdit: false,
      userId: '',
      submitting: false,

      formData: {
        avatar: '',
        name: '',
        employeeId: '',
        phone: '',
        email: '',
        role: '',
        department: '',
        position: '',
        status: 'active',
        dataScope: 'department',
        permissions: [],
        password: '',
        passwordSendMethod: 'sms',
        forcePasswordChange: true,
        remark: ''
      },

      errors: {},

      roleOptions: [
        { label: '系统管理员', value: 'admin' },
        { label: '评估师', value: 'assessor' },
        { label: '护理员', value: 'caregiver' },
        { label: '医生', value: 'doctor' },
        { label: '普通用户', value: 'user' }
      ],

      departmentOptions: [
        { label: '管理部', value: 'management' },
        { label: '医疗部', value: 'medical' },
        { label: '护理部', value: 'nursing' },
        { label: '康复部', value: 'rehabilitation' },
        { label: '营养部', value: 'nutrition' },
        { label: '社工部', value: 'social_work' }
      ],

      statusOptions: [
        { label: '正常', value: 'active' },
        { label: '禁用', value: 'disabled' },
        { label: '待激活', value: 'pending' }
      ],

      dataScopeOptions: [
        { label: '全部数据', value: 'all' },
        { label: '本部门数据', value: 'department' },
        { label: '个人数据', value: 'self' }
      ],

      permissionOptions: [
        { label: '用户管理', value: 'user_manage' },
        { label: '量表管理', value: 'scale_manage' },
        { label: '数据导出', value: 'data_export' },
        { label: '系统配置', value: 'system_config' }
      ],

      passwordSendOptions: [
        { label: '短信发送', value: 'sms' },
        { label: '邮件发送', value: 'email' },
        { label: '不发送', value: 'none' }
      ]
    }
  },

  onLoad(options) {
    if (options.id) {
      this.isEdit = true
      this.userId = options.id
      this.loadUserData()
    }
  },

  methods: {async loadUserData() {
      try {
        const userDetail = await this.userStore.getUserDetail(this.userId)

        // 填充表单数据
        this.formData = {
          ...this.formData,
          ...userDetail,
          permissions: userDetail.permissions || []
        }
      } catch (error) {
        console.error('加载用户数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },

    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          const tempFilePath = res.tempFilePaths[0]
          this.uploadAvatar(tempFilePath)
        }
      })
    },

    async uploadAvatar(filePath) {
      try {
        uni.showLoading({ title: '上传中...' })

        // 这里应该调用实际的上传接口
        // const uploadResult = await this.uploadFile(filePath)
        // this.formData.avatar = uploadResult.url

        // 临时使用本地路径
        this.formData.avatar = filePath

        uni.hideLoading()
        uni.showToast({
          title: '上传成功',
          icon: 'success'
        })
      } catch (error) {
        uni.hideLoading()
        console.error('上传头像失败:', error)
        uni.showToast({
          title: '上传失败',
          icon: 'error'
        })
      }
    },

    previewAvatar() {
      if (this.formData.avatar) {
        uni.previewImage({
          urls: [this.formData.avatar],
          current: this.formData.avatar
        })
      }
    },

    removeAvatar() {
      this.formData.avatar = ''
    },

    handleRoleChange(role) {
      // 根据角色自动设置权限
      if (role === 'admin') {
        this.formData.permissions = [...this.permissionOptions.map(p => p.value)]
        this.formData.dataScope = 'all'
      } else if (role === 'assessor') {
        this.formData.permissions = ['data_export']
        this.formData.dataScope = 'department'
      } else {
        this.formData.permissions = []
        this.formData.dataScope = 'self'
      }
    },

    togglePermission(permission) {
      const index = this.formData.permissions.indexOf(permission)
      if (index > -1) {
        this.formData.permissions.splice(index, 1)
      } else {
        this.formData.permissions.push(permission)
      }
    },

    toggleForcePasswordChange() {
      this.formData.forcePasswordChange = !this.formData.forcePasswordChange
    },

    validateField(field) {
      const value = this.formData[field]
      let error = ''

      switch (field) {
        case 'name':
          if (!value || value.trim() === '') {
            error = '请输入姓名'
          } else if (value.length < 2) {
            error = '姓名至少2个字符'
          }
          break

        case 'phone':
          if (!value || value.trim() === '') {
            error = '请输入手机号码'
          } else if (!/^1[3-9]\d{9}$/.test(value)) {
            error = '请输入正确的手机号码'
          }
          break

        case 'email':
          if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            error = '请输入正确的邮箱地址'
          }
          break

        case 'employeeId':
          if (value && value.length < 3) {
            error = '工号至少3个字符'
          }
          break

        case 'role':
          if (!value) {
            error = '请选择用户角色'
          }
          break

        case 'password':
          if (value && value.length < 6) {
            error = '密码至少6个字符'
          }
          break
      }

      if (error) {
        this.$set(this.errors, field, error)
      } else {
        this.$delete(this.errors, field)
      }

      return !error
    },

    validateForm() {
      const requiredFields = ['name', 'phone', 'role']
      let isValid = true

      // 清空之前的错误
      this.errors = {}

      // 验证必填字段
      requiredFields
        .forEach(field => {
          if (!this.validateField(field)) {
            isValid = false
          }
        })

        [
          // 验证可选字段
          ('email', 'employeeId', 'password')
        ].forEach(field => {
          if (this.formData[field]) {
            if (!this.validateField(field)) {
              isValid = false
            }
          }
        })

      return isValid
    },

    async handleSubmit() {
      if (!this.validateForm()) {
        uni.showToast({
          title: '请检查表单信息',
          icon: 'error'
        })
        return
      }

      try {
        this.submitting = true

        const submitData = { ...this.formData }

        // 如果是编辑模式，移除密码相关字段
        if (this.isEdit) {
          delete submitData.password
          delete submitData.passwordSendMethod
          delete submitData.forcePasswordChange
        }

        if (this.isEdit) {
          await this.userStore.updateUser({ id: this.userId, ...submitData })
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        } else {
          await this.userStore.createUser(submitData)
          uni.showToast({
            title: '创建成功',
            icon: 'success'
          })
        }

        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        console.error('提交失败:', error)
        uni.showToast({
          title: this.isEdit ? '保存失败' : '创建失败',
          icon: 'error'
        })
      } finally {
        this.submitting = false
      }
    },

    handleCancel() {
      // 检查是否有未保存的更改
      uni.showModal({
        title: '确认离开',
        content: '确定要离开吗？未保存的更改将丢失。',
        success: res => {
          if (res.confirm) {
            uni.navigateBack()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.user-form {
  padding: $spacing-md;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.form-section {
  margin-bottom: $spacing-md;
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: $spacing-lg;

  .avatar-upload {
    position: relative;

    image {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      border: 2px solid $border-color-light;
    }

    .avatar-placeholder {
      width: 100px;
      height: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: $bg-color-light;
      border: 2px dashed $border-color-light;
      border-radius: 50%;
      cursor: pointer;

      .upload-icon {
        font-size: $font-size-xl;
        margin-bottom: $spacing-xs;
      }

      .upload-text {
        font-size: $font-size-xs;
        color: $text-color-secondary;
      }
    }

    .avatar-actions {
      position: absolute;
      bottom: -30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: $spacing-xs;
      white-space: nowrap;
    }
  }
}

.permission-checkboxes {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  .permission-item {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    cursor: pointer;

    .checkbox {
      width: 20px;
      height: 20px;
      border: 2px solid $border-color-base;
      border-radius: $border-radius-xs;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $bg-color-white;

      &.checked {
        background-color: $color-primary;
        border-color: $color-primary;
        color: white;
      }

      text {
        font-size: $font-size-xs;
        font-weight: 600;
      }
    }

    .permission-label {
      font-size: $font-size-base;
      color: $text-color-primary;
    }
  }
}

.password-send-options {
  display: flex;
  gap: $spacing-sm;

  .send-option {
    flex: 1;
    padding: $spacing-sm;
    text-align: center;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-sm;
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background-color: $color-primary;
      border-color: $color-primary;
      color: white;
    }

    text {
      font-size: $font-size-sm;
    }
  }
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  cursor: pointer;

  .checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid $border-color-base;
    border-radius: $border-radius-xs;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-color-white;

    &.checked {
      background-color: $color-primary;
      border-color: $color-primary;
      color: white;
    }

    text {
      font-size: $font-size-xs;
      font-weight: 600;
    }
  }

  .checkbox-label {
    font-size: $font-size-base;
    color: $text-color-primary;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;
}

// 响应式设计
@media (max-width: 768px) {
  .password-send-options {
    flex-direction: column;
  }

  .bottom-actions {
    flex-direction: column;
  }
}
</style>
