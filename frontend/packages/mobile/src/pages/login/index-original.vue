<template>
  <view class="login-container">
    <!-- 头部logo区域 -->
    <view class="login-header">
      <view class="logo">
        <text class="logo-icon">🏥</text>
        <text class="logo-text">智能评估平台</text>
      </view>
      <text class="subtitle">多租户登录系统</text>
    </view>

    <!-- 登录表单 -->
    <form class="login-form" @submit.prevent="handleLogin">
      <!-- 租户代码输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">机构代码</text>
          <text class="required">*</text>
        </view>
        <u-input
          v-model="loginForm.tenantCode"
          placeholder="请输入您的机构代码"
          maxlength="50"
          :prefix-icon="prefixIcon.building"
          :custom-style="{
            background: '#f8f9fa',
            borderRadius: '16rpx',
            height: '88rpx'
          }"
          @input="clearError('tenantCode')"
        />
        <text v-if="errors.tenantCode" class="error-text">{{ errors.tenantCode }}</text>
        <text class="hint-text">示例：demo_hospital</text>
      </view>

      <!-- 用户名输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">用户名</text>
          <text class="required">*</text>
        </view>
        <u-input
          v-model="loginForm.username"
          placeholder="请输入用户名"
          maxlength="50"
          :prefix-icon="prefixIcon.user"
          :custom-style="{
            background: '#f8f9fa',
            borderRadius: '16rpx',
            height: '88rpx'
          }"
          @input="clearError('username')"
        />
        <text v-if="errors.username" class="error-text">{{ errors.username }}</text>
      </view>

      <!-- 密码输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">密码</text>
          <text class="required">*</text>
        </view>
        <u-input
          v-model="loginForm.password"
          placeholder="请输入密码"
          maxlength="50"
          :type="showPassword ? 'text' : 'password'"
          :prefix-icon="prefixIcon.lock"
          :suffix-icon="showPassword ? prefixIcon.eyeOpen : prefixIcon.eyeClosed"
          :custom-style="{
            background: '#f8f9fa',
            borderRadius: '16rpx',
            height: '88rpx'
          }"
          @input="clearError('password')"
          @click-suffix="togglePassword"
        />
        <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
      </view>

      <!-- 滑动验证码 -->
      <view class="form-item">
        <SlideCaptcha
          v-if="captchaToken"
          :token="captchaToken"
          @success="handleCaptchaSuccess"
          @fail="handleCaptchaFail"
          @error="handleCaptchaError"
        />
      </view>

      <!-- 记住我选项 -->
      <view class="form-options">
        <u-checkbox
          v-model="loginForm.rememberMe"
          active-color="#667eea"
          label="记住我"
          :custom-style="{
            fontSize: '28rpx',
            color: '#666666'
          }"
        />
      </view>

      <!-- 登录按钮 -->
      <view class="form-actions">
        <!-- 使用uView按钮组件 -->
        <u-button
          type="primary"
          :loading="loading"
          :disabled="loading || !captchaVerified"
          :custom-style="{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '16rpx',
            height: '88rpx',
            fontSize: '32rpx'
          }"
          @click="handleLogin"
        >
          <text v-if="loading">登录中...</text>
          <text v-else-if="!captchaVerified">请先完成验证码</text>
          <text v-else>登录</text>
        </u-button>
      </view>
    </form>

    <!-- 演示账户信息 -->
    <view class="demo-section">
      <view class="demo-header" @click="toggleDemoInfo">
        <text class="demo-title">💡 演示账户</text>
        <text class="demo-toggle">{{ showDemo ? '收起' : '展开' }}</text>
      </view>

      <view v-if="showDemo" class="demo-content">
        <view class="demo-item" @click="fillDemoAccount('hospital')">
          <text class="demo-type">🏥 医院机构</text>
          <view class="demo-details">
            <text class="demo-text">机构代码: demo_hospital</text>
            <text class="demo-text">用户名: demo_hospital_admin</text>
            <text class="demo-text">密码: password123</text>
          </view>
        </view>

        <view class="demo-item" @click="fillDemoAccount('nursing')">
          <text class="demo-type">🏠 护理机构</text>
          <view class="demo-details">
            <text class="demo-text">机构代码: demo_nursing</text>
            <text class="demo-text">用户名: demo_nursing_admin</text>
            <text class="demo-text">密码: password123</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">智能评估平台 v2.0.0</text>
      <text class="copyright">支持多租户架构</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { login } from '@/api/auth'
import { getCaptcha, checkCaptcha } from '@/api/captcha'
import { useUserStore } from '@/store'
import SlideCaptcha from '@/components/SlideCaptcha.vue'

// 使用 Pinia store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const showDemo = ref(false)

// uView图标配置
const prefixIcon = {
  building: 'home',
  user: 'account',
  lock: 'lock',
  eyeOpen: 'eye',
  eyeClosed: 'eye-off'
}

const loginForm = reactive({
  tenantCode: 'demo_hospital',
  username: 'demo_hospital_admin',
  password: 'password123',
  rememberMe: false
})

const errors = reactive<Record<string, string>>({})

// 验证码相关
const captchaToken = ref('')
const captchaVerification = ref('')
const captchaVerified = ref(false)

// 生命周期
onMounted(() => {
  // 检查是否已经登录
  const token = uni.getStorageSync('token')
  if (token) {
    redirectToMain()
  }

  // 自动填充上次登录信息
  loadRememberedInfo()
  
  // 获取验证码
  getCaptchaToken()
})

// 方法
const validateForm = (): boolean => {
  Object.keys(errors).forEach(key => delete errors[key])

  if (!loginForm.tenantCode.trim()) {
    errors.tenantCode = '请输入机构代码'
  }

  if (!loginForm.username.trim()) {
    errors.username = '请输入用户名'
  }

  if (!loginForm.password.trim()) {
    errors.password = '请输入密码'
  }

  return Object.keys(errors).length === 0
}

const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  if (!captchaVerified.value) {
    uni.showToast({
      title: '请先完成验证码',
      icon: 'none'
    })
    return
  }

  loading.value = true

  try {
    const response = await login({
      ...loginForm,
      captchaToken: captchaToken.value,
      captchaVerification: captchaVerification.value
    })

    if (response.code === 200) {
      const { token, userInfo } = response.data

      // 使用 Pinia store 保存登录信息
      userStore.$patch({
        token,
        userInfo
      })
      
      // 手动设置存储
      uni.setStorageSync('token', token)
      uni.setStorageSync('userInfo', userInfo)

      // 记住登录信息
      if (loginForm.rememberMe) {
        saveRememberedInfo()
      }

      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })

      setTimeout(() => {
        redirectToMain()
      }, 1500)
    } else {
      throw new Error(response.message || '登录失败')
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none',
      duration: 2000
    })

    // 登录失败后刷新验证码
    getCaptchaToken()
    captchaVerified.value = false
  } finally {
    loading.value = false
  }
}

const getCaptchaToken = async () => {
  try {
    const response = await getCaptcha()
    if ((response as any).code === 200) {
      captchaToken.value = (response as any).data.token
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

const handleCaptchaSuccess = (verification: string) => {
  captchaVerification.value = verification
  captchaVerified.value = true
  uni.showToast({
    title: '验证成功',
    icon: 'success',
    duration: 1000
  })
}

const handleCaptchaFail = () => {
  captchaVerified.value = false
  uni.showToast({
    title: '验证失败，请重试',
    icon: 'none'
  })
}

const handleCaptchaError = (error: any) => {
  console.error('验证码错误:', error)
  getCaptchaToken()
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleDemoInfo = () => {
  showDemo.value = !showDemo.value
}

const fillDemoAccount = (type: 'hospital' | 'nursing') => {
  if (type === 'hospital') {
    loginForm.tenantCode = 'demo_hospital'
    loginForm.username = 'demo_hospital_admin'
    loginForm.password = 'password123'
  } else if (type === 'nursing') {
    loginForm.tenantCode = 'demo_nursing'
    loginForm.username = 'demo_nursing_admin'
    loginForm.password = 'password123'
  }
}

const clearError = (field: string) => {
  delete errors[field]
}

const saveRememberedInfo = () => {
  uni.setStorageSync('remembered_tenant', loginForm.tenantCode)
  uni.setStorageSync('remembered_username', loginForm.username)
}

const loadRememberedInfo = () => {
  const rememberedTenant = uni.getStorageSync('remembered_tenant')
  const rememberedUsername = uni.getStorageSync('remembered_username')

  if (rememberedTenant) {
    loginForm.tenantCode = rememberedTenant
  }
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
  }
}

const redirectToMain = () => {
  uni.reLaunch({
    url: '/pages/home/<USER>'
  })
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 40rpx;
  box-sizing: border-box;
}

/* 头部区域 */
.login-header {
  text-align: center;
  margin-bottom: 80rpx;
  animation: fadeInDown 0.8s ease-out;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.logo-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.logo-text {
  font-size: 48rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 2rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 10rpx;
}

/* 登录表单 */
.login-form {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.required {
  color: #ff4757;
  margin-left: 8rpx;
  font-size: 28rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  background: #ffffff;
}

.input-icon {
  width: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #666666;
}

.input-field {
  flex: 1;
  height: 88rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
}

.input-field::placeholder {
  color: #999999;
}

.toggle-password {
  width: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #666666;
  cursor: pointer;
  transition: color 0.3s ease;
}

.toggle-password:active {
  color: #667eea;
}

.error-text {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
  display: block;
}

.hint-text {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
  display: block;
}

/* 验证码区域 */
.form-item:has(.slide-captcha) {
  margin-bottom: 30rpx;
}

/* 表单选项 */
.form-options {
  margin-bottom: 40rpx;
}

.remember-me {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.remember-text {
  font-size: 28rpx;
  color: #666666;
  margin-left: 16rpx;
}

/* 登录按钮 */
.form-actions {
  margin-bottom: 20rpx;
}

.login-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}

.login-button:not([disabled]):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.login-button[disabled] {
  background: #cccccc;
  color: #666666;
  box-shadow: none;
  cursor: not-allowed;
}

/* 演示区域 */
.demo-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  margin: 40rpx 0;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background: rgba(102, 126, 234, 0.1);
  cursor: pointer;
  transition: background 0.3s ease;
}

.demo-header:active {
  background: rgba(102, 126, 234, 0.15);
}

.demo-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.demo-toggle {
  font-size: 26rpx;
  color: #667eea;
}

.demo-content {
  padding: 0 40rpx 30rpx;
}

.demo-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.demo-item:last-child {
  margin-bottom: 0;
}

.demo-item:active {
  background: #e9ecef;
  border-color: #667eea;
  transform: scale(0.98);
}

.demo-type {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.demo-details {
  display: flex;
  flex-direction: column;
}

.demo-text {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
  font-family: 'Courier New', monospace;
}

.demo-text:last-child {
  margin-bottom: 0;
}

/* 版本信息 */
.version-info {
  text-align: center;
  margin-top: 40rpx;
  animation: fadeIn 0.8s ease-out 0.6s both;
}

.version-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
  display: block;
}

.copyright {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 动画效果 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .login-container {
    padding: 40rpx 30rpx 30rpx;
  }
  
  .login-form {
    padding: 40rpx 30rpx;
  }
  
  .logo-icon {
    font-size: 100rpx;
  }
  
  .logo-text {
    font-size: 42rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-form {
    background: rgba(30, 30, 30, 0.95);
  }
  
  .label-text {
    color: #ffffff;
  }
  
  .input-wrapper {
    background: #2c2c2c;
    border-color: #404040;
  }
  
  .input-wrapper:focus-within {
    background: #1a1a1a;
  }
  
  .input-field {
    color: #ffffff;
  }
  
  .demo-section {
    background: rgba(30, 30, 30, 0.9);
  }
  
  .demo-item {
    background: #2c2c2c;
  }
  
  .demo-item:active {
    background: #404040;
  }
  
  .demo-title,
  .demo-type {
    color: #ffffff;
  }
  
  .demo-text {
    color: #cccccc;
  }
}
</style>