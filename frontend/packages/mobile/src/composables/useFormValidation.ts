import { reactive, ref } from 'vue'

// 验证规则类型定义
export interface ValidationRule {
  (value: any): string | null
}

export interface ValidationRules {
  [field: string]: ValidationRule[]
}

// 常用验证规则工厂函数
export const createValidationRules = {
  // 必填验证
  required: (message: string = '此字段为必填项'): ValidationRule => {
    return (value: any) => {
      if (value === null || value === undefined || value === '' || 
          (Array.isArray(value) && value.length === 0)) {
        return message
      }
      return null
    }
  },

  // 最小长度验证
  minLength: (min: number, message?: string): ValidationRule => {
    return (value: string) => {
      if (value && value.length < min) {
        return message || `最少需要${min}个字符`
      }
      return null
    }
  },

  // 最大长度验证
  maxLength: (max: number, message?: string): ValidationRule => {
    return (value: string) => {
      if (value && value.length > max) {
        return message || `最多允许${max}个字符`
      }
      return null
    }
  },

  // 手机号验证
  mobile: (message: string = '请输入正确的手机号码'): ValidationRule => {
    return (value: string) => {
      if (value && !/^1[3-9]\d{9}$/.test(value)) {
        return message
      }
      return null
    }
  },

  // 邮箱验证
  email: (message: string = '请输入正确的邮箱地址'): ValidationRule => {
    return (value: string) => {
      if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return message
      }
      return null
    }
  },

  // 身份证验证
  idCard: (message: string = '请输入正确的身份证号码'): ValidationRule => {
    return (value: string) => {
      if (value && !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
        return message
      }
      return null
    }
  },

  // 数字验证
  number: (message: string = '请输入数字'): ValidationRule => {
    return (value: string) => {
      if (value && !/^\d+(\.\d+)?$/.test(value)) {
        return message
      }
      return null
    }
  },

  // 正整数验证
  positiveInteger: (message: string = '请输入正整数'): ValidationRule => {
    return (value: string) => {
      if (value && !/^[1-9]\d*$/.test(value)) {
        return message
      }
      return null
    }
  },

  // 年龄验证
  age: (min: number = 0, max: number = 150, message?: string): ValidationRule => {
    return (value: string | number) => {
      const age = typeof value === 'string' ? parseInt(value) : value
      if (value && (isNaN(age) || age < min || age > max)) {
        return message || `年龄应在${min}-${max}岁之间`
      }
      return null
    }
  },

  // 自定义正则验证
  pattern: (regex: RegExp, message: string): ValidationRule => {
    return (value: string) => {
      if (value && !regex.test(value)) {
        return message
      }
      return null
    }
  }
}

/**
 * 表单验证组合式函数
 * @param validationRules 验证规则配置
 * @returns 验证相关的响应式数据和方法
 */
export function useFormValidation(validationRules: ValidationRules) {
  // 错误信息存储
  const errors = reactive<Record<string, string>>({})
  
  // 验证状态
  const isValidating = ref(false)

  /**
   * 验证单个字段
   * @param field 字段名
   * @param value 字段值
   * @returns 是否验证通过
   */
  const validateField = (field: string, value: any): boolean => {
    const rules = validationRules[field]
    if (!rules) return true

    // 清除之前的错误
    delete errors[field]

    // 逐一验证规则
    for (const rule of rules) {
      const error = rule(value)
      if (error) {
        errors[field] = error
        return false
      }
    }

    return true
  }

  /**
   * 验证整个表单
   * @param formData 表单数据
   * @returns 是否验证通过
   */
  const validateForm = async (formData: Record<string, any>): Promise<boolean> => {
    isValidating.value = true
    
    try {
      // 清空所有错误
      Object.keys(errors).forEach(key => delete errors[key])

      let isValid = true

      // 验证所有有规则的字段
      for (const field of Object.keys(validationRules)) {
        const fieldValid = validateField(field, formData[field])
        if (!fieldValid) {
          isValid = false
        }
      }

      return isValid
    } finally {
      isValidating.value = false
    }
  }

  /**
   * 清除指定字段的错误
   * @param field 字段名
   */
  const clearFieldError = (field: string) => {
    delete errors[field]
  }

  /**
   * 清除所有错误
   */
  const clearAllErrors = () => {
    Object.keys(errors).forEach(key => delete errors[key])
  }

  /**
   * 获取字段错误信息
   * @param field 字段名
   * @returns 错误信息或null
   */
  const getFieldError = (field: string): string | null => {
    return errors[field] || null
  }

  /**
   * 检查字段是否有错误
   * @param field 字段名
   * @returns 是否有错误
   */
  const hasFieldError = (field: string): boolean => {
    return !!errors[field]
  }

  /**
   * 检查表单是否有错误
   * @returns 是否有错误
   */
  const hasErrors = (): boolean => {
    return Object.keys(errors).length > 0
  }

  /**
   * 获取错误字段数量
   * @returns 错误字段数量
   */
  const getErrorCount = (): number => {
    return Object.keys(errors).length
  }

  return {
    // 响应式数据
    errors,
    isValidating,
    
    // 验证方法
    validateField,
    validateForm,
    
    // 错误管理
    clearFieldError,
    clearAllErrors,
    getFieldError,
    hasFieldError,
    hasErrors,
    getErrorCount
  }
}

// 常用验证规则预设
export const commonValidationRules = {
  // 用户相关
  username: [
    createValidationRules.required('请输入用户名'),
    createValidationRules.minLength(2, '用户名至少2个字符'),
    createValidationRules.maxLength(20, '用户名最多20个字符')
  ],
  
  realName: [
    createValidationRules.required('请输入姓名'),
    createValidationRules.minLength(2, '姓名至少2个字符'),
    createValidationRules.maxLength(10, '姓名最多10个字符')
  ],

  phone: [
    createValidationRules.required('请输入手机号码'),
    createValidationRules.mobile()
  ],

  optionalPhone: [
    createValidationRules.mobile()
  ],

  email: [
    createValidationRules.email()
  ],

  idCard: [
    createValidationRules.idCard()
  ],

  age: [
    createValidationRules.age(0, 150)
  ],

  // 老年人相关
  elderlyName: [
    createValidationRules.required('请输入老年人姓名'),
    createValidationRules.minLength(2, '姓名至少2个字符'),
    createValidationRules.maxLength(10, '姓名最多10个字符')
  ],

  elderlyAge: [
    createValidationRules.required('请输入年龄'),
    createValidationRules.age(60, 120, '老年人年龄应在60-120岁之间')
  ],

  // 评估相关
  assessmentTitle: [
    createValidationRules.required('请输入评估标题'),
    createValidationRules.minLength(2, '标题至少2个字符'),
    createValidationRules.maxLength(50, '标题最多50个字符')
  ],

  // 量表相关
  scaleTitle: [
    createValidationRules.required('请输入量表标题'),
    createValidationRules.minLength(2, '标题至少2个字符'),
    createValidationRules.maxLength(100, '标题最多100个字符')
  ],

  scaleDescription: [
    createValidationRules.maxLength(500, '描述最多500个字符')
  ]
}