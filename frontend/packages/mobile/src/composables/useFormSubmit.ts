import { ref } from 'vue'

// 提交选项配置
export interface SubmitOptions {
  // 成功提示
  successMessage?: {
    create?: string
    update?: string
  }
  // 失败提示
  errorMessage?: {
    create?: string
    update?: string
  }
  // 提交后的行为
  onSuccess?: (data: any, isEdit: boolean) => void | Promise<void>
  onError?: (error: any, isEdit: boolean) => void | Promise<void>
  // 是否自动返回上一页
  autoNavigateBack?: boolean
  // 返回延迟时间(ms)
  navigateBackDelay?: number
}

// Store接口定义
export interface CrudStore {
  create?: (data: any) => Promise<any>
  update?: (data: any) => Promise<any>
  [key: string]: any
}

/**
 * 表单提交组合式函数
 * @param store 数据仓库实例
 * @param entityType 实体类型名称（用于生成方法名）
 * @param options 提交配置选项
 * @returns 提交相关的响应式数据和方法
 */
export function useFormSubmit(
  store: CrudStore,
  entityType: string = '',
  options: SubmitOptions = {}
) {
  // 默认配置
  const defaultOptions: Required<SubmitOptions> = {
    successMessage: {
      create: '创建成功',
      update: '保存成功'
    },
    errorMessage: {
      create: '创建失败',
      update: '保存失败'
    },
    onSuccess: async () => {},
    onError: async () => {},
    autoNavigateBack: true,
    navigateBackDelay: 1500
  }

  const finalOptions = { ...defaultOptions, ...options }

  // 响应式状态
  const submitting = ref(false)
  const submitError = ref<string | null>(null)

  /**
   * 生成方法名
   * @param action 操作类型
   * @returns 方法名
   */
  const getMethodName = (action: 'create' | 'update'): string => {
    if (!entityType) return action
    const capitalizedType = entityType.charAt(0).toUpperCase() + entityType.slice(1)
    return `${action}${capitalizedType}`
  }

  /**
   * 显示成功提示
   * @param isEdit 是否为编辑模式
   */
  const showSuccessToast = (isEdit: boolean) => {
    const message = isEdit 
      ? finalOptions.successMessage.update 
      : finalOptions.successMessage.create

    uni.showToast({
      title: message,
      icon: 'success',
      duration: 1500
    })
  }

  /**
   * 显示错误提示
   * @param error 错误信息
   * @param isEdit 是否为编辑模式
   */
  const showErrorToast = (error: any, isEdit: boolean) => {
    let message = isEdit 
      ? finalOptions.errorMessage.update 
      : finalOptions.errorMessage.create

    // 如果有具体的错误信息，使用具体信息
    if (error?.message) {
      message = error.message
    } else if (typeof error === 'string') {
      message = error
    }

    uni.showToast({
      title: message,
      icon: 'error',
      duration: 2000
    })

    submitError.value = message || '未知错误'
  }

  /**
   * 处理导航返回
   */
  const handleNavigateBack = () => {
    if (finalOptions.autoNavigateBack) {
      setTimeout(() => {
        uni.navigateBack()
      }, finalOptions.navigateBackDelay)
    }
  }

  /**
   * 提交表单数据
   * @param formData 表单数据
   * @param isEdit 是否为编辑模式
   * @param entityId 实体ID（编辑时需要）
   * @returns 提交结果
   */
  const handleSubmit = async (
    formData: Record<string, any>,
    isEdit: boolean = false,
    entityId?: string | number
  ): Promise<any> => {
    if (submitting.value) {
      console.warn('表单正在提交中，请勿重复提交')
      return null
    }

    submitting.value = true
    submitError.value = null

    try {
      let result: any

      if (isEdit) {
        // 编辑模式
        const updateMethod = store[getMethodName('update')] || store.update
        if (!updateMethod) {
          throw new Error(`Store中未找到更新方法: ${getMethodName('update')}`)
        }

        const updateData = entityId 
          ? { id: entityId, ...formData }
          : formData

        result = await updateMethod(updateData)
      } else {
        // 创建模式
        const createMethod = store[getMethodName('create')] || store.create
        if (!createMethod) {
          throw new Error(`Store中未找到创建方法: ${getMethodName('create')}`)
        }

        result = await createMethod(formData)
      }

      // 显示成功提示
      showSuccessToast(isEdit)

      // 执行成功回调
      await finalOptions.onSuccess(result, isEdit)

      // 处理导航返回
      handleNavigateBack()

      return result

    } catch (error: any) {
      console.error('表单提交失败:', error)

      // 显示错误提示
      showErrorToast(error, isEdit)

      // 执行错误回调
      await finalOptions.onError(error, isEdit)

      throw error

    } finally {
      submitting.value = false
    }
  }

  /**
   * 提交新建表单
   * @param formData 表单数据
   * @returns 提交结果
   */
  const handleCreate = async (formData: Record<string, any>): Promise<any> => {
    return handleSubmit(formData, false)
  }

  /**
   * 提交编辑表单
   * @param formData 表单数据
   * @param entityId 实体ID
   * @returns 提交结果
   */
  const handleUpdate = async (
    formData: Record<string, any>,
    entityId: string | number
  ): Promise<any> => {
    return handleSubmit(formData, true, entityId)
  }

  /**
   * 重置提交状态
   */
  const resetSubmitState = () => {
    submitting.value = false
    submitError.value = null
  }

  /**
   * 取消提交（用于处理表单取消操作）
   */
  const handleCancel = () => {
    if (finalOptions.autoNavigateBack) {
      uni.navigateBack()
    }
  }

  return {
    // 响应式状态
    submitting,
    submitError,

    // 提交方法
    handleSubmit,
    handleCreate,
    handleUpdate,

    // 辅助方法
    resetSubmitState,
    handleCancel,
    showSuccessToast,
    showErrorToast
  }
}

// 预设的Store方法名映射
export const storeMethodMappings = {
  user: {
    create: 'createUser',
    update: 'updateUser'
  },
  elderly: {
    create: 'createElderly', 
    update: 'updateElderly'
  },
  assessment: {
    create: 'createAssessment',
    update: 'updateAssessment'
  },
  scale: {
    create: 'createScale',
    update: 'updateScale'
  }
}

/**
 * 创建特定实体类型的提交Composable
 * @param store 数据仓库实例
 * @param entityType 实体类型
 * @param customOptions 自定义选项
 * @returns 提交Composable
 */
export function createEntitySubmit(
  store: CrudStore,
  entityType: keyof typeof storeMethodMappings,
  customOptions: SubmitOptions = {}
) {
  const mapping = storeMethodMappings[entityType]
  
  // 使用映射的方法名
  const enhancedStore = {
    ...store,
    create: store[mapping.create],
    update: store[mapping.update]
  }

  return useFormSubmit(enhancedStore, '', customOptions)
}

// 便捷的实体特定Composable创建器
export const createUserSubmit = (store: CrudStore, options?: SubmitOptions) => 
  createEntitySubmit(store, 'user', options)

export const createElderlySubmit = (store: CrudStore, options?: SubmitOptions) => 
  createEntitySubmit(store, 'elderly', options)

export const createAssessmentSubmit = (store: CrudStore, options?: SubmitOptions) => 
  createEntitySubmit(store, 'assessment', options)

export const createScaleSubmit = (store: CrudStore, options?: SubmitOptions) => 
  createEntitySubmit(store, 'scale', options)