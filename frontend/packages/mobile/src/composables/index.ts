/**
 * Composables导出文件
 * 统一导出所有可复用的组合式函数
 */

// 导入所有composables
import { 
  useFormValidation, 
  createValidationRules, 
  commonValidationRules 
} from './useFormValidation'

import { 
  useFormSubmit,
  createEntitySubmit,
  createUserSubmit,
  createElderlySubmit,
  createAssessmentSubmit,
  createScaleSubmit,
  storeMethodMappings
} from './useFormSubmit'

// 表单验证相关导出
export {
  useFormValidation,
  createValidationRules,
  commonValidationRules
} from './useFormValidation'

// 表单提交相关导出
export {
  useFormSubmit,
  createEntitySubmit,
  createUserSubmit,
  createElderlySubmit,
  createAssessmentSubmit,
  createScaleSubmit,
  storeMethodMappings
} from './useFormSubmit'

// 类型导出
export type {
  ValidationRule,
  ValidationRules
} from './useFormValidation'

export type {
  SubmitOptions,
  CrudStore
} from './useFormSubmit'

// 导出所有Composables的便捷对象
export const Composables = {
  // 表单相关
  useFormValidation,
  useFormSubmit,
  
  // 实体特定的提交函数
  createUserSubmit,
  createElderlySubmit,
  createAssessmentSubmit,
  createScaleSubmit,
  
  // 验证规则
  createValidationRules,
  commonValidationRules
}