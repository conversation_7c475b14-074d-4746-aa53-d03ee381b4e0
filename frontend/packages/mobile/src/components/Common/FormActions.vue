<template>
  <view class="form-actions" :class="{ fixed: fixed, 'with-safe-area': withSafeArea }">
    <!-- 取消按钮 -->
    <Button
      v-if="showCancel"
      :disabled="loading"
      :type="cancelType"
      :size="buttonSize"
      class="cancel-button"
      @click="handleCancel"
    >
      {{ cancelText }}
    </Button>
    
    <!-- 主要操作按钮 -->
    <Button
      :loading="loading"
      :disabled="disabled || loading"
      :type="submitType"
      :size="buttonSize"
      class="submit-button"
      @click="handleSubmit"
    >
      <template v-if="!loading">
        {{ submitText }}
      </template>
      <template v-else>
        {{ loadingText }}
      </template>
    </Button>
    
    <!-- 额外操作按钮插槽 -->
    <slot name="extra" :loading="loading" :disabled="disabled"></slot>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Button from './Button.vue'

// Props定义
export interface FormActionsProps {
  // 加载状态
  loading?: boolean
  // 禁用状态
  disabled?: boolean
  // 是否固定在底部
  fixed?: boolean
  // 是否适配安全区域
  withSafeArea?: boolean
  // 是否显示取消按钮
  showCancel?: boolean
  
  // 按钮文本
  submitText?: string
  cancelText?: string
  loadingText?: string
  
  // 按钮样式
  submitType?: 'primary' | 'success' | 'warning' | 'danger' | 'default'
  cancelType?: 'primary' | 'success' | 'warning' | 'danger' | 'default'
  buttonSize?: 'large' | 'default' | 'small'
  
  // 按钮布局
  layout?: 'horizontal' | 'vertical'
  gap?: string
  
  // 确认操作
  confirmSubmit?: boolean
  confirmMessage?: string
  confirmCancel?: boolean
  cancelConfirmMessage?: string
}

const props = withDefaults(defineProps<FormActionsProps>(), {
  loading: false,
  disabled: false,
  fixed: true,
  withSafeArea: true,
  showCancel: true,
  
  submitText: '确定',
  cancelText: '取消',
  loadingText: '处理中...',
  
  submitType: 'primary',
  cancelType: 'default',
  buttonSize: 'large',
  
  layout: 'horizontal',
  gap: '32rpx',
  
  confirmSubmit: false,
  confirmMessage: '确定要提交吗？',
  confirmCancel: false,
  cancelConfirmMessage: '确定要取消吗？当前输入的内容将丢失。'
})

// Emits定义
const emit = defineEmits<{
  'submit': []
  'cancel': []
  'confirm-submit': []
  'confirm-cancel': []
}>()

// 计算样式
const containerStyle = computed(() => ({
  flexDirection: props.layout === 'vertical' ? 'column' : 'row',
  gap: props.gap
}))

/**
 * 处理提交操作
 */
const handleSubmit = () => {
  if (props.loading || props.disabled) return
  
  if (props.confirmSubmit) {
    uni.showModal({
      title: '确认操作',
      content: props.confirmMessage,
      success: (res) => {
        if (res.confirm) {
          emit('confirm-submit')
          emit('submit')
        }
      }
    })
  } else {
    emit('submit')
  }
}

/**
 * 处理取消操作
 */
const handleCancel = () => {
  if (props.loading) return
  
  if (props.confirmCancel) {
    uni.showModal({
      title: '确认取消',
      content: props.cancelConfirmMessage,
      success: (res) => {
        if (res.confirm) {
          emit('confirm-cancel')
          emit('cancel')
        }
      }
    })
  } else {
    emit('cancel')
  }
}

// 暴露方法
defineExpose({
  handleSubmit,
  handleCancel
})
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;

.form-actions {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  background: $bg-color-white;
  
  &.fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-top: 1rpx solid $border-color-light;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  }
  
  &.with-safe-area {
    padding-bottom: calc(#{$spacing-md} + env(safe-area-inset-bottom));
  }
}

.cancel-button {
  flex: 1;
  margin-right: $spacing-sm;
}

.submit-button {
  flex: 2;
}

// 垂直布局样式
.form-actions[style*="column"] {
  .cancel-button {
    margin-right: 0;
    margin-bottom: $spacing-sm;
  }
  
  .submit-button {
    margin-bottom: 0;
  }
}

// 按钮hover效果（H5端）
// #ifdef H5
.cancel-button:hover,
.submit-button:hover {
  transform: translateY(-2rpx);
  transition: transform 0.2s ease;
}
// #endif

// 响应式调整
@media (max-width: 750rpx) {
  .form-actions {
    padding: $spacing-sm;
    
    &.with-safe-area {
      padding-bottom: calc(#{$spacing-sm} + env(safe-area-inset-bottom));
    }
  }
}

// 主题变量（可根据设计系统调整）
:deep(.submit-button) {
  --button-primary-bg: #{$primary-color};
  --button-primary-border: #{$primary-color};
  --button-primary-color: #ffffff;
  
  &:hover {
    --button-primary-bg: #{darken($primary-color, 5%)};
    --button-primary-border: #{darken($primary-color, 5%)};
  }
  
  &:active {
    --button-primary-bg: #{darken($primary-color, 10%)};
    --button-primary-border: #{darken($primary-color, 10%)};
  }
}

:deep(.cancel-button) {
  --button-default-bg: #{$bg-color-white};
  --button-default-border: #{$border-color-base};
  --button-default-color: #{$text-color-primary};
  
  &:hover {
    --button-default-border: #{$primary-color};
    --button-default-color: #{$primary-color};
  }
}

// 加载状态样式
:deep(.submit-button.is-loading) {
  opacity: 0.8;
  cursor: not-allowed;
}

// 禁用状态样式
:deep(.submit-button.is-disabled) {
  opacity: 0.5;
  cursor: not-allowed;
  
  &:hover {
    --button-primary-bg: #{$primary-color};
    --button-primary-border: #{$primary-color};
  }
}

// 特殊场景样式
.form-actions.compact {
  padding: $spacing-sm $spacing-md;
  
  .cancel-button,
  .submit-button {
    font-size: $font-size-sm;
    padding: $spacing-xs $spacing-sm;
  }
}

// 危险操作样式
.form-actions.danger {
  :deep(.submit-button) {
    --button-primary-bg: #{$error-color};
    --button-primary-border: #{$error-color};
    
    &:hover {
      --button-primary-bg: #{darken($error-color, 5%)};
      --button-primary-border: #{darken($error-color, 5%)};
    }
  }
}

// 成功操作样式
.form-actions.success {
  :deep(.submit-button) {
    --button-primary-bg: #{$success-color};
    --button-primary-border: #{$success-color};
    
    &:hover {
      --button-primary-bg: #{darken($success-color, 5%)};
      --button-primary-border: #{darken($success-color, 5%)};
    }
  }
}
</style>