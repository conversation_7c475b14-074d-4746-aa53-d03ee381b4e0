<template>
  <view class="avatar-uploader">
    <!-- 头像预览区域 -->
    <view class="avatar-preview" @click="handleChooseImage">
      <image 
        v-if="displayValue" 
        :src="displayValue" 
        class="avatar-image"
        mode="aspectFill"
        @error="handleImageError"
      />
      <view v-else class="avatar-placeholder">
        <text class="upload-icon">📷</text>
        <text class="upload-text">{{ placeholder }}</text>
      </view>
      
      <!-- 上传中状态 -->
      <view v-if="uploading" class="upload-overlay">
        <view class="upload-spinner"></view>
        <text class="upload-progress">{{ uploadProgress }}%</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view v-if="displayValue && showActions" class="avatar-actions">
      <view class="action-button" @click="handleChooseImage">
        <text class="action-icon">🔄</text>
        <text class="action-text">更换</text>
      </view>
      <view class="action-button danger" @click="handleRemoveImage">
        <text class="action-icon">🗑️</text>
        <text class="action-text">删除</text>
      </view>
    </view>
    
    <!-- 错误提示 -->
    <view v-if="errorMessage" class="error-message">
      <text class="error-text">{{ errorMessage }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Props定义
export interface AvatarUploaderProps {
  modelValue?: string
  placeholder?: string
  maxSize?: number // KB
  allowedTypes?: string[]
  showActions?: boolean
  disabled?: boolean
  width?: string
  height?: string
  borderRadius?: string
  uploadUrl?: string
  headers?: Record<string, string>
}

const props = withDefaults(defineProps<AvatarUploaderProps>(), {
  placeholder: '上传头像',
  maxSize: 5120, // 5MB
  allowedTypes: () => ['jpg', 'jpeg', 'png', 'webp'],
  showActions: true,
  disabled: false,
  width: '200rpx',
  height: '200rpx',
  borderRadius: '50%',
  uploadUrl: '/api/upload/avatar',
  headers: () => ({})
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'upload-success': [data: any]
  'upload-error': [error: any]
  'before-upload': [file: any]
  'remove': []
}>()

// 响应式状态
const uploading = ref(false)
const uploadProgress = ref(0)
const errorMessage = ref('')

// 计算属性
const displayValue = computed(() => props.modelValue)

// 样式计算
const avatarStyle = computed(() => ({
  width: props.width,
  height: props.height,
  borderRadius: props.borderRadius
}))

// 监听modelValue变化，清除错误信息
watch(() => props.modelValue, () => {
  errorMessage.value = ''
})

/**
 * 验证文件类型
 */
const validateFileType = (filePath: string): boolean => {
  const extension = filePath.split('.').pop()?.toLowerCase()
  if (!extension || !props.allowedTypes.includes(extension)) {
    errorMessage.value = `只支持${props.allowedTypes.join('、')}格式的图片`
    return false
  }
  return true
}

/**
 * 验证文件大小
 */
const validateFileSize = (filePath: string): Promise<boolean> => {
  return new Promise((resolve) => {
    uni.getFileInfo({
      filePath,
      success: (res) => {
        const sizeKB = res.size / 1024
        if (sizeKB > props.maxSize) {
          errorMessage.value = `图片大小不能超过${props.maxSize}KB`
          resolve(false)
        } else {
          resolve(true)
        }
      },
      fail: () => {
        errorMessage.value = '获取文件信息失败'
        resolve(false)
      }
    })
  })
}

/**
 * 选择图片
 */
const handleChooseImage = () => {
  if (props.disabled || uploading.value) return

  // 清除之前的错误信息
  errorMessage.value = ''

  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0]
      
      // 验证文件类型
      if (!validateFileType(tempFilePath)) {
        return
      }
      
      // 验证文件大小
      const sizeValid = await validateFileSize(tempFilePath)
      if (!sizeValid) {
        return
      }
      
      // 触发上传前事件
      emit('before-upload', { path: tempFilePath })
      
      // 开始上传
      await uploadImage(tempFilePath)
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      errorMessage.value = '选择图片失败，请重试'
    }
  })
}

/**
 * 上传图片
 */
const uploadImage = async (filePath: string) => {
  uploading.value = true
  uploadProgress.value = 0
  
  try {
    // 获取认证头信息
    const token = uni.getStorageSync('token')
    const uploadHeaders = {
      'Authorization': token ? `Bearer ${token}` : '',
      ...props.headers
    }

    const uploadResult = await new Promise<any>((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        url: props.uploadUrl,
        filePath,
        name: 'file',
        header: uploadHeaders,
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.success || data.code === 200) {
              resolve(data.data || data)
            } else {
              reject(new Error(data.message || '上传失败'))
            }
          } catch (e) {
            reject(new Error('解析上传结果失败'))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })

      // 监听上传进度
      uploadTask.onProgressUpdate((res) => {
        uploadProgress.value = res.progress
      })
    })

    // 更新modelValue
    const imageUrl = uploadResult.url || uploadResult.path || filePath
    emit('update:modelValue', imageUrl)
    emit('upload-success', uploadResult)

    uni.showToast({
      title: '上传成功',
      icon: 'success',
      duration: 1500
    })

  } catch (error: any) {
    console.error('上传头像失败:', error)
    errorMessage.value = error.message || '上传失败，请重试'
    emit('upload-error', error)
    
    uni.showToast({
      title: '上传失败',
      icon: 'error',
      duration: 2000
    })
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

/**
 * 删除图片
 */
const handleRemoveImage = () => {
  if (props.disabled) return

  uni.showModal({
    title: '确认删除',
    content: '确定要删除当前头像吗？',
    success: (res) => {
      if (res.confirm) {
        emit('update:modelValue', '')
        emit('remove')
        errorMessage.value = ''
        
        uni.showToast({
          title: '已删除',
          icon: 'success',
          duration: 1000
        })
      }
    }
  })
}

/**
 * 图片加载错误处理
 */
const handleImageError = () => {
  errorMessage.value = '图片加载失败'
  console.error('头像图片加载失败:', props.modelValue)
}

// 暴露方法给父组件
defineExpose({
  chooseImage: handleChooseImage,
  removeImage: handleRemoveImage,
  clearError: () => { errorMessage.value = '' }
})
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;

.avatar-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-sm;
}

.avatar-preview {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: $bg-color-light;
  border: 2rpx dashed $border-color-light;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: $primary-color;
    background: rgba($primary-color, 0.05);
  }
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
  color: $text-color-secondary;
}

.upload-icon {
  font-size: 64rpx;
  margin-bottom: $spacing-xs;
}

.upload-text {
  font-size: $font-size-sm;
  color: $text-color-placeholder;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.upload-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: $spacing-xs;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.upload-progress {
  font-size: $font-size-sm;
  color: white;
}

.avatar-actions {
  display: flex;
  gap: $spacing-sm;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  background: $bg-color-white;
  border: 1rpx solid $border-color-light;
  border-radius: $border-radius-sm;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: $bg-color-light;
    border-color: $primary-color;
  }
  
  &.danger {
    &:hover {
      background: rgba($error-color, 0.1);
      border-color: $error-color;
      color: $error-color;
    }
  }
}

.action-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: $font-size-xs;
  color: $text-color-secondary;
}

.error-message {
  margin-top: $spacing-xs;
}

.error-text {
  font-size: $font-size-xs;
  color: $error-color;
  text-align: center;
}

// 响应式调整
@media (max-width: 750rpx) {
  .upload-icon {
    font-size: 48rpx;
  }
  
  .upload-text {
    font-size: $font-size-xs;
  }
  
  .action-icon {
    font-size: 28rpx;
  }
}
</style>