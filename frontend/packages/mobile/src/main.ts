import { createSSRApp } from 'vue'
import App from './App.vue'
import pinia from './store'
// import './common/styles/index.scss' // 暂时禁用，使用uView UI样式
import { optimizeEventListeners } from './utils/eventOptimizer'
// 引入uView Plus - Vue 3原生支持
import uviewPlus from 'uview-plus'

export function createApp() {
  const app = createSSRApp(App)

  // 优化事件监听器性能
  if (typeof window !== 'undefined') {
    optimizeEventListeners()
  }

  // 使用 Pinia
  app.use(pinia)
  
  // 使用 uView Plus
  app.use(uviewPlus)

  return {
    app
  }
}