<template>
  <div class="min-h-screen">
    <!-- Hero Section with brand gradient -->
    <div class="hero min-h-[70vh] bg-gradient-to-br from-light-blue via-warm-white to-foshou-yellow/20 relative overflow-hidden">
      <!-- Floating decorative elements -->
      <div class="absolute top-10 left-10 w-20 h-20 bg-changchun-blue/10 rounded-full animate-float"></div>
      <div class="absolute top-20 right-20 w-16 h-16 bg-foshou-yellow/20 rounded-full animate-float" style="animation-delay: 2s;"></div>
      <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-changchun-blue/5 rounded-full animate-float" style="animation-delay: 4s;"></div>
      
      <div class="hero-content text-center relative z-10">
        <div class="max-w-4xl">
          <!-- Badge -->
          <div class="badge badge-secondary badge-lg mb-6 animate-pulse-soft">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            领先的智慧评估平台
          </div>
          
          <!-- Main title with gradient text -->
          <h1 class="text-6xl font-bold mb-6 bg-gradient-to-r from-changchun-blue to-dark-blue bg-clip-text text-transparent animate-bounce-soft">
            智慧评估，精准决策
          </h1>
          
          <!-- Subtitle -->
          <p class="text-xl text-text-primary mb-8 leading-relaxed max-w-3xl mx-auto">
            我们提供领先的多租户SaaS评估解决方案，专为养老、医疗、政府及保险机构设计，通过智能化数据分析，助力您实现卓越管理。
          </p>
          
          <!-- CTA Buttons with enhanced styling -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button class="btn btn-primary btn-lg shadow-brand hover:shadow-brand-hover transform hover:scale-105 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              获取演示
            </button>
            <button class="btn btn-secondary btn-lg shadow-yellow hover:shadow-yellow-hover transform hover:scale-105 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              免费试用
            </button>
          </div>
          
          <!-- Trust indicators -->
          <div class="mt-12 flex flex-wrap justify-center items-center gap-6 opacity-60">
            <div class="flex items-center gap-2">
              <div class="badge badge-outline">500+ 机构信赖</div>
            </div>
            <div class="flex items-center gap-2">
              <div class="badge badge-outline">50万+ 评估数据</div>
            </div>
            <div class="flex items-center gap-2">
              <div class="badge badge-outline">99.9% 系统稳定性</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Features Section with enhanced styling -->
    <section class="py-20 bg-base-100">
      <div class="container mx-auto px-4">
        <!-- Section header -->
        <div class="text-center mb-16">
          <div class="badge badge-primary badge-lg mb-4">核心功能</div>
          <h2 class="text-5xl font-bold text-primary mb-6">强大功能，专业保障</h2>
          <p class="text-lg text-text-secondary max-w-2xl mx-auto">
            集成前沿技术与行业最佳实践，为您打造全方位的评估管理解决方案
          </p>
        </div>
        
        <!-- Features grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <!-- Feature 1 -->
          <div class="card bg-gradient-to-br from-warm-white to-base-200 shadow-soft-lg border-2 border-foshou-yellow/30 hover:border-changchun-blue hover:shadow-brand-hover transition-all duration-500 transform hover:-translate-y-2">
            <figure class="px-8 pt-8">
              <div class="avatar placeholder">
                <div class="bg-changchun-blue text-primary-content rounded-full w-16 h-16">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
              </div>
            </figure>
            <div class="card-body text-center">
              <h3 class="card-title justify-center text-primary text-xl mb-3">多租户SaaS架构</h3>
              <p class="text-base-content/80 leading-relaxed mb-4">
                企业级数据隔离与独立管理，确保信息安全，支持机构无限扩展。
              </p>
              <div class="flex justify-center gap-2">
                <div class="badge badge-outline badge-sm">数据隔离</div>
                <div class="badge badge-outline badge-sm">弹性扩展</div>
              </div>
            </div>
          </div>
          
          <!-- Feature 2 -->
          <div class="card bg-gradient-to-br from-warm-white to-base-200 shadow-soft-lg border-2 border-foshou-yellow/30 hover:border-changchun-blue hover:shadow-brand-hover transition-all duration-500 transform hover:-translate-y-2">
            <figure class="px-8 pt-8">
              <div class="avatar placeholder">
                <div class="bg-changchun-blue text-primary-content rounded-full w-16 h-16">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
              </div>
            </figure>
            <div class="card-body text-center">
              <h3 class="card-title justify-center text-primary text-xl mb-3">AI智能分析</h3>
              <p class="text-base-content/80 leading-relaxed mb-4">
                内置先进算法，自动生成多维度分析报告，提供科学决策支持。
              </p>
              <div class="flex justify-center gap-2">
                <div class="badge badge-outline badge-sm">智能算法</div>
                <div class="badge badge-outline badge-sm">数据洞察</div>
              </div>
            </div>
          </div>
          
          <!-- Feature 3 -->
          <div class="card bg-gradient-to-br from-warm-white to-base-200 shadow-soft-lg border-2 border-foshou-yellow/30 hover:border-changchun-blue hover:shadow-brand-hover transition-all duration-500 transform hover:-translate-y-2">
            <figure class="px-8 pt-8">
              <div class="avatar placeholder">
                <div class="bg-changchun-blue text-primary-content rounded-full w-16 h-16">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
            </figure>
            <div class="card-body text-center">
              <h3 class="card-title justify-center text-primary text-xl mb-3">移动优先设计</h3>
              <p class="text-base-content/80 leading-relaxed mb-4">
                专为一线评估人员优化的移动端体验，支持离线操作与数据同步。
              </p>
              <div class="flex justify-center gap-2">
                <div class="badge badge-outline badge-sm">移动优先</div>
                <div class="badge badge-outline badge-sm">离线同步</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Statistics -->
        <div class="stats stats-vertical lg:stats-horizontal shadow-soft-lg w-full bg-gradient-to-r from-changchun-blue to-dark-blue text-primary-content">
          <div class="stat place-items-center">
            <div class="stat-title text-primary-content/80">服务机构</div>
            <div class="stat-value text-foshou-yellow">500+</div>
            <div class="stat-desc text-primary-content/60">覆盖全国主要城市</div>
          </div>
          <div class="stat place-items-center">
            <div class="stat-title text-primary-content/80">评估数据</div>
            <div class="stat-value text-foshou-yellow">50万+</div>
            <div class="stat-desc text-primary-content/60">累计处理评估量</div>
          </div>
          <div class="stat place-items-center">
            <div class="stat-title text-primary-content/80">系统稳定性</div>
            <div class="stat-value text-foshou-yellow">99.9%</div>
            <div class="stat-desc text-primary-content/60">7×24小时可用</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Who We Serve Section with enhanced timeline design -->
    <section class="bg-gradient-to-br from-light-blue via-warm-white to-base-200 py-20">
      <div class="container mx-auto px-4">
        <!-- Section header -->
        <div class="text-center mb-16">
          <div class="badge badge-secondary badge-lg mb-4">服务对象</div>
          <h2 class="text-5xl font-bold text-primary mb-6">我们的服务对象</h2>
          <p class="text-lg text-text-secondary max-w-2xl mx-auto">
            为不同行业量身定制的专业评估解决方案，满足各类机构的特定需求
          </p>
        </div>
        
        <!-- Service categories with enhanced design -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <!-- Government -->
          <div class="card bg-gradient-to-br from-warm-white to-base-100 shadow-soft-lg border-2 border-changchun-blue/20 hover:border-changchun-blue hover:shadow-brand-hover transition-all duration-500 transform hover:-translate-y-3">
            <figure class="px-8 pt-8">
              <div class="avatar placeholder">
                <div class="bg-gradient-to-br from-changchun-blue to-dark-blue text-primary-content rounded-full w-20 h-20">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
              </div>
            </figure>
            <div class="card-body text-center">
              <h3 class="card-title justify-center text-primary text-xl mb-3">政府机构</h3>
              <p class="text-base-content/80 leading-relaxed mb-4">
                标准化评估体系，助力政策制定与监管，确保公共服务质量
              </p>
              <div class="flex justify-center gap-2 mb-4">
                <div class="badge badge-outline badge-sm">政策支持</div>
                <div class="badge badge-outline badge-sm">标准化</div>
              </div>
              <div class="collapse collapse-arrow bg-base-200">
                <input type="checkbox" />
                <div class="collapse-title text-sm font-medium">
                  了解更多
                </div>
                <div class="collapse-content text-sm">
                  <ul class="list-disc list-inside space-y-1 text-left">
                    <li>多维度评估指标体系</li>
                    <li>数据可视化决策支持</li>
                    <li>合规性监管工具</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Healthcare -->
          <div class="card bg-gradient-to-br from-warm-white to-base-100 shadow-soft-lg border-2 border-changchun-blue/20 hover:border-changchun-blue hover:shadow-brand-hover transition-all duration-500 transform hover:-translate-y-3">
            <figure class="px-8 pt-8">
              <div class="avatar placeholder">
                <div class="bg-gradient-to-br from-changchun-blue to-dark-blue text-primary-content rounded-full w-20 h-20">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
              </div>
            </figure>
            <div class="card-body text-center">
              <h3 class="card-title justify-center text-primary text-xl mb-3">医疗机构</h3>
              <p class="text-base-content/80 leading-relaxed mb-4">
                临床决策支持，提升患者护理质量，优化医疗资源配置
              </p>
              <div class="flex justify-center gap-2 mb-4">
                <div class="badge badge-outline badge-sm">临床支持</div>
                <div class="badge badge-outline badge-sm">质量提升</div>
              </div>
              <div class="collapse collapse-arrow bg-base-200">
                <input type="checkbox" />
                <div class="collapse-title text-sm font-medium">
                  了解更多
                </div>
                <div class="collapse-content text-sm">
                  <ul class="list-disc list-inside space-y-1 text-left">
                    <li>智能诊疗辅助系统</li>
                    <li>患者风险评估模型</li>
                    <li>医疗质量管控工具</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Elderly Care -->
          <div class="card bg-gradient-to-br from-warm-white to-base-100 shadow-soft-lg border-2 border-changchun-blue/20 hover:border-changchun-blue hover:shadow-brand-hover transition-all duration-500 transform hover:-translate-y-3">
            <figure class="px-8 pt-8">
              <div class="avatar placeholder">
                <div class="bg-gradient-to-br from-changchun-blue to-dark-blue text-primary-content rounded-full w-20 h-20">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
            </figure>
            <div class="card-body text-center">
              <h3 class="card-title justify-center text-primary text-xl mb-3">养老机构</h3>
              <p class="text-base-content/80 leading-relaxed mb-4">
                个性化照护计划，优化资源分配，提升老年人生活质量
              </p>
              <div class="flex justify-center gap-2 mb-4">
                <div class="badge badge-outline badge-sm">个性化</div>
                <div class="badge badge-outline badge-sm">照护优化</div>
              </div>
              <div class="collapse collapse-arrow bg-base-200">
                <input type="checkbox" />
                <div class="collapse-title text-sm font-medium">
                  了解更多
                </div>
                <div class="collapse-content text-sm">
                  <ul class="list-disc list-inside space-y-1 text-left">
                    <li>能力评估与照护等级</li>
                    <li>健康状况持续监测</li>
                    <li>照护方案智能推荐</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Insurance -->
          <div class="card bg-gradient-to-br from-warm-white to-base-100 shadow-soft-lg border-2 border-changchun-blue/20 hover:border-changchun-blue hover:shadow-brand-hover transition-all duration-500 transform hover:-translate-y-3">
            <figure class="px-8 pt-8">
              <div class="avatar placeholder">
                <div class="bg-gradient-to-br from-changchun-blue to-dark-blue text-primary-content rounded-full w-20 h-20">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
              </div>
            </figure>
            <div class="card-body text-center">
              <h3 class="card-title justify-center text-primary text-xl mb-3">保险机构</h3>
              <p class="text-base-content/80 leading-relaxed mb-4">
                精准风险评估，优化长护险产品设计，降低理赔风险
              </p>
              <div class="flex justify-center gap-2 mb-4">
                <div class="badge badge-outline badge-sm">风险评估</div>
                <div class="badge badge-outline badge-sm">产品优化</div>
              </div>
              <div class="collapse collapse-arrow bg-base-200">
                <input type="checkbox" />
                <div class="collapse-title text-sm font-medium">
                  了解更多
                </div>
                <div class="collapse-content text-sm">
                  <ul class="list-disc list-inside space-y-1 text-left">
                    <li>精算模型与风险量化</li>
                    <li>理赔智能审核系统</li>
                    <li>产品设计数据支持</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Service Flow with Steps -->
        <div class="text-center mb-12">
          <h3 class="text-3xl font-bold text-primary mb-8">服务流程</h3>
          <div class="steps steps-horizontal w-full">
            <div class="step step-primary">需求分析</div>
            <div class="step step-primary">方案设计</div>
            <div class="step step-primary">系统部署</div>
            <div class="step step-primary">培训支持</div>
            <div class="step">持续优化</div>
          </div>
        </div>
        
        <!-- Success Stories with Testimonials -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="card bg-base-100 shadow-soft-lg">
            <div class="card-body">
              <div class="flex items-start gap-4">
                <div class="avatar">
                  <div class="w-12 h-12 rounded-full bg-changchun-blue text-primary-content flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-base-content/80 italic mb-3">
                    "系统大大提升了我们的评估效率，数据分析功能特别强大"
                  </p>
                  <div class="text-sm">
                    <span class="font-semibold text-primary">张主任</span>
                    <span class="text-base-content/60"> - 某市民政局</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="card bg-base-100 shadow-soft-lg">
            <div class="card-body">
              <div class="flex items-start gap-4">
                <div class="avatar">
                  <div class="w-12 h-12 rounded-full bg-changchun-blue text-primary-content flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-base-content/80 italic mb-3">
                    "移动端操作很方便，护士们很快就上手了"
                  </p>
                  <div class="text-sm">
                    <span class="font-semibold text-primary">李护士长</span>
                    <span class="text-base-content/60"> - 市中心医院</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="card bg-base-100 shadow-soft-lg">
            <div class="card-body">
              <div class="flex items-start gap-4">
                <div class="avatar">
                  <div class="w-12 h-12 rounded-full bg-changchun-blue text-primary-content flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-base-content/80 italic mb-3">
                    "照护计划更精准了，老人家属满意度明显提升"
                  </p>
                  <div class="text-sm">
                    <span class="font-semibold text-primary">王院长</span>
                    <span class="text-base-content/60"> - 阳光养老院</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action Section with enhanced design -->
    <section class="bg-gradient-to-br from-changchun-blue via-dark-blue to-changchun-blue py-20 relative overflow-hidden">
      <!-- Background decorative elements -->
      <div class="absolute top-0 left-0 w-full h-full">
        <div class="absolute top-20 left-20 w-32 h-32 bg-foshou-yellow/10 rounded-full animate-float"></div>
        <div class="absolute bottom-20 right-20 w-24 h-24 bg-warm-white/10 rounded-full animate-float" style="animation-delay: 3s;"></div>
        <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-foshou-yellow/5 rounded-full animate-float" style="animation-delay: 1.5s;"></div>
      </div>
      
      <div class="container mx-auto px-4 text-center relative z-10">
        <!-- Main CTA content -->
        <div class="max-w-4xl mx-auto">
          <!-- Badge with animation -->
          <div class="badge badge-secondary badge-lg mb-6 animate-pulse-soft">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            免费体验
          </div>
          
          <h2 class="text-5xl font-bold text-primary-content mb-6">准备好提升您的评估效率了吗？</h2>
          <p class="text-xl text-primary-content/90 max-w-3xl mx-auto mb-12 leading-relaxed">
            立即开始，体验专为您的行业打造的下一代评估工具。我们的专家团队将为您提供全程支持，助您快速实现数字化转型。
          </p>
          
          <!-- CTA buttons with enhanced styling -->
          <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <button class="btn btn-secondary btn-lg shadow-yellow hover:shadow-yellow-hover transform hover:scale-105 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              立即申请演示
            </button>
            <button class="btn btn-outline btn-lg border-primary-content text-primary-content hover:bg-primary-content hover:text-changchun-blue transform hover:scale-105 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              下载产品手册
            </button>
          </div>
          
          <!-- Contact options -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <!-- Phone -->
            <div class="card bg-primary-content/10 backdrop-blur-sm border border-primary-content/20">
              <div class="card-body text-center py-6">
                <div class="avatar placeholder mb-3">
                  <div class="bg-foshou-yellow text-changchun-blue rounded-full w-12 h-12">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                </div>
                <h4 class="text-lg font-semibold text-primary-content mb-2">电话咨询</h4>
                <p class="text-primary-content/80 text-sm">************</p>
                <p class="text-primary-content/60 text-xs">工作日 9:00-18:00</p>
              </div>
            </div>
            
            <!-- Email -->
            <div class="card bg-primary-content/10 backdrop-blur-sm border border-primary-content/20">
              <div class="card-body text-center py-6">
                <div class="avatar placeholder mb-3">
                  <div class="bg-foshou-yellow text-changchun-blue rounded-full w-12 h-12">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
                <h4 class="text-lg font-semibold text-primary-content mb-2">邮箱联系</h4>
                <p class="text-primary-content/80 text-sm"><EMAIL></p>
                <p class="text-primary-content/60 text-xs">24小时内回复</p>
              </div>
            </div>
            
            <!-- WeChat -->
            <div class="card bg-primary-content/10 backdrop-blur-sm border border-primary-content/20">
              <div class="card-body text-center py-6">
                <div class="avatar placeholder mb-3">
                  <div class="bg-foshou-yellow text-changchun-blue rounded-full w-12 h-12">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                </div>
                <h4 class="text-lg font-semibold text-primary-content mb-2">微信咨询</h4>
                <p class="text-primary-content/80 text-sm">智能评估助手</p>
                <p class="text-primary-content/60 text-xs">实时在线服务</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- Footer Section -->
    <footer class="footer footer-center p-10 bg-base-200 text-base-content">
      <aside>
        <div class="avatar placeholder mb-4">
          <div class="bg-changchun-blue text-primary-content rounded-full w-16 h-16">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
        </div>
        <p class="font-bold text-xl text-primary">
          智慧评估平台
        </p>
        <p class="text-base-content/70">专业的多租户SaaS评估解决方案提供商</p>
        <p class="text-sm text-base-content/50">Copyright © 2024 - All right reserved</p>
      </aside>
      <nav>
        <div class="grid grid-flow-col gap-4">
          <div class="tooltip" data-tip="关注我们的微信公众号">
            <a class="btn btn-ghost btn-circle">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </a>
          </div>
          <div class="tooltip" data-tip="联系客服">
            <a class="btn btn-ghost btn-circle">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </a>
          </div>
          <div class="tooltip" data-tip="发送邮件">
            <a class="btn btn-ghost btn-circle">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </a>
          </div>
        </div>
      </nav>
    </footer>
  </div>
</template>