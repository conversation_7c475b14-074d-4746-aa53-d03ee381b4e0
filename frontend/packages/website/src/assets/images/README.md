# 图片资源说明

本目录包含网站所需的图片资源。

## 已完成的图片列表

### 1. ProductIntro.vue 页面 ✅
- `saas-architecture.jpg` - SaaS多租户架构示意图
  - 尺寸: 800x600px
  - 描述: 展示多租户SaaS架构的技术图表
  - 状态: 已添加并应用

### 2. CaseStudy.vue 页面 ✅
- `government-building.jpg` - 政府办公大楼
  - 尺寸: 600x400px
  - 描述: 现代政府办公建筑外观
  - 状态: 已添加并应用

- `hospital.jpg` - 现代医院建筑
  - 尺寸: 600x400px
  - 描述: 现代化医疗中心或医院建筑
  - 状态: 已添加并应用

- `elderly-care.jpg` - 养老机构
  - 尺寸: 600x400px
  - 描述: 温馨的养老院或护理中心环境
  - 状态: 已添加并应用

- `insurance.jpg` - 保险公司办公环境
  - 尺寸: 600x400px
  - 描述: 专业的保险公司办公场所
  - 状态: 已添加并应用

### 3. Features.vue 页面 ✅
- `web-dashboard.jpg` - Web管理后台界面
  - 尺寸: 600x400px
  - 描述: 数据分析仪表板界面
  - 状态: 已添加并应用

- `mobile-app.jpg` - 移动端应用界面
  - 尺寸: 600x400px
  - 描述: 移动设备上的应用界面
  - 状态: 已添加并应用

### 4. Pricing.vue 页面 📋
- `pricing-bg.jpg` - 定价页面背景图
  - 尺寸: 800x600px
  - 描述: 商务办公环境背景
  - 状态: 已下载，待应用

## 完成情况总结

✅ **已完成的工作：**
1. 创建了 `frontend/packages/website/src/assets/images/` 目录
2. 从 Unsplash 下载了8张高质量免费商用图片
3. 更新了以下页面的图片引用：
   - ProductIntro.vue: 替换了外部SaaS架构图
   - CaseStudy.vue: 替换了所有4张案例图片
   - Features.vue: 添加了Web和移动端功能图片
4. 所有图片都使用本地路径 `@/assets/images/` 引用

📋 **待完成的工作：**
- Pricing.vue 页面可以考虑添加背景图片（已下载 pricing-bg.jpg）

## 图片来源

所有图片均来自 Unsplash (https://unsplash.com)，遵循 Unsplash License：
- 免费用于商业和非商业用途
- 无需署名（但建议署名）
- 可以修改和分发

## 技术说明

1. 图片格式: JPG (已优化压缩)
2. 引用方式: Vue 别名路径 `@/assets/images/`
3. 响应式处理: 使用 Tailwind CSS 类进行响应式布局
4. 加载优化: 图片大小已针对网页显示优化
