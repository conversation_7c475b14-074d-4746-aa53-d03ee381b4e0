<template>
  <div class="dynamic-form-renderer">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      :label-position="labelPosition"
      :size="size"
      @submit.prevent="handleSubmit"
    >
      <!-- 渲染表单字段 -->
      <template v-for="field in formFields" :key="field.id">
        <el-form-item
          :label="field.label"
          :prop="field.name"
          :required="field.required"
          :class="getFieldClass(field)"
        >
          <!-- 文本输入 -->
          <el-input
            v-if="field.type === 'text'"
            v-model="formData[field.name]"
            :placeholder="field.placeholder"
            :maxlength="field.maxLength"
            :disabled="field.disabled || readonly"
            :clearable="field.clearable"
            show-word-limit
          />
          
          <!-- 多行文本 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.name]"
            type="textarea"
            :placeholder="field.placeholder"
            :rows="field.rows || 4"
            :maxlength="field.maxLength"
            :disabled="field.disabled || readonly"
            show-word-limit
          />
          
          <!-- 数字输入 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formData[field.name]"
            :min="field.min"
            :max="field.max"
            :step="field.step || 1"
            :precision="field.precision"
            :disabled="field.disabled || readonly"
            controls-position="right"
            style="width: 100%"
          />
          
          <!-- 下拉选择 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formData[field.name]"
            :placeholder="field.placeholder || '请选择'"
            :disabled="field.disabled || readonly"
            :clearable="field.clearable"
            :multiple="field.multiple"
            :filterable="field.filterable"
            style="width: 100%"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>
          
          <!-- 单选按钮组 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="formData[field.name]"
            :disabled="field.disabled || readonly"
          >
            <el-radio
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          
          <!-- 多选框组 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="formData[field.name]"
            :disabled="field.disabled || readonly"
          >
            <el-checkbox
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
          
          <!-- 开关 -->
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="formData[field.name]"
            :disabled="field.disabled || readonly"
            :active-text="field.activeText"
            :inactive-text="field.inactiveText"
          />
          
          <!-- 日期选择 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="formData[field.name]"
            type="date"
            :placeholder="field.placeholder || '选择日期'"
            :disabled="field.disabled || readonly"
            :clearable="field.clearable"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
          
          <!-- 时间选择 -->
          <el-time-picker
            v-else-if="field.type === 'time'"
            v-model="formData[field.name]"
            :placeholder="field.placeholder || '选择时间'"
            :disabled="field.disabled || readonly"
            :clearable="field.clearable"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            style="width: 100%"
          />
          
          <!-- 日期时间选择 -->
          <el-date-picker
            v-else-if="field.type === 'datetime'"
            v-model="formData[field.name]"
            type="datetime"
            :placeholder="field.placeholder || '选择日期时间'"
            :disabled="field.disabled || readonly"
            :clearable="field.clearable"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          
          <!-- 评分组件 -->
          <el-rate
            v-else-if="field.type === 'rate'"
            v-model="formData[field.name]"
            :max="field.max || 5"
            :disabled="field.disabled || readonly"
            :allow-half="field.allowHalf"
            :show-text="field.showText"
            :texts="field.texts"
          />
          
          <!-- 滑块 -->
          <el-slider
            v-else-if="field.type === 'slider'"
            v-model="formData[field.name]"
            :min="field.min || 0"
            :max="field.max || 100"
            :step="field.step || 1"
            :disabled="field.disabled || readonly"
            :show-stops="field.showStops"
            :show-tooltip="field.showTooltip"
            :marks="field.marks"
          />
          
          <!-- 颜色选择器 -->
          <el-color-picker
            v-else-if="field.type === 'color'"
            v-model="formData[field.name]"
            :disabled="field.disabled || readonly"
            :show-alpha="field.showAlpha"
            :color-format="field.colorFormat"
          />
          
          <!-- 文件上传 -->
          <el-upload
            v-else-if="field.type === 'upload'"
            class="dynamic-upload"
            :action="field.action || uploadAction"
            :headers="field.headers"
            :data="field.data"
            :multiple="field.multiple"
            :accept="field.accept"
            :limit="field.limit"
            :disabled="field.disabled || readonly"
            :on-success="(response: any, file: any) => handleUploadSuccess(response, file, field)"
            :on-error="(error: any, file: any) => handleUploadError(error, file, field)"
            :on-remove="(file: any) => handleUploadRemove(file, field)"
          >
            <el-button :disabled="field.disabled || readonly">
              <el-icon><Upload /></el-icon>
              {{ field.uploadText || '选择文件' }}
            </el-button>
            <template #tip>
              <div class="el-upload__tip" v-if="field.tip">
                {{ field.tip }}
              </div>
            </template>
          </el-upload>
          
          <!-- 级联选择器 -->
          <el-cascader
            v-else-if="field.type === 'cascader'"
            v-model="formData[field.name]"
            :options="field.options"
            :props="field.props"
            :placeholder="field.placeholder || '请选择'"
            :disabled="field.disabled || readonly"
            :clearable="field.clearable"
            :filterable="field.filterable"
            style="width: 100%"
          />
          
          <!-- 树形选择 -->
          <el-tree-select
            v-else-if="field.type === 'tree-select'"
            v-model="formData[field.name]"
            :data="field.data"
            :props="field.props"
            :placeholder="field.placeholder || '请选择'"
            :disabled="field.disabled || readonly"
            :clearable="field.clearable"
            :filterable="field.filterable"
            :multiple="field.multiple"
            style="width: 100%"
          />
          
          <!-- 自定义组件插槽 -->
          <slot
            v-else-if="field.type === 'custom'"
            :name="`field-${field.name}`"
            :field="field"
            :value="formData[field.name]"
            :updateValue="(value: any) => updateFieldValue(field.name, value)"
          />
          
          <!-- 字段帮助文本 -->
          <div v-if="field.help" class="field-help">
            <el-icon><QuestionFilled /></el-icon>
            {{ field.help }}
          </div>
        </el-form-item>
      </template>
      
      <!-- 表单操作按钮 -->
      <el-form-item v-if="showActions && !readonly" class="form-actions">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ submitText }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, QuestionFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 类型定义
interface FormFieldOption {
  label: string
  value: any
  disabled?: boolean
}

interface FormField {
  id: string
  name: string
  type: string
  label: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  clearable?: boolean
  help?: string
  
  // 数字类型特有属性
  min?: number
  max?: number
  step?: number
  precision?: number
  
  // 文本类型特有属性
  maxLength?: number
  rows?: number
  
  // 选择类型特有属性
  options?: FormFieldOption[]
  multiple?: boolean
  filterable?: boolean
  
  // 开关类型特有属性
  activeText?: string
  inactiveText?: string
  
  // 评分类型特有属性
  allowHalf?: boolean
  showText?: boolean
  texts?: string[]
  
  // 滑块类型特有属性
  showStops?: boolean
  showTooltip?: boolean
  marks?: Record<number, string>
  
  // 颜色选择器特有属性
  showAlpha?: boolean
  colorFormat?: string
  
  // 上传特有属性
  action?: string
  headers?: Record<string, string>
  data?: Record<string, any>
  accept?: string
  limit?: number
  uploadText?: string
  tip?: string
  
  // 级联选择器特有属性
  props?: Record<string, any>
  
  // 树形选择特有属性
  treeData?: any[]
  
  // 验证规则
  rules?: any[]
  
  // 条件显示
  showWhen?: {
    field: string
    value: any
    operator?: 'eq' | 'ne' | 'in' | 'not-in'
  }
  
  // 计算属性
  computed?: {
    fields: string[]
    expression: string
  }
}

// Props
interface Props {
  fields: FormField[]
  modelValue?: Record<string, any>
  readonly?: boolean
  labelWidth?: string
  labelPosition?: 'left' | 'right' | 'top'
  size?: 'large' | 'default' | 'small'
  showActions?: boolean
  submitText?: string
  uploadAction?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  readonly: false,
  labelWidth: '120px',
  labelPosition: 'right',
  size: 'default',
  showActions: true,
  submitText: '提交',
  uploadAction: '/api/upload'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  'submit': [value: Record<string, any>]
  'reset': []
  'field-change': [fieldName: string, value: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const formData = reactive<Record<string, any>>({})
const submitting = ref(false)

// 计算属性
const formFields = computed(() => {
  return props.fields.filter(field => {
    // 条件显示逻辑
    if (field.showWhen) {
      const { field: targetField, value: targetValue, operator = 'eq' } = field.showWhen
      const currentValue = formData[targetField]
      
      switch (operator) {
        case 'eq':
          return currentValue === targetValue
        case 'ne':
          return currentValue !== targetValue
        case 'in':
          return Array.isArray(targetValue) && targetValue.includes(currentValue)
        case 'not-in':
          return Array.isArray(targetValue) && !targetValue.includes(currentValue)
        default:
          return true
      }
    }
    return true
  })
})

const formRules = computed<FormRules>(() => {
  const rules: FormRules = {}
  
  formFields.value.forEach(field => {
    const fieldRules: any[] = []
    
    // 必填验证
    if (field.required) {
      fieldRules.push({
        required: true,
        message: `请输入${field.label}`,
        trigger: getValidationTrigger(field.type)
      })
    }
    
    // 长度验证
    if (field.maxLength) {
      fieldRules.push({
        max: field.maxLength,
        message: `${field.label}最多${field.maxLength}个字符`,
        trigger: 'blur'
      })
    }
    
    // 数字范围验证
    if (field.type === 'number') {
      if (field.min !== undefined) {
        fieldRules.push({
          type: 'number',
          min: field.min,
          message: `${field.label}不能小于${field.min}`,
          trigger: 'blur'
        })
      }
      if (field.max !== undefined) {
        fieldRules.push({
          type: 'number',
          max: field.max,
          message: `${field.label}不能大于${field.max}`,
          trigger: 'blur'
        })
      }
    }
    
    // 自定义验证规则
    if (field.rules) {
      fieldRules.push(...field.rules)
    }
    
    if (fieldRules.length > 0) {
      rules[field.name] = fieldRules
    }
  })
  
  return rules
})

// 方法
const initFormData = () => {
  // 初始化表单数据
  props.fields.forEach(field => {
    if (props.modelValue[field.name] !== undefined) {
      formData[field.name] = props.modelValue[field.name]
    } else {
      // 设置默认值
      formData[field.name] = getDefaultValue(field)
    }
  })
}

const getDefaultValue = (field: FormField) => {
  switch (field.type) {
    case 'checkbox':
      return []
    case 'number':
    case 'rate':
    case 'slider':
      return field.min || 0
    case 'switch':
      return false
    default:
      return ''
  }
}

const getValidationTrigger = (fieldType: string) => {
  switch (fieldType) {
    case 'select':
    case 'cascader':
    case 'tree-select':
    case 'date':
    case 'time':
    case 'datetime':
      return 'change'
    default:
      return 'blur'
  }
}

const getFieldClass = (field: FormField) => {
  const classes = []
  
  if (field.required) {
    classes.push('required-field')
  }
  
  if (field.disabled) {
    classes.push('disabled-field')
  }
  
  classes.push(`field-type-${field.type}`)
  
  return classes.join(' ')
}

const updateFieldValue = (fieldName: string, value: any) => {
  formData[fieldName] = value
  emit('field-change', fieldName, value)
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    submitting.value = true
    
    // 表单验证
    await formRef.value.validate()
    
    // 处理计算字段
    processComputedFields()
    
    // 提交表单
    emit('submit', { ...formData })
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    submitting.value = false
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}

const processComputedFields = () => {
  props.fields.forEach(field => {
    if (field.computed) {
      try {
        // 简单的表达式计算（可以扩展为更复杂的计算引擎）
        const { fields, expression } = field.computed
        const values = fields.map(fieldName => formData[fieldName])
        
        // 这里可以实现更复杂的计算逻辑
        if (expression === 'sum') {
          formData[field.name] = values.reduce((sum, val) => sum + (Number(val) || 0), 0)
        } else if (expression === 'avg') {
          const validValues = values.filter(val => !isNaN(Number(val)))
          formData[field.name] = validValues.length > 0 
            ? validValues.reduce((sum, val) => sum + Number(val), 0) / validValues.length
            : 0
        }
      } catch (error) {
        console.error('计算字段失败:', error)
      }
    }
  })
}

const handleUploadSuccess = (response: any, file: any, field: FormField) => {
  if (!formData[field.name]) {
    formData[field.name] = []
  }
  formData[field.name].push({
    name: file.name,
    url: response.url || response.data?.url,
    response
  })
  
  emit('field-change', field.name, formData[field.name])
}

const handleUploadError = (error: any, file: any, field: FormField) => {
  console.error('文件上传失败:', error)
  ElMessage.error(`${file.name} 上传失败`)
}

const handleUploadRemove = (file: any, field: FormField) => {
  if (formData[field.name] && Array.isArray(formData[field.name])) {
    const index = formData[field.name].findIndex((item: any) => item.name === file.name)
    if (index > -1) {
      formData[field.name].splice(index, 1)
      emit('field-change', field.name, formData[field.name])
    }
  }
}

// 暴露方法给父组件
const validate = () => formRef.value?.validate()
const resetFields = () => formRef.value?.resetFields()
const clearValidate = () => formRef.value?.clearValidate()

defineExpose({
  validate,
  resetFields,
  clearValidate,
  formData
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  Object.assign(formData, newValue)
}, { deep: true })

watch(formData, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 生命周期
onMounted(() => {
  initFormData()
})
</script>

<style scoped>
.dynamic-form-renderer {
  width: 100%;
}

.field-help {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.form-actions {
  margin-top: 24px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 8px;
}

.required-field :deep(.el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.disabled-field {
  opacity: 0.6;
}

.dynamic-upload {
  width: 100%;
}

/* 字段类型特定样式 */
.field-type-rate :deep(.el-rate) {
  display: flex;
  align-items: center;
}

.field-type-slider :deep(.el-slider) {
  margin: 16px 0;
}

.field-type-color :deep(.el-color-picker) {
  width: 100%;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .dynamic-form-renderer :deep(.el-form--label-left .el-form-item__label) {
    text-align: left;
    padding-right: 8px;
  }
  
  .form-actions {
    text-align: left;
  }
  
  .form-actions .el-button {
    width: 100%;
    margin: 4px 0;
  }
}
</style>