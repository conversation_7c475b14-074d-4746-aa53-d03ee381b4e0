<template>
  <div class="form-generator">
    <div class="generator-header">
      <h3>智能表单生成器</h3>
      <div class="header-actions">
        <el-button @click="importFromAI" :icon="MagicStick" type="primary">
          从AI分析导入
        </el-button>
        <el-button @click="importFromTemplate" :icon="Document">
          从模板导入
        </el-button>
        <el-button @click="exportForm" :icon="Download">
          导出表单
        </el-button>
        <el-button @click="previewForm" :icon="View">
          预览表单
        </el-button>
      </div>
    </div>

    <div class="generator-content">
      <el-row :gutter="20">
        <!-- 左侧：数据源和生成配置 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>数据源</span>
                <el-tag :type="getDataSourceStatus()">
                  {{ dataSource.type || '未选择' }}
                </el-tag>
              </div>
            </template>

            <!-- 数据源选择 -->
            <el-form :model="generatorConfig" label-width="100px">
              <el-form-item label="数据源类型">
                <el-select 
                  v-model="dataSource.type" 
                  style="width: 100%"
                  @change="handleDataSourceChange"
                >
                  <el-option label="AI分析结果" value="ai-analysis" />
                  <el-option label="数据库表结构" value="database-schema" />
                  <el-option label="JSON配置" value="json-config" />
                  <el-option label="CSV文件" value="csv-file" />
                  <el-option label="手动创建" value="manual" />
                </el-select>
              </el-form-item>

              <!-- AI分析结果输入 -->
              <template v-if="dataSource.type === 'ai-analysis'">
                <el-form-item label="分析文件">
                  <el-upload
                    v-model:file-list="dataSource.files"
                    :auto-upload="false"
                    :limit="1"
                    accept=".json,.md"
                    class="source-upload"
                  >
                    <el-button :icon="Upload">选择分析文件</el-button>
                  </el-upload>
                </el-form-item>
                
                <el-form-item label="分析文本">
                  <el-input
                    v-model="dataSource.analysisText"
                    type="textarea"
                    :rows="4"
                    placeholder="粘贴AI分析结果或量表描述..."
                  />
                </el-form-item>
              </template>

              <!-- 数据库表结构输入 -->
              <template v-if="dataSource.type === 'database-schema'">
                <el-form-item label="表名">
                  <el-input v-model="dataSource.tableName" placeholder="输入表名" />
                </el-form-item>
                
                <el-form-item label="数据库">
                  <el-select v-model="dataSource.database" style="width: 100%">
                    <el-option label="临时数据库" value="staging" />
                    <el-option label="生产数据库" value="production" />
                  </el-select>
                </el-form-item>
                
                <el-form-item>
                  <el-button @click="loadTableSchema" :loading="loading.schema">
                    加载表结构
                  </el-button>
                </el-form-item>
              </template>

              <!-- JSON配置输入 -->
              <template v-if="dataSource.type === 'json-config'">
                <el-form-item label="JSON配置">
                  <el-input
                    v-model="dataSource.jsonConfig"
                    type="textarea"
                    :rows="6"
                    placeholder="输入JSON表单配置..."
                  />
                </el-form-item>
              </template>
            </el-form>

            <!-- 生成配置 -->
            <el-divider content-position="left">生成配置</el-divider>
            
            <el-form :model="generatorConfig" label-width="100px">
              <el-form-item label="表单类型">
                <el-select v-model="generatorConfig.formType" style="width: 100%">
                  <el-option label="评估表单" value="assessment" />
                  <el-option label="数据录入" value="data-entry" />
                  <el-option label="搜索表单" value="search" />
                  <el-option label="设置表单" value="settings" />
                </el-select>
              </el-form-item>

              <el-form-item label="表单布局">
                <el-radio-group v-model="generatorConfig.layout">
                  <el-radio label="vertical">垂直布局</el-radio>
                  <el-radio label="horizontal">水平布局</el-radio>
                  <el-radio label="inline">内联布局</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="字段映射">
                <el-switch 
                  v-model="generatorConfig.autoMapping"
                  active-text="自动映射"
                  inactive-text="手动配置"
                />
              </el-form-item>

              <el-form-item label="验证规则">
                <el-switch 
                  v-model="generatorConfig.enableValidation"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </el-form-item>

              <el-form-item>
                <el-button 
                  type="primary" 
                  @click="generateForm"
                  :loading="loading.generate"
                  :disabled="!canGenerate"
                >
                  生成表单
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 生成进度 -->
          <el-card v-if="generationProgress.visible" class="progress-card">
            <template #header>
              <span>生成进度</span>
            </template>
            
            <el-progress
              :percentage="generationProgress.percentage"
              :status="generationProgress.status"
            />
            
            <div class="progress-steps">
              <div 
                v-for="step in generationProgress.steps"
                :key="step.name"
                class="progress-step"
                :class="{ 
                  'active': step.status === 'active',
                  'completed': step.status === 'completed',
                  'failed': step.status === 'failed'
                }"
              >
                <el-icon>
                  <component :is="getStepIcon(step.status)" />
                </el-icon>
                <span>{{ step.name }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：表单构建器和预览 -->
        <el-col :span="16">
          <el-tabs v-model="activeTab" type="border-card" class="generator-tabs">
            <!-- 字段映射 -->
            <el-tab-pane label="字段映射" name="mapping">
              <div class="field-mapping">
                <div class="mapping-header">
                  <span>找到 {{ detectedFields.length }} 个字段</span>
                  <el-button @click="autoMapFields" size="small">
                    自动映射
                  </el-button>
                </div>

                <div class="mapping-list">
                  <div 
                    v-for="(field, index) in detectedFields"
                    :key="field.name"
                    class="mapping-item"
                  >
                    <div class="field-info">
                      <div class="field-name">{{ field.name }}</div>
                      <div class="field-type">{{ field.type }}</div>
                      <div class="field-description">{{ field.description }}</div>
                    </div>

                    <el-icon class="mapping-arrow">
                      <ArrowRight />
                    </el-icon>

                    <div class="form-config">
                      <el-select 
                        v-model="field.formType" 
                        size="small"
                        style="width: 120px"
                      >
                        <el-option 
                          v-for="type in availableFormTypes"
                          :key="type.value"
                          :label="type.label"
                          :value="type.value"
                        />
                      </el-select>

                      <el-input
                        v-model="field.label"
                        size="small"
                        placeholder="字段标签"
                        style="width: 100px; margin-left: 8px"
                      />

                      <el-switch
                        v-model="field.required"
                        size="small"
                        active-text="必填"
                        inactive-text="可选"
                        style="margin-left: 8px"
                      />

                      <el-button
                        @click="configureField(field, index)"
                        size="small"
                        link
                        type="primary"
                      >
                        配置
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 表单构建器 -->
            <el-tab-pane label="表单构建" name="builder">
              <FormFieldBuilder
                v-model="generatedFormFields"
                @field-selected="handleFieldSelected"
                @schema-exported="handleSchemaExported"
              />
            </el-tab-pane>

            <!-- 表单预览 -->
            <el-tab-pane label="表单预览" name="preview">
              <div class="form-preview">
                <div class="preview-header">
                  <span>表单预览</span>
                  <div class="preview-actions">
                    <el-button-group>
                      <el-button 
                        :type="previewMode === 'desktop' ? 'primary' : ''"
                        @click="previewMode = 'desktop'"
                        size="small"
                      >
                        桌面端
                      </el-button>
                      <el-button 
                        :type="previewMode === 'tablet' ? 'primary' : ''"
                        @click="previewMode = 'tablet'"
                        size="small"
                      >
                        平板
                      </el-button>
                      <el-button 
                        :type="previewMode === 'mobile' ? 'primary' : ''"
                        @click="previewMode = 'mobile'"
                        size="small"
                      >
                        手机
                      </el-button>
                    </el-button-group>
                  </div>
                </div>

                <div class="preview-container" :class="`preview-${previewMode}`">
                  <DynamicFormRenderer
                    v-if="generatedFormFields.length > 0"
                    :fields="generatedFormFields"
                    :label-width="generatorConfig.layout === 'horizontal' ? '120px' : ''"
                    :label-position="generatorConfig.layout === 'horizontal' ? 'right' : 'top'"
                    :size="previewMode === 'mobile' ? 'small' : 'default'"
                    @submit="handleFormSubmit"
                    @field-change="handleFieldChange"
                  />
                  
                  <div v-else class="preview-empty">
                    <el-empty description="暂无表单内容，请先生成表单" />
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 代码生成 -->
            <el-tab-pane label="代码生成" name="code">
              <div class="code-generation">
                <div class="code-header">
                  <span>生成的代码</span>
                  <el-button-group>
                    <el-button 
                      :type="codeType === 'vue' ? 'primary' : ''"
                      @click="codeType = 'vue'"
                      size="small"
                    >
                      Vue组件
                    </el-button>
                    <el-button 
                      :type="codeType === 'json' ? 'primary' : ''"
                      @click="codeType = 'json'"
                      size="small"
                    >
                      JSON配置
                    </el-button>
                    <el-button 
                      :type="codeType === 'html' ? 'primary' : ''"
                      @click="codeType = 'html'"
                      size="small"
                    >
                      HTML表单
                    </el-button>
                  </el-button-group>
                </div>

                <div class="code-actions">
                  <el-button @click="copyCode" :icon="CopyDocument">
                    复制代码
                  </el-button>
                  <el-button @click="downloadCode" :icon="Download">
                    下载文件
                  </el-button>
                </div>

                <div class="code-content">
                  <pre><code :class="codeType">{{ generateCode() }}</code></pre>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>

    <!-- 字段配置对话框 -->
    <el-dialog
      v-model="showFieldDialog"
      title="字段详细配置"
      width="600px"
    >
      <FieldConfiguration
        v-if="selectedField"
        v-model="selectedField"
        @save="saveFieldConfiguration"
        @cancel="showFieldDialog = false"
      />
    </el-dialog>

    <!-- AI分析导入对话框 -->
    <el-dialog
      v-model="showAIDialog"
      title="从AI分析导入"
      width="70%"
    >
      <AIAnalysisImporter
        @import="handleAIImport"
        @cancel="showAIDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  MagicStick, Document, Download, View, Upload, ArrowRight,
  CopyDocument, Check, Close, Clock, Loading
} from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'

// 导入子组件
import FormFieldBuilder from './FormFieldBuilder.vue'
import DynamicFormRenderer from './DynamicFormRenderer.vue'
// import FieldConfiguration from './FieldConfiguration.vue'
// import AIAnalysisImporter from './AIAnalysisImporter.vue'

// 类型定义
interface DataSource {
  type: string
  files: any[]
  analysisText: string
  tableName: string
  database: string
  jsonConfig: string
}

interface GeneratorConfig {
  formType: string
  layout: 'vertical' | 'horizontal' | 'inline'
  autoMapping: boolean
  enableValidation: boolean
}

interface DetectedField {
  name: string
  type: string
  description: string
  formType: string
  label: string
  required: boolean
  options?: any[]
  validation?: any[]
}

interface GenerationStep {
  name: string
  status: 'pending' | 'active' | 'completed' | 'failed'
}

// 响应式数据
const activeTab = ref('mapping')
const previewMode = ref('desktop')
const codeType = ref('vue')
const showFieldDialog = ref(false)
const showAIDialog = ref(false)
const selectedField = ref<DetectedField | null>(null)
const selectedFieldIndex = ref(-1)

const dataSource = reactive<DataSource>({
  type: '',
  files: [],
  analysisText: '',
  tableName: '',
  database: 'staging',
  jsonConfig: ''
})

const generatorConfig = reactive<GeneratorConfig>({
  formType: 'assessment',
  layout: 'vertical',
  autoMapping: true,
  enableValidation: true
})

const loading = reactive({
  schema: false,
  generate: false
})

const generationProgress = reactive({
  visible: false,
  percentage: 0,
  status: '',
  steps: [] as GenerationStep[]
})

const detectedFields = ref<DetectedField[]>([])
const generatedFormFields = ref<any[]>([])

// 可用的表单类型
const availableFormTypes = [
  { label: '文本输入', value: 'text' },
  { label: '多行文本', value: 'textarea' },
  { label: '数字输入', value: 'number' },
  { label: '下拉选择', value: 'select' },
  { label: '单选按钮', value: 'radio' },
  { label: '多选框', value: 'checkbox' },
  { label: '开关', value: 'switch' },
  { label: '日期选择', value: 'date' },
  { label: '时间选择', value: 'time' },
  { label: '评分', value: 'rate' },
  { label: '滑块', value: 'slider' },
  { label: '文件上传', value: 'upload' }
]

// 计算属性
const canGenerate = computed(() => {
  return dataSource.type && (
    (dataSource.type === 'ai-analysis' && (dataSource.analysisText || dataSource.files.length > 0)) ||
    (dataSource.type === 'database-schema' && dataSource.tableName) ||
    (dataSource.type === 'json-config' && dataSource.jsonConfig) ||
    (dataSource.type === 'manual')
  )
})

// 剪贴板
const { copy } = useClipboard()

// 方法
const handleDataSourceChange = () => {
  detectedFields.value = []
  generatedFormFields.value = []
  
  if (dataSource.type === 'manual') {
    // 手动创建时直接跳转到构建器
    activeTab.value = 'builder'
  }
}

const loadTableSchema = async () => {
  loading.schema = true
  
  try {
    // 这里调用API获取表结构
    const response = await fetch(`/api/scale-management/table-structure/${dataSource.tableName}?environment=${dataSource.database}`)
    const result = await response.json()
    
    if (result.columns) {
      detectedFields.value = result.columns.map((col: any) => ({
        name: col.column_name,
        type: col.data_type,
        description: col.column_comment || '',
        formType: mapDatabaseTypeToFormType(col.data_type),
        label: formatFieldLabel(col.column_name),
        required: col.is_nullable === 'NO'
      }))
      
      ElMessage.success(`成功加载 ${detectedFields.value.length} 个字段`)
      activeTab.value = 'mapping'
    }
  } catch (error) {
    ElMessage.error('加载表结构失败')
    console.error('Load schema error:', error)
  } finally {
    loading.schema = false
  }
}

const generateForm = async () => {
  loading.generate = true
  generationProgress.visible = true
  generationProgress.percentage = 0
  generationProgress.status = ''
  
  // 初始化生成步骤
  generationProgress.steps = [
    { name: '解析数据源', status: 'pending' },
    { name: '字段检测', status: 'pending' },
    { name: '字段映射', status: 'pending' },
    { name: '表单生成', status: 'pending' },
    { name: '验证配置', status: 'pending' }
  ]

  try {
    // 步骤1: 解析数据源
    await executeGenerationStep(0, async () => {
      await parseDataSource()
    })

    // 步骤2: 字段检测
    await executeGenerationStep(1, async () => {
      await detectFields()
    })

    // 步骤3: 字段映射
    await executeGenerationStep(2, async () => {
      if (generatorConfig.autoMapping) {
        autoMapFields()
      }
    })

    // 步骤4: 表单生成
    await executeGenerationStep(3, async () => {
      await buildFormFields()
    })

    // 步骤5: 验证配置
    await executeGenerationStep(4, async () => {
      if (generatorConfig.enableValidation) {
        await configureValidation()
      }
    })

    generationProgress.percentage = 100
    generationProgress.status = 'success'
    
    ElMessage.success('表单生成完成！')
    activeTab.value = 'preview'
    
  } catch (error) {
    generationProgress.status = 'exception'
    ElMessage.error('表单生成失败: ' + error)
    console.error('Form generation error:', error)
  } finally {
    loading.generate = false
    setTimeout(() => {
      generationProgress.visible = false
    }, 2000)
  }
}

const executeGenerationStep = async (stepIndex: number, action: () => Promise<void>) => {
  generationProgress.steps[stepIndex].status = 'active'
  generationProgress.percentage = (stepIndex / generationProgress.steps.length) * 100
  
  try {
    await action()
    generationProgress.steps[stepIndex].status = 'completed'
    await new Promise(resolve => setTimeout(resolve, 500)) // 延迟以显示进度
  } catch (error) {
    generationProgress.steps[stepIndex].status = 'failed'
    throw error
  }
}

const parseDataSource = async () => {
  switch (dataSource.type) {
    case 'ai-analysis':
      await parseAIAnalysis()
      break
    case 'json-config':
      await parseJSONConfig()
      break
    default:
      // 其他类型已经在前面处理
      break
  }
}

const parseAIAnalysis = async () => {
  if (dataSource.analysisText) {
    // 简单的AI分析解析
    const analysisData = {
      tableName: 'ai_generated_form',
      fields: extractFieldsFromText(dataSource.analysisText)
    }
    
    detectedFields.value = analysisData.fields
  }
}

const parseJSONConfig = async () => {
  try {
    const config = JSON.parse(dataSource.jsonConfig)
    if (config.fields) {
      detectedFields.value = config.fields.map((field: any) => ({
        name: field.name || '',
        type: field.type || 'text',
        description: field.description || '',
        formType: field.formType || 'text',
        label: field.label || '',
        required: field.required || false
      }))
    }
  } catch (error) {
    throw new Error('JSON配置格式错误')
  }
}

const detectFields = async () => {
  // 字段检测逻辑
  if (detectedFields.value.length === 0) {
    throw new Error('未检测到任何字段')
  }
}

const autoMapFields = () => {
  detectedFields.value.forEach(field => {
    if (!field.formType) {
      field.formType = mapDatabaseTypeToFormType(field.type)
    }
    if (!field.label) {
      field.label = formatFieldLabel(field.name)
    }
  })
}

const buildFormFields = async () => {
  generatedFormFields.value = detectedFields.value.map(field => ({
    id: `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: field.name,
    type: field.formType,
    label: field.label,
    required: field.required,
    placeholder: `请输入${field.label}`,
    help: field.description,
    options: field.options || []
  }))
}

const configureValidation = async () => {
  // 为字段添加验证规则
  generatedFormFields.value.forEach(field => {
    field.rules = []
    
    if (field.required) {
      field.rules.push({
        required: true,
        message: `请输入${field.label}`,
        trigger: 'blur'
      })
    }
    
    // 根据字段类型添加特定验证
    switch (field.type) {
      case 'number':
        field.rules.push({
          type: 'number',
          message: '请输入有效的数字',
          trigger: 'blur'
        })
        break
      case 'email':
        field.rules.push({
          type: 'email',
          message: '请输入有效的邮箱地址',
          trigger: 'blur'
        })
        break
    }
  })
}

const mapDatabaseTypeToFormType = (dbType: string): string => {
  const typeMap: Record<string, string> = {
    'varchar': 'text',
    'text': 'textarea',
    'integer': 'number',
    'bigint': 'number',
    'boolean': 'switch',
    'timestamp': 'datetime',
    'date': 'date',
    'time': 'time'
  }
  
  return typeMap[dbType.toLowerCase()] || 'text'
}

const formatFieldLabel = (fieldName: string): string => {
  return fieldName
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
}

const extractFieldsFromText = (text: string): DetectedField[] => {
  // 简单的文本字段提取逻辑
  const fields: DetectedField[] = []
  const lines = text.split('\n')
  
  lines.forEach(line => {
    // 查找类似 "字段名: 描述" 的模式
    const match = line.match(/^(\w+):\s*(.+)/)
    if (match) {
      fields.push({
        name: match[1],
        type: 'text',
        description: match[2],
        formType: 'text',
        label: formatFieldLabel(match[1]),
        required: false
      })
    }
  })
  
  return fields
}

const configureField = (field: DetectedField, index: number) => {
  selectedField.value = { ...field }
  selectedFieldIndex.value = index
  showFieldDialog.value = true
}

const saveFieldConfiguration = (configuredField: DetectedField) => {
  if (selectedFieldIndex.value >= 0) {
    detectedFields.value[selectedFieldIndex.value] = configuredField
  }
  showFieldDialog.value = false
}

const handleFieldSelected = (field: any) => {
  // 处理字段选择
}

const handleSchemaExported = (schema: any) => {
  // 处理模式导出
}

const handleFormSubmit = (formData: any) => {
  console.log('Form submitted:', formData)
  ElMessage.success('表单提交成功')
}

const handleFieldChange = (fieldName: string, value: any) => {
  console.log('Field changed:', fieldName, value)
}

const generateCode = (): string => {
  switch (codeType.value) {
    case 'vue':
      return generateVueComponent()
    case 'json':
      return JSON.stringify({ fields: generatedFormFields.value }, null, 2)
    case 'html':
      return generateHTMLForm()
    default:
      return ''
  }
}

const generateVueComponent = (): string => {
  return `<template>
  <div class="generated-form">
    <DynamicFormRenderer
      :fields="formFields"
      @submit="handleSubmit"
      @field-change="handleFieldChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import DynamicFormRenderer from '@/components/DynamicForm/DynamicFormRenderer.vue'

const formFields = ref(${JSON.stringify(generatedFormFields.value, null, 2)})

const handleSubmit = (formData) => {
  console.log('Form data:', formData)
}

const handleFieldChange = (fieldName, value) => {
  console.log('Field changed:', fieldName, value)
}
<\/script>`
}

const generateHTMLForm = (): string => {
  let html = '<form class="generated-form">\n'
  
  generatedFormFields.value.forEach(field => {
    html += `  <div class="form-group">
    <label for="${field.name}">${field.label}${field.required ? ' *' : ''}</label>
    `
    
    switch (field.type) {
      case 'textarea':
        html += `<textarea id="${field.name}" name="${field.name}" placeholder="${field.placeholder || ''}"></textarea>\n`
        break
      case 'select':
        html += `<select id="${field.name}" name="${field.name}">
      <option value="">请选择</option>
    </select>\n`
        break
      default:
        html += `<input type="${field.type === 'number' ? 'number' : 'text'}" id="${field.name}" name="${field.name}" placeholder="${field.placeholder || ''}" />\n`
    }
    
    html += '  </div>\n'
  })
  
  html += '  <button type="submit">提交</button>\n</form>'
  return html
}

const copyCode = async () => {
  try {
    await copy(generateCode())
    ElMessage.success('代码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadCode = () => {
  const code = generateCode()
  const filename = `generated-form.${codeType.value === 'vue' ? 'vue' : codeType.value === 'json' ? 'json' : 'html'}`
  
  const blob = new Blob([code], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('代码文件已下载')
}

const importFromAI = () => {
  showAIDialog.value = true
}

const importFromTemplate = () => {
  // 导入模板逻辑
  ElMessage.info('模板导入功能开发中')
}

const exportForm = () => {
  const formConfig = {
    metadata: {
      name: 'Generated Form',
      type: generatorConfig.formType,
      layout: generatorConfig.layout,
      createdAt: new Date().toISOString()
    },
    fields: generatedFormFields.value,
    validation: generatorConfig.enableValidation
  }
  
  const blob = new Blob([JSON.stringify(formConfig, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `form-config-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('表单配置已导出')
}

const previewForm = () => {
  activeTab.value = 'preview'
}

const handleAIImport = (analysisData: any) => {
  dataSource.type = 'ai-analysis'
  dataSource.analysisText = analysisData.text || ''
  
  if (analysisData.fields) {
    detectedFields.value = analysisData.fields
  }
  
  showAIDialog.value = false
  ElMessage.success('AI分析数据导入成功')
}

const getDataSourceStatus = () => {
  if (!dataSource.type) return 'info'
  return canGenerate.value ? 'success' : 'warning'
}

const getStepIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return Check
    case 'failed':
      return Close
    case 'active':
      return Loading
    default:
      return Clock
  }
}

// 监听器
watch(() => dataSource.type, () => {
  if (dataSource.type === 'ai-analysis') {
    activeTab.value = 'mapping'
  }
})
</script>

<style scoped>
.form-generator {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.generator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.generator-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.generator-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-card {
  margin-top: 16px;
}

.progress-steps {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.progress-step.active {
  background-color: #ecf5ff;
  color: #409eff;
}

.progress-step.completed {
  background-color: #f0f9ff;
  color: #67c23a;
}

.progress-step.failed {
  background-color: #fef0f0;
  color: #f56c6c;
}

.generator-tabs {
  height: calc(100vh - 200px);
}

.field-mapping {
  padding: 16px;
}

.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 500;
}

.mapping-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mapping-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  gap: 16px;
}

.field-info {
  flex: 1;
}

.field-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.field-type {
  color: #409eff;
  font-size: 12px;
  margin-bottom: 4px;
}

.field-description {
  color: #666;
  font-size: 14px;
}

.mapping-arrow {
  color: #c0c4cc;
  font-size: 20px;
}

.form-config {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-preview {
  padding: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-container {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 24px;
  background: white;
  transition: all 0.3s;
}

.preview-container.preview-desktop {
  max-width: none;
}

.preview-container.preview-tablet {
  max-width: 768px;
  margin: 0 auto;
}

.preview-container.preview-mobile {
  max-width: 375px;
  margin: 0 auto;
}

.preview-empty {
  text-align: center;
  padding: 40px;
}

.code-generation {
  padding: 16px;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.code-actions {
  margin-bottom: 16px;
}

.code-content {
  background: #f5f5f5;
  border-radius: 6px;
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.code-content pre {
  margin: 0;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.source-upload :deep(.el-upload) {
  width: 100%;
}

:deep(.el-tabs__content) {
  height: calc(100% - 55px);
  overflow-y: auto;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}
</style>