<template>
  <div class="form-field-builder">
    <div class="builder-header">
      <h3>表单字段构建器</h3>
      <div class="header-actions">
        <el-button @click="addField" type="primary" :icon="Plus">
          添加字段
        </el-button>
        <el-button @click="importFromTemplate" :icon="Document">
          从模板导入
        </el-button>
        <el-button @click="exportSchema" :icon="Download">
          导出配置
        </el-button>
      </div>
    </div>

    <div class="builder-content">
      <!-- 左侧字段列表 -->
      <div class="fields-panel">
        <div class="panel-header">
          <span>字段列表 ({{ fields.length }})</span>
          <el-button text @click="collapseAll" :icon="Fold">
            全部收起
          </el-button>
        </div>
        
        <draggable
          v-model="fields"
          group="form-fields"
          item-key="id"
          handle=".drag-handle"
          @change="handleFieldsReorder"
        >
          <template #item="{ element: field, index }">
            <div 
              class="field-item"
              :class="{ 'active': selectedFieldId === field.id }"
              @click="selectField(field.id)"
            >
              <div class="field-header">
                <div class="field-info">
                  <el-icon class="drag-handle">
                    <Rank />
                  </el-icon>
                  <span class="field-type-icon">
                    <component :is="getFieldTypeIcon(field.type)" />
                  </span>
                  <span class="field-label">{{ field.label || '未命名字段' }}</span>
                  <el-tag size="small" :type="getFieldTypeColor(field.type)">
                    {{ getFieldTypeName(field.type) }}
                  </el-tag>
                </div>
                
                <div class="field-actions">
                  <el-button
                    text
                    size="small"
                    :icon="field.collapsed ? ArrowDown : ArrowUp"
                    @click.stop="toggleFieldCollapse(field.id)"
                  />
                  <el-button
                    text
                    size="small"
                    :icon="CopyDocument"
                    @click.stop="duplicateField(field.id)"
                  />
                  <el-button
                    text
                    size="small"
                    :icon="Delete"
                    @click.stop="removeField(field.id)"
                    type="danger"
                  />
                </div>
              </div>
              
              <!-- 字段预览 -->
              <div v-if="!field.collapsed" class="field-preview">
                <component
                  :is="getFieldPreviewComponent(field.type)"
                  :field="field"
                  :value="getPreviewValue(field)"
                  readonly
                  size="small"
                />
              </div>
            </div>
          </template>
        </draggable>
        
        <div v-if="fields.length === 0" class="empty-fields">
          <el-empty description="暂无字段，点击添加字段开始构建表单" />
        </div>
      </div>

      <!-- 右侧字段配置 -->
      <div class="config-panel">
        <div v-if="selectedField" class="field-config">
          <div class="config-header">
            <h4>字段配置</h4>
            <el-tag :type="getFieldTypeColor(selectedField.type)">
              {{ getFieldTypeName(selectedField.type) }}
            </el-tag>
          </div>
          
          <el-form :model="selectedField" label-width="100px" size="small">
            <!-- 基础配置 -->
            <el-divider content-position="left">基础配置</el-divider>
            
            <el-form-item label="字段标识">
              <el-input
                v-model="selectedField.name"
                placeholder="字段名称（英文）"
                @blur="validateFieldName"
              />
            </el-form-item>
            
            <el-form-item label="显示标签">
              <el-input
                v-model="selectedField.label"
                placeholder="字段显示标签"
              />
            </el-form-item>
            
            <el-form-item label="字段类型">
              <el-select
                v-model="selectedField.type"
                @change="handleFieldTypeChange"
                style="width: 100%"
              >
                <el-option
                  v-for="type in fieldTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                >
                  <span class="field-type-option">
                    <component :is="type.icon" />
                    {{ type.label }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="占位符">
              <el-input
                v-model="selectedField.placeholder"
                placeholder="请输入占位符文本"
              />
            </el-form-item>
            
            <el-form-item label="帮助文本">
              <el-input
                v-model="selectedField.help"
                type="textarea"
                :rows="2"
                placeholder="字段说明或帮助信息"
              />
            </el-form-item>
            
            <!-- 验证配置 -->
            <el-divider content-position="left">验证配置</el-divider>
            
            <el-form-item>
              <el-checkbox v-model="selectedField.required">
                必填字段
              </el-checkbox>
            </el-form-item>
            
            <el-form-item>
              <el-checkbox v-model="selectedField.disabled">
                禁用状态
              </el-checkbox>
            </el-form-item>
            
            <el-form-item>
              <el-checkbox v-model="selectedField.clearable">
                可清空
              </el-checkbox>
            </el-form-item>
            
            <!-- 字段类型特定配置 -->
            <template v-if="shouldShowTypeSpecificConfig">
              <el-divider content-position="left">类型配置</el-divider>
              
              <!-- 文本类型配置 -->
              <template v-if="['text', 'textarea'].includes(selectedField.type)">
                <el-form-item label="最大长度">
                  <el-input-number
                    v-model="selectedField.maxLength"
                    :min="1"
                    :max="10000"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item v-if="selectedField.type === 'textarea'" label="行数">
                  <el-input-number
                    v-model="selectedField.rows"
                    :min="2"
                    :max="20"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </template>
              
              <!-- 数字类型配置 -->
              <template v-if="selectedField.type === 'number'">
                <el-form-item label="最小值">
                  <el-input-number
                    v-model="selectedField.min"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="最大值">
                  <el-input-number
                    v-model="selectedField.max"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="步长">
                  <el-input-number
                    v-model="selectedField.step"
                    :min="0.01"
                    :step="0.01"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="精度">
                  <el-input-number
                    v-model="selectedField.precision"
                    :min="0"
                    :max="10"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </template>
              
              <!-- 选择类型配置 -->
              <template v-if="['select', 'radio', 'checkbox'].includes(selectedField.type)">
                <el-form-item label="选项配置">
                  <div class="options-config">
                    <div
                      v-for="(option, index) in selectedField.options"
                      :key="index"
                      class="option-item"
                    >
                      <el-input
                        v-model="option.label"
                        placeholder="选项显示文本"
                        size="small"
                      />
                      <el-input
                        v-model="option.value"
                        placeholder="选项值"
                        size="small"
                      />
                      <el-button
                        size="small"
                        :icon="Delete"
                        @click="removeOption(index)"
                        type="danger"
                        text
                      />
                    </div>
                    
                    <el-button
                      size="small"
                      :icon="Plus"
                      @click="addOption"
                      type="primary"
                      text
                    >
                      添加选项
                    </el-button>
                  </div>
                </el-form-item>
                
                <el-form-item v-if="selectedField.type === 'select'">
                  <el-checkbox v-model="selectedField.multiple">
                    多选模式
                  </el-checkbox>
                </el-form-item>
                
                <el-form-item v-if="selectedField.type === 'select'">
                  <el-checkbox v-model="selectedField.filterable">
                    可搜索
                  </el-checkbox>
                </el-form-item>
              </template>
              
              <!-- 评分类型配置 -->
              <template v-if="selectedField.type === 'rate'">
                <el-form-item label="最大分值">
                  <el-input-number
                    v-model="selectedField.max"
                    :min="1"
                    :max="10"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-checkbox v-model="selectedField.allowHalf">
                    允许半星
                  </el-checkbox>
                </el-form-item>
                
                <el-form-item>
                  <el-checkbox v-model="selectedField.showText">
                    显示文字
                  </el-checkbox>
                </el-form-item>
                
                <el-form-item v-if="selectedField.showText" label="评分文字">
                  <el-input
                    v-model="selectedField.textsInput"
                    placeholder="用逗号分隔，如：很差,差,一般,好,很好"
                    @blur="updateTextsArray"
                  />
                </el-form-item>
              </template>
              
              <!-- 滑块类型配置 -->
              <template v-if="selectedField.type === 'slider'">
                <el-form-item label="最小值">
                  <el-input-number
                    v-model="selectedField.min"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="最大值">
                  <el-input-number
                    v-model="selectedField.max"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="步长">
                  <el-input-number
                    v-model="selectedField.step"
                    :min="1"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-checkbox v-model="selectedField.showStops">
                    显示间断点
                  </el-checkbox>
                </el-form-item>
              </template>
            </template>
            
            <!-- 条件显示配置 -->
            <el-divider content-position="left">高级配置</el-divider>
            
            <el-form-item>
              <el-checkbox v-model="showConditionConfig">
                启用条件显示
              </el-checkbox>
            </el-form-item>
            
            <template v-if="showConditionConfig">
              <el-form-item label="依赖字段">
                <el-select
                  v-model="selectedField.showWhen?.field"
                  placeholder="选择依赖的字段"
                  style="width: 100%"
                >
                  <el-option
                    v-for="field in availableFields"
                    :key="field.name"
                    :label="field.label"
                    :value="field.name"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="条件操作">
                <el-select
                  v-model="selectedField.showWhen?.operator"
                  style="width: 100%"
                >
                  <el-option label="等于" value="eq" />
                  <el-option label="不等于" value="ne" />
                  <el-option label="包含" value="in" />
                  <el-option label="不包含" value="not-in" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="条件值">
                <el-input
                  v-model="selectedField.showWhen?.value"
                  placeholder="输入条件值"
                />
              </el-form-item>
            </template>
          </el-form>
        </div>
        
        <div v-else class="no-selection">
          <el-empty description="请选择左侧字段进行配置" />
        </div>
      </div>
    </div>

    <!-- 导入模板对话框 -->
    <el-dialog v-model="showImportDialog" title="导入字段模板" width="600px">
      <div class="template-list">
        <div
          v-for="template in fieldTemplates"
          :key="template.id"
          class="template-item"
          @click="selectTemplate(template)"
        >
          <div class="template-header">
            <h4>{{ template.name }}</h4>
            <el-tag>{{ template.fields.length }} 个字段</el-tag>
          </div>
          <p>{{ template.description }}</p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button type="primary" @click="importTemplate" :disabled="!selectedTemplate">
          导入
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Document, Download, Fold, Rank, ArrowDown, ArrowUp,
  CopyDocument, Delete, EditPen, Calendar, Clock, Star,
  Switch, Upload, Select, Grid
} from '@element-plus/icons-vue'
// import draggable from 'vuedraggable'

// 类型定义
interface FormField {
  id: string
  name: string
  type: string
  label: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  clearable?: boolean
  help?: string
  collapsed?: boolean
  
  // 类型特定属性
  maxLength?: number
  rows?: number
  min?: number
  max?: number
  step?: number
  precision?: number
  options?: Array<{ label: string; value: any; disabled?: boolean }>
  multiple?: boolean
  filterable?: boolean
  allowHalf?: boolean
  showText?: boolean
  texts?: string[]
  textsInput?: string
  showStops?: boolean
  
  // 条件显示
  showWhen?: {
    field: string
    value: any
    operator?: 'eq' | 'ne' | 'in' | 'not-in'
  }
}

interface FieldTemplate {
  id: string
  name: string
  description: string
  fields: FormField[]
}

// Props
interface Props {
  modelValue: FormField[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => []
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [fields: FormField[]]
  'field-selected': [field: FormField]
  'schema-exported': [schema: any]
}>()

// 响应式数据
const fields = ref<FormField[]>([...props.modelValue])
const selectedFieldId = ref<string>('')
const showImportDialog = ref(false)
const showConditionConfig = ref(false)
const selectedTemplate = ref<FieldTemplate | null>(null)

// 字段类型定义
const fieldTypes = [
  { label: '文本输入', value: 'text', icon: EditPen },
  { label: '多行文本', value: 'textarea', icon: Document },
  { label: '数字输入', value: 'number', icon: Grid },
  { label: '下拉选择', value: 'select', icon: Select },
  { label: '单选按钮', value: 'radio', icon: Select },
  { label: '多选框', value: 'checkbox', icon: Select },
  { label: '开关', value: 'switch', icon: Switch },
  { label: '日期', value: 'date', icon: Calendar },
  { label: '时间', value: 'time', icon: Clock },
  { label: '日期时间', value: 'datetime', icon: Calendar },
  { label: '评分', value: 'rate', icon: Star },
  { label: '滑块', value: 'slider', icon: Grid },
  { label: '文件上传', value: 'upload', icon: Upload }
]

// 字段模板
const fieldTemplates: FieldTemplate[] = [
  {
    id: 'basic-info',
    name: '基础信息模板',
    description: '包含姓名、年龄、性别等基础字段',
    fields: [
      {
        id: 'name',
        name: 'name',
        type: 'text',
        label: '姓名',
        required: true,
        placeholder: '请输入姓名'
      },
      {
        id: 'age',
        name: 'age',
        type: 'number',
        label: '年龄',
        required: true,
        min: 0,
        max: 150
      },
      {
        id: 'gender',
        name: 'gender',
        type: 'radio',
        label: '性别',
        required: true,
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' }
        ]
      }
    ]
  },
  {
    id: 'assessment-scale',
    name: '评估量表模板',
    description: '评分类字段模板，适用于能力评估',
    fields: [
      {
        id: 'cognitive_score',
        name: 'cognitive_score',
        type: 'rate',
        label: '认知能力评分',
        required: true,
        max: 5,
        showText: true,
        texts: ['很差', '差', '一般', '好', '很好']
      },
      {
        id: 'physical_score',
        name: 'physical_score',
        type: 'slider',
        label: '身体机能评分',
        required: true,
        min: 0,
        max: 100,
        step: 5
      }
    ]
  }
]

// 计算属性
const selectedField = computed(() => {
  return fields.value.find(field => field.id === selectedFieldId.value)
})

const availableFields = computed(() => {
  return fields.value.filter(field => field.id !== selectedFieldId.value)
})

const shouldShowTypeSpecificConfig = computed(() => {
  if (!selectedField.value) return false
  
  const configTypes = [
    'text', 'textarea', 'number', 'select', 'radio', 'checkbox',
    'rate', 'slider', 'upload', 'cascader', 'tree-select'
  ]
  
  return configTypes.includes(selectedField.value.type)
})

// 方法
const addField = () => {
  const newField: FormField = {
    id: `field_${Date.now()}`,
    name: `field_${fields.value.length + 1}`,
    type: 'text',
    label: `字段 ${fields.value.length + 1}`,
    required: false,
    clearable: true
  }
  
  fields.value.push(newField)
  selectField(newField.id)
}

const selectField = (fieldId: string) => {
  selectedFieldId.value = fieldId
  const field = fields.value.find(f => f.id === fieldId)
  if (field) {
    // 初始化条件显示配置
    if (!field.showWhen) {
      field.showWhen = {
        field: '',
        value: '',
        operator: 'eq'
      }
    }
    showConditionConfig.value = !!field.showWhen.field
    
    // 初始化评分文字输入
    if (field.type === 'rate' && field.texts) {
      field.textsInput = field.texts.join(',')
    }
    
    emit('field-selected', field)
  }
}

const removeField = (fieldId: string) => {
  ElMessageBox.confirm('确定要删除这个字段吗？', '确认删除', {
    type: 'warning'
  }).then(() => {
    const index = fields.value.findIndex(f => f.id === fieldId)
    if (index > -1) {
      fields.value.splice(index, 1)
      if (selectedFieldId.value === fieldId) {
        selectedFieldId.value = ''
      }
      ElMessage.success('字段已删除')
    }
  }).catch(() => {})
}

const duplicateField = (fieldId: string) => {
  const originalField = fields.value.find(f => f.id === fieldId)
  if (originalField) {
    const newField: FormField = {
      ...JSON.parse(JSON.stringify(originalField)),
      id: `field_${Date.now()}`,
      name: `${originalField.name}_copy`,
      label: `${originalField.label} 副本`
    }
    
    const index = fields.value.findIndex(f => f.id === fieldId)
    fields.value.splice(index + 1, 0, newField)
    selectField(newField.id)
  }
}

const toggleFieldCollapse = (fieldId: string) => {
  const field = fields.value.find(f => f.id === fieldId)
  if (field) {
    field.collapsed = !field.collapsed
  }
}

const collapseAll = () => {
  fields.value.forEach(field => {
    field.collapsed = true
  })
}

const handleFieldsReorder = () => {
  // 字段重新排序后的处理
}

const handleFieldTypeChange = () => {
  if (!selectedField.value) return
  
  // 重置类型特定的属性
  const field = selectedField.value
  
  // 清除旧的类型特定属性
  delete field.maxLength
  delete field.rows
  delete field.min
  delete field.max
  delete field.step
  delete field.precision
  delete field.options
  delete field.multiple
  delete field.filterable
  delete field.allowHalf
  delete field.showText
  delete field.texts
  delete field.showStops
  
  // 根据新类型设置默认属性
  switch (field.type) {
    case 'textarea':
      field.rows = 4
      break
    case 'number':
      field.min = 0
      field.step = 1
      break
    case 'select':
    case 'radio':
    case 'checkbox':
      field.options = [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' }
      ]
      break
    case 'rate':
      field.max = 5
      field.allowHalf = false
      field.showText = false
      break
    case 'slider':
      field.min = 0
      field.max = 100
      field.step = 1
      break
  }
}

const validateFieldName = () => {
  if (!selectedField.value) return
  
  const field = selectedField.value
  const name = field.name.trim()
  
  if (!name) {
    ElMessage.error('字段名称不能为空')
    return
  }
  
  if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name)) {
    ElMessage.error('字段名称只能包含字母、数字和下划线，且不能以数字开头')
    return
  }
  
  const duplicate = fields.value.find(f => f.id !== field.id && f.name === name)
  if (duplicate) {
    ElMessage.error('字段名称已存在')
    return
  }
}

const addOption = () => {
  if (!selectedField.value?.options) {
    selectedField.value!.options = []
  }
  
  selectedField.value?.options?.push({
    label: `选项${selectedField.value?.options?.length + 1}`,
    value: `option_${selectedField.value?.options?.length + 1}`
  })
}

const removeOption = (index: number) => {
  if (selectedField.value?.options) {
    selectedField.value.options.splice(index, 1)
  }
}

const updateTextsArray = () => {
  if (selectedField.value?.textsInput) {
    selectedField.value.texts = selectedField.value.textsInput
      .split(',')
      .map(text => text.trim())
      .filter(text => text.length > 0)
  }
}

const getFieldTypeIcon = (type: string) => {
  const typeMap: Record<string, any> = {
    text: EditPen,
    textarea: Document,
    number: Grid,
    select: Select,
    radio: Select,
    checkbox: Select,
    switch: Switch,
    date: Calendar,
    time: Clock,
    datetime: Calendar,
    rate: Star,
    slider: Grid,
    upload: Upload
  }
  return typeMap[type] || EditPen
}

const getFieldTypeName = (type: string) => {
  const field = fieldTypes.find(t => t.value === type)
  return field?.label || type
}

const getFieldTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    text: '',
    textarea: 'info',
    number: 'success',
    select: 'warning',
    radio: 'warning',
    checkbox: 'warning',
    switch: 'danger',
    date: 'info',
    time: 'info',
    datetime: 'info',
    rate: 'success',
    slider: 'success',
    upload: 'info'
  }
  return colorMap[type] || ''
}

const getFieldPreviewComponent = (type: string) => {
  // 返回对应的预览组件名
  return 'div' // 简化实现，实际应该返回对应的组件
}

const getPreviewValue = (field: FormField) => {
  // 返回预览用的示例值
  switch (field.type) {
    case 'text':
    case 'textarea':
      return field.placeholder || '示例文本'
    case 'number':
      return field.min || 0
    case 'select':
    case 'radio':
      return field.options?.[0]?.value || ''
    case 'checkbox':
      return []
    case 'switch':
      return false
    case 'rate':
      return 3
    case 'slider':
      return (field.min || 0) + Math.floor(((field.max || 100) - (field.min || 0)) / 2)
    default:
      return ''
  }
}

const importFromTemplate = () => {
  showImportDialog.value = true
}

const selectTemplate = (template: FieldTemplate) => {
  selectedTemplate.value = template
}

const importTemplate = () => {
  if (selectedTemplate.value) {
    const newFields = selectedTemplate.value.fields.map(field => ({
      ...field,
      id: `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }))
    
    fields.value.push(...newFields)
    showImportDialog.value = false
    selectedTemplate.value = null
    
    ElMessage.success(`已导入 ${newFields.length} 个字段`)
  }
}

const exportSchema = () => {
  const schema = {
    version: '1.0',
    fields: fields.value,
    meta: {
      createdAt: new Date().toISOString(),
      fieldCount: fields.value.length
    }
  }
  
  emit('schema-exported', schema)
  
  // 下载JSON文件
  const blob = new Blob([JSON.stringify(schema, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `form-schema-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('配置已导出')
}

// 监听器
watch(fields, (newFields) => {
  emit('update:modelValue', newFields)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  fields.value = [...newValue]
}, { deep: true })
</script>

<style scoped>
.form-field-builder {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.builder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.builder-header h3 {
  margin: 0;
}

.builder-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.fields-panel {
  width: 400px;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.config-panel {
  flex: 1;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.field-item {
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
}

.field-item:hover {
  background-color: #f8f9fa;
}

.field-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #409eff;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.field-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.drag-handle {
  cursor: move;
  color: #c0c4cc;
}

.drag-handle:hover {
  color: #409eff;
}

.field-type-icon {
  color: #909399;
}

.field-label {
  font-weight: 500;
  flex: 1;
}

.field-actions {
  display: flex;
  gap: 4px;
}

.field-preview {
  padding: 0 16px 12px 40px;
}

.empty-fields {
  padding: 40px 16px;
  text-align: center;
}

.field-config {
  padding: 16px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.config-header h4 {
  margin: 0;
}

.no-selection {
  padding: 40px;
  text-align: center;
}

.field-type-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.options-config {
  width: 100%;
}

.option-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.template-list {
  max-height: 400px;
  overflow-y: auto;
}

.template-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-header h4 {
  margin: 0;
}

.template-item p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #409eff;
}
</style>