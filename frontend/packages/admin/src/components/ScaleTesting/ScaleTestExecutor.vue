<template>
  <div class="scale-test-executor">
    <div class="test-header">
      <h3>量表三阶段测试</h3>
      <div class="header-actions">
        <el-button @click="resetForm" :icon="Refresh">
          重置
        </el-button>
        <el-button @click="getRecommendations" :icon="BellFilled">
          获取建议
        </el-button>
        <el-button 
          @click="executeTest" 
          type="primary" 
          :loading="testing"
          :icon="VideoPlay"
        >
          {{ testing ? '执行中...' : '开始测试' }}
        </el-button>
      </div>
    </div>

    <div class="test-content">
      <!-- 测试配置 -->
      <div class="test-config">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>测试配置</span>
              <el-tag :type="getTestPhaseColor(testRequest.testPhase)">
                {{ getTestPhaseName(testRequest.testPhase) }}
              </el-tag>
            </div>
          </template>

          <el-form 
            ref="formRef" 
            :model="testRequest" 
            :rules="formRules"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="表名" prop="tableName">
                  <el-input
                    v-model="testRequest.tableName"
                    placeholder="请输入要测试的表名"
                    clearable
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="测试阶段" prop="testPhase">
                  <el-select 
                    v-model="testRequest.testPhase" 
                    style="width: 100%"
                    @change="handleTestPhaseChange"
                  >
                    <el-option
                      v-for="phase in testPhases"
                      :key="phase.value"
                      :label="phase.label"
                      :value="phase.value"
                    >
                      <span class="test-phase-option">
                        <component :is="phase.icon" />
                        {{ phase.label }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 数据测试配置 -->
            <el-row v-if="showDataTestConfig" :gutter="20">
              <el-col :span="8">
                <el-form-item label="测试数据量">
                  <el-input-number
                    v-model="testRequest.testDataSize"
                    :min="1"
                    :max="10000"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item label="并发用户数">
                  <el-input-number
                    v-model="testRequest.concurrentUsers"
                    :min="1"
                    :max="100"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item label="大数据量">
                  <el-input-number
                    v-model="testRequest.largeDataSize"
                    :min="100"
                    :max="100000"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 高级选项 -->
            <el-divider content-position="left">高级选项</el-divider>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item>
                  <el-checkbox v-model="testRequest.cleanupAfterTest">
                    测试后清理数据
                  </el-checkbox>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item>
                  <el-checkbox v-model="testOptions.verboseLogging">
                    详细日志模式
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item>
                  <el-checkbox v-model="testOptions.parallelExecution">
                    并行执行测试
                  </el-checkbox>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="测试超时">
                  <el-input-number
                    v-model="testOptions.testTimeoutSeconds"
                    :min="60"
                    :max="1800"
                    controls-position="right"
                    style="width: 100%"
                  />
                  <span class="field-suffix">秒</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <!-- 测试进度 -->
      <div v-if="testing || testResult" class="test-progress">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>测试进度</span>
              <el-tag v-if="testResult" :type="testResult.success ? 'success' : 'danger'">
                {{ testResult.statusDescription }}
              </el-tag>
            </div>
          </template>

          <!-- 进度条 -->
          <div v-if="testing" class="progress-container">
            <el-progress
              :percentage="progressPercentage"
              :status="progressStatus"
              :stroke-width="8"
            />
            <div class="progress-text">
              {{ currentPhaseDescription }}
            </div>
          </div>

          <!-- 阶段进度 -->
          <div class="phase-progress">
            <div 
              v-for="(phase, index) in testPhaseProgress"
              :key="phase.name"
              class="phase-item"
              :class="{ 
                'active': phase.status === 'running',
                'completed': phase.status === 'completed',
                'failed': phase.status === 'failed'
              }"
            >
              <div class="phase-icon">
                <el-icon v-if="phase.status === 'completed'">
                  <Check />
                </el-icon>
                <el-icon v-else-if="phase.status === 'failed'">
                  <Close />
                </el-icon>
                <el-icon v-else-if="phase.status === 'running'">
                  <Loading />
                </el-icon>
                <span v-else class="phase-number">{{ index + 1 }}</span>
              </div>
              <div class="phase-content">
                <div class="phase-name">{{ phase.name }}</div>
                <div class="phase-description">{{ phase.description }}</div>
                <div v-if="phase.duration" class="phase-duration">
                  耗时: {{ phase.duration }}ms
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>测试结果</span>
              <div class="result-actions">
                <el-button @click="downloadReport" :icon="Download" size="small">
                  下载报告
                </el-button>
                <el-button @click="shareResult" :icon="Share" size="small">
                  分享结果
                </el-button>
              </div>
            </div>
          </template>

          <!-- 总览 -->
          <div class="result-overview">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-statistic title="测试状态" :value="testResult.success ? '通过' : '失败'">
                  <template #suffix>
                    <el-icon :color="testResult.success ? '#67c23a' : '#f56c6c'">
                      <component :is="testResult.success ? 'SuccessFilled' : 'CircleCloseFilled'" />
                    </el-icon>
                  </template>
                </el-statistic>
              </el-col>
              
              <el-col :span="6">
                <el-statistic title="通过率" :value="testResult.passRate" suffix="%">
                  <template #suffix>
                    <span>%</span>
                  </template>
                </el-statistic>
              </el-col>
              
              <el-col :span="6">
                <el-statistic title="执行时间" :value="testResult.executionTimeMs" suffix="ms" />
              </el-col>
              
              <el-col :span="6">
                <el-statistic 
                  title="阶段进度" 
                  :value="`${testResult.testPhasesPassed}/${testResult.totalTestPhases}`" 
                />
              </el-col>
            </el-row>
          </div>

          <!-- 详细结果 -->
          <el-divider>详细结果</el-divider>
          
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 结构测试结果 -->
            <el-tab-pane v-if="testResult.structureTestResult" label="结构测试" name="structure">
              <StructureTestResult :result="testResult.structureTestResult" />
            </el-tab-pane>
            
            <!-- 数据测试结果 -->
            <el-tab-pane v-if="testResult.dataFillTestResult" label="数据测试" name="data">
              <DataFillTestResult :result="testResult.dataFillTestResult" />
            </el-tab-pane>
            
            <!-- 性能测试结果 -->
            <el-tab-pane v-if="testResult.performanceTestResult" label="性能测试" name="performance">
              <PerformanceTestResult :result="testResult.performanceTestResult" />
            </el-tab-pane>
            
            <!-- 问题汇总 -->
            <el-tab-pane label="问题汇总" name="issues">
              <IssuesSummary :errors="testResult.errors" :warnings="testResult.warnings" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>
    </div>

    <!-- 测试建议对话框 -->
    <el-dialog v-model="showRecommendationsDialog" title="测试配置建议" width="800px">
      <TestRecommendations 
        v-if="recommendations" 
        :recommendations="recommendations"
        @apply="applyRecommendations"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, BellFilled, VideoPlay, Download, Share,
  Check, Close, Loading, SuccessFilled, CircleCloseFilled,
  Document, DataAnalysis, TrendCharts
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 导入子组件 - 临时注释掉缺失的组件
// import StructureTestResult from './StructureTestResult.vue'
// import DataFillTestResult from './DataFillTestResult.vue'
// import PerformanceTestResult from './PerformanceTestResult.vue'
// import IssuesSummary from './IssuesSummary.vue'
// import TestRecommendations from './TestRecommendations.vue'

// 类型定义
interface TestRequest {
  tableName: string
  testPhase: string
  testDataSize: number
  concurrentUsers: number
  largeDataSize: number
  cleanupAfterTest: boolean
}

interface TestResult {
  success: boolean
  message: string
  tableName: string
  executionTimeMs: number
  testPhasesPassed: number
  totalTestPhases: number
  passRate: number
  statusDescription: string
  errors: string[]
  warnings: string[]
  structureTestResult?: any
  dataFillTestResult?: any
  performanceTestResult?: any
}

interface TestPhaseProgress {
  name: string
  description: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  duration?: number
}

// 响应式数据
const formRef = ref<FormInstance>()
const testing = ref(false)
const activeTab = ref('structure')
const showRecommendationsDialog = ref(false)

const testRequest = reactive<TestRequest>({
  tableName: '',
  testPhase: 'FULL',
  testDataSize: 100,
  concurrentUsers: 10,
  largeDataSize: 1000,
  cleanupAfterTest: true
})

const testOptions = reactive({
  verboseLogging: false,
  parallelExecution: true,
  testTimeoutSeconds: 300
})

const testResult = ref<TestResult | null>(null)
const recommendations = ref<any>(null)
const currentPhase = ref(0)

// 测试阶段定义
const testPhases = [
  { label: '仅结构测试', value: 'STRUCTURE', icon: Document },
  { label: '数据填入测试', value: 'DATA', icon: DataAnalysis },
  { label: '性能测试', value: 'PERFORMANCE', icon: TrendCharts },
  { label: '完整测试', value: 'FULL', icon: Check }
]

const testPhaseProgress = ref<TestPhaseProgress[]>([
  {
    name: '结构测试',
    description: '检查表结构完整性和合理性',
    status: 'pending'
  },
  {
    name: '数据填入测试', 
    description: '测试数据插入、查询、更新操作',
    status: 'pending'
  },
  {
    name: '性能测试',
    description: '测试并发性能和大数据量处理',
    status: 'pending'
  }
])

// 表单验证规则
const formRules: FormRules = {
  tableName: [
    { required: true, message: '请输入表名', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '表名格式不正确', trigger: 'blur' }
  ],
  testPhase: [
    { required: true, message: '请选择测试阶段', trigger: 'change' }
  ]
}

// 计算属性
const showDataTestConfig = computed(() => {
  return ['DATA', 'PERFORMANCE', 'FULL'].includes(testRequest.testPhase)
})

const progressPercentage = computed(() => {
  if (!testing.value) return 0
  return Math.round((currentPhase.value / testPhaseProgress.value.length) * 100)
})

const progressStatus = computed(() => {
  if (testResult.value?.success === false) return 'exception'
  if (testing.value) return 'active'
  if (testResult.value?.success) return 'success'
  return undefined
})

const currentPhaseDescription = computed(() => {
  if (currentPhase.value < testPhaseProgress.value.length) {
    return testPhaseProgress.value[currentPhase.value].description
  }
  return '测试完成'
})

// 方法
const executeTest = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    testing.value = true
    testResult.value = null
    currentPhase.value = 0
    
    // 重置阶段状态
    testPhaseProgress.value.forEach(phase => {
      phase.status = 'pending'
      delete phase.duration
    })
    
    // 模拟测试执行
    await simulateTestExecution()
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  }
}

const simulateTestExecution = async () => {
  try {
    // 这里应该调用实际的API
    for (let i = 0; i < testPhaseProgress.value.length; i++) {
      if (!shouldExecutePhase(i)) continue
      
      currentPhase.value = i
      testPhaseProgress.value[i].status = 'running'
      
      // 模拟执行时间
      const startTime = Date.now()
      await new Promise(resolve => setTimeout(resolve, 2000))
      const duration = Date.now() - startTime
      
      testPhaseProgress.value[i].status = 'completed'
      testPhaseProgress.value[i].duration = duration
    }
    
    // 生成测试结果
    testResult.value = {
      success: true,
      message: '测试完成',
      tableName: testRequest.tableName,
      executionTimeMs: 6000,
      testPhasesPassed: 3,
      totalTestPhases: 3,
      passRate: 100,
      statusDescription: '测试通过',
      errors: [],
      warnings: ['建议添加索引以提高查询性能'],
      structureTestResult: {
        passed: true,
        errors: [],
        warnings: ['建议添加created_at审计字段'],
        testDetails: { columnCount: 8, validationsPassed: true }
      }
    }
    
  } catch (error) {
    ElMessage.error('测试执行失败: ' + error)
    testPhaseProgress.value[currentPhase.value].status = 'failed'
  } finally {
    testing.value = false
  }
}

const shouldExecutePhase = (phaseIndex: number) => {
  switch (testRequest.testPhase) {
    case 'STRUCTURE':
      return phaseIndex === 0
    case 'DATA':
      return phaseIndex <= 1
    case 'PERFORMANCE':
      return phaseIndex === 2
    case 'FULL':
      return true
    default:
      return true
  }
}

const getRecommendations = async () => {
  if (!testRequest.tableName) {
    ElMessage.warning('请先输入表名')
    return
  }
  
  try {
    // 这里应该调用API获取建议
    recommendations.value = {
      tableName: testRequest.tableName,
      recommendedTestDataSize: 100,
      recommendedConcurrentUsers: 10,
      estimatedDuration: {
        structureTest: '5-10秒',
        dataFillTest: '30-60秒',
        performanceTest: '2-5分钟'
      }
    }
    
    showRecommendationsDialog.value = true
    
  } catch (error) {
    ElMessage.error('获取建议失败')
  }
}

const applyRecommendations = (recs: any) => {
  testRequest.testDataSize = recs.recommendedTestDataSize
  testRequest.concurrentUsers = recs.recommendedConcurrentUsers
  testRequest.largeDataSize = recs.recommendedLargeDataSize
  
  showRecommendationsDialog.value = false
  ElMessage.success('已应用测试建议')
}

const resetForm = () => {
  formRef.value?.resetFields()
  testResult.value = null
  testing.value = false
  currentPhase.value = 0
}

const downloadReport = () => {
  if (!testResult.value) return
  
  const report = {
    testResult: testResult.value,
    testRequest: testRequest,
    timestamp: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `test-report-${testRequest.tableName}-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('测试报告已下载')
}

const shareResult = () => {
  if (!testResult.value) return
  
  const shareData = {
    title: `量表测试报告 - ${testRequest.tableName}`,
    text: `测试状态: ${testResult.value.success ? '通过' : '失败'}, 通过率: ${testResult.value.passRate}%`,
    url: window.location.href
  }
  
  if (navigator.share) {
    navigator.share(shareData)
  } else {
    navigator.clipboard.writeText(shareData.text + '\n' + shareData.url)
    ElMessage.success('测试结果已复制到剪贴板')
  }
}

const handleTestPhaseChange = () => {
  // 根据测试阶段调整配置
  switch (testRequest.testPhase) {
    case 'STRUCTURE':
      testRequest.testDataSize = 10
      break
    case 'DATA':
      testRequest.testDataSize = 100
      break
    case 'PERFORMANCE':
      testRequest.testDataSize = 1000
      testRequest.concurrentUsers = 20
      break
    case 'FULL':
      testRequest.testDataSize = 100
      testRequest.concurrentUsers = 10
      break
  }
}

const getTestPhaseColor = (phase: string) => {
  const colorMap: Record<string, string> = {
    STRUCTURE: 'info',
    DATA: 'warning',
    PERFORMANCE: 'danger',
    FULL: 'success'
  }
  return colorMap[phase] || ''
}

const getTestPhaseName = (phase: string) => {
  const phaseItem = testPhases.find(p => p.value === phase)
  return phaseItem?.label || phase
}

// 监听器
watch(() => testRequest.tableName, (newValue) => {
  if (newValue && testResult.value) {
    testResult.value = null // 清除之前的测试结果
  }
})
</script>

<style scoped>
.scale-test-executor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.test-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.test-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.test-config {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-phase-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.test-progress {
  margin-bottom: 24px;
}

.progress-container {
  margin-bottom: 24px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #606266;
}

.phase-progress {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.phase-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.phase-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.phase-item.completed {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.phase-item.failed {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.phase-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #909399;
}

.phase-item.active .phase-icon {
  background-color: #409eff;
  color: white;
}

.phase-item.completed .phase-icon {
  background-color: #67c23a;
  color: white;
}

.phase-item.failed .phase-icon {
  background-color: #f56c6c;
  color: white;
}

.phase-number {
  font-weight: bold;
}

.phase-content {
  flex: 1;
}

.phase-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.phase-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.phase-duration {
  color: #909399;
  font-size: 12px;
}

.test-result {
  margin-bottom: 24px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-overview {
  margin-bottom: 24px;
}

:deep(.el-statistic__content) {
  font-size: 24px;
}

:deep(.el-tabs__content) {
  padding: 16px 0;
}
</style>