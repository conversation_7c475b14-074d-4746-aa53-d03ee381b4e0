<template>
  <div class="drawdb-test-suite">
    <div class="test-header">
      <h3>DrawDB集成功能测试套件</h3>
      <div class="header-actions">
        <el-button @click="runAllTests" type="primary" :loading="running">
          {{ running ? '执行中...' : '运行所有测试' }}
        </el-button>
        <el-button @click="resetTests" :icon="Refresh">
          重置测试
        </el-button>
        <el-button @click="exportTestReport" :icon="Download">
          导出报告
        </el-button>
      </div>
    </div>

    <div class="test-content">
      <!-- 测试进度 -->
      <el-card class="test-progress-card">
        <template #header>
          <div class="card-header">
            <span>测试进度</span>
            <el-tag :type="getOverallStatus()">
              {{ getOverallStatusText() }}
            </el-tag>
          </div>
        </template>

        <el-progress
          :percentage="progressPercentage"
          :status="progressStatus"
          :stroke-width="12"
        />
        
        <div class="progress-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总测试数" :value="testResults.length" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="通过" :value="passedCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="失败" :value="failedCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="跳过" :value="skippedCount" />
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 测试列表 -->
      <el-card class="test-list-card">
        <template #header>
          <div class="card-header">
            <span>测试项目</span>
            <div class="test-filters">
              <el-radio-group v-model="statusFilter" size="small">
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="passed">通过</el-radio-button>
                <el-radio-button label="failed">失败</el-radio-button>
                <el-radio-button label="pending">待执行</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <div class="test-items">
          <div
            v-for="test in filteredTests"
            :key="test.id"
            class="test-item"
            :class="getTestItemClass(test)"
          >
            <div class="test-item-header">
              <div class="test-info">
                <el-icon class="test-icon">
                  <component :is="getTestIcon(test.status)" />
                </el-icon>
                <div class="test-details">
                  <div class="test-name">{{ test.name }}</div>
                  <div class="test-description">{{ test.description }}</div>
                </div>
              </div>
              
              <div class="test-actions">
                <el-button
                  v-if="test.status !== 'running'"
                  size="small"
                  @click="runSingleTest(test)"
                  :disabled="running"
                >
                  {{ test.status === 'pending' ? '运行' : '重新运行' }}
                </el-button>
                <el-button
                  v-if="test.status === 'failed' && test.error"
                  size="small"
                  type="danger"
                  text
                  @click="showTestError(test)"
                >
                  查看错误
                </el-button>
              </div>
            </div>

            <!-- 测试结果详情 -->
            <div v-if="test.status !== 'pending' && test.result" class="test-result">
              <div class="result-summary">
                <span class="result-label">执行时间:</span>
                <span class="result-value">{{ test.result.duration }}ms</span>
                
                <span v-if="test.result.assertions" class="result-label">断言:</span>
                <span v-if="test.result.assertions" class="result-value">
                  {{ test.result.assertions.passed }}/{{ test.result.assertions.total }}
                </span>
              </div>
              
              <div v-if="test.result.details" class="result-details">
                <el-collapse>
                  <el-collapse-item title="详细信息">
                    <pre>{{ JSON.stringify(test.result.details, null, 2) }}</pre>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>

            <!-- 运行中指示器 -->
            <div v-if="test.status === 'running'" class="test-running">
              <el-progress :percentage="50" :indeterminate="true" />
              <div class="running-text">测试执行中...</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- DrawDB集成演示 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span>DrawDB集成演示</span>
            <el-switch
              v-model="showDemo"
              active-text="显示演示"
              inactive-text="隐藏演示"
            />
          </div>
        </template>

        <div v-if="showDemo" class="demo-container">
          <DrawDBDesigner
            ref="drawdbRef"
            :ai-analysis-data="demoData"
            @sql-generated="handleSQLGenerated"
            @schema-changed="handleSchemaChanged"
            @deploy-requested="handleDeployRequested"
          />
        </div>
      </el-card>
    </div>

    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="showErrorDialog"
      title="测试错误详情"
      width="70%"
    >
      <div v-if="selectedTestError" class="error-details">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="错误类型">
            {{ selectedTestError.type }}
          </el-descriptions-item>
          <el-descriptions-item label="错误消息">
            {{ selectedTestError.message }}
          </el-descriptions-item>
          <el-descriptions-item label="堆栈跟踪" v-if="selectedTestError.stack">
            <pre class="error-stack">{{ selectedTestError.stack }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Download, CircleCheck, CircleClose, 
  Clock, Warning, Loading
} from '@element-plus/icons-vue'
import DrawDBDesigner from './DrawDBDesigner.vue'

// 类型定义
interface TestCase {
  id: string
  name: string
  description: string
  category: string
  status: 'pending' | 'running' | 'passed' | 'failed' | 'skipped'
  result?: TestResult
  error?: TestError
  priority: 'high' | 'medium' | 'low'
}

interface TestResult {
  duration: number
  assertions: {
    passed: number
    total: number
  }
  details?: any
}

interface TestError {
  type: string
  message: string
  stack?: string
}

// 响应式数据
const running = ref(false)
const statusFilter = ref('all')
const showDemo = ref(false)
const showErrorDialog = ref(false)
const selectedTestError = ref<TestError | null>(null)
const drawdbRef = ref()

// 测试用例定义
const testResults = ref<TestCase[]>([
  {
    id: 'iframe-load',
    name: 'iframe加载测试',
    description: '验证DrawDB iframe能够正常加载',
    category: '基础功能',
    status: 'pending',
    priority: 'high'
  },
  {
    id: 'communication-setup',
    name: '通信建立测试',
    description: '验证与DrawDB的postMessage通信能够正常建立',
    category: '通信',
    status: 'pending',
    priority: 'high'
  },
  {
    id: 'schema-import',
    name: '模式导入测试',
    description: '验证能够将AI分析结果导入到DrawDB中',
    category: 'AI集成',
    status: 'pending',
    priority: 'high'
  },
  {
    id: 'sql-export',
    name: 'SQL导出测试',
    description: '验证能够从DrawDB导出SQL语句',
    category: '导出功能',
    status: 'pending',
    priority: 'medium'
  },
  {
    id: 'settings-apply',
    name: '设置应用测试',
    description: '验证设置能够正确应用到DrawDB中',
    category: '设置管理',
    status: 'pending',
    priority: 'low'
  },
  {
    id: 'validation',
    name: '设计验证测试',
    description: '验证数据库设计验证功能',
    category: '验证功能',
    status: 'pending',
    priority: 'medium'
  },
  {
    id: 'staging-deploy',
    name: '临时库部署测试',
    description: '验证能够将设计部署到临时数据库',
    category: '部署功能',
    status: 'pending',
    priority: 'high'
  },
  {
    id: 'error-handling',
    name: '错误处理测试',
    description: '验证各种错误情况的处理',
    category: '错误处理',
    status: 'pending',
    priority: 'medium'
  }
])

// 演示数据
const demoData = reactive({
  tableName: 'user_assessment',
  fields: [
    {
      name: 'id',
      type: 'UUID',
      nullable: false,
      isPrimaryKey: true,
      comment: '主键ID'
    },
    {
      name: 'user_id',
      type: 'UUID',
      nullable: false,
      isPrimaryKey: false,
      comment: '用户ID'
    },
    {
      name: 'assessment_type',
      type: 'VARCHAR(50)',
      nullable: false,
      isPrimaryKey: false,
      comment: '评估类型'
    },
    {
      name: 'score',
      type: 'INTEGER',
      nullable: true,
      isPrimaryKey: false,
      comment: '评估分数'
    },
    {
      name: 'created_at',
      type: 'TIMESTAMP',
      nullable: false,
      isPrimaryKey: false,
      comment: '创建时间'
    }
  ]
})

// 计算属性
const filteredTests = computed(() => {
  if (statusFilter.value === 'all') {
    return testResults.value
  }
  return testResults.value.filter(test => test.status === statusFilter.value)
})

const progressPercentage = computed(() => {
  const total = testResults.value.length
  const completed = testResults.value.filter(t => 
    ['passed', 'failed', 'skipped'].includes(t.status)
  ).length
  return total === 0 ? 0 : Math.round((completed / total) * 100)
})

const progressStatus = computed(() => {
  if (failedCount.value > 0) return 'exception'
  if (progressPercentage.value === 100) return 'success'
  if (running.value) return undefined
  return undefined
})

const passedCount = computed(() => 
  testResults.value.filter(t => t.status === 'passed').length
)

const failedCount = computed(() => 
  testResults.value.filter(t => t.status === 'failed').length
)

const skippedCount = computed(() => 
  testResults.value.filter(t => t.status === 'skipped').length
)

// 方法
const runAllTests = async () => {
  running.value = true
  
  try {
    // 重置所有测试状态
    testResults.value.forEach(test => {
      test.status = 'pending'
      test.result = undefined
      test.error = undefined
    })

    // 按优先级顺序执行测试
    const sortedTests = [...testResults.value].sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })

    for (const test of sortedTests) {
      await runSingleTest(test)
      // 短暂延迟，让用户看到进度
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    const summary = `测试完成！通过: ${passedCount.value}, 失败: ${failedCount.value}, 跳过: ${skippedCount.value}`
    ElMessage.success(summary)
    
  } catch (error) {
    ElMessage.error('测试执行过程中发生异常')
    console.error('Test execution error:', error)
  } finally {
    running.value = false
  }
}

const runSingleTest = async (test: TestCase): Promise<void> => {
  test.status = 'running'
  const startTime = Date.now()

  try {
    const result = await executeTest(test)
    test.result = {
      duration: Date.now() - startTime,
      assertions: result.assertions || { passed: 1, total: 1 },
      details: result.details
    }
    test.status = 'passed'
  } catch (error: any) {
    test.status = 'failed'
    test.error = {
      type: error.name || 'TestError',
      message: error.message || '未知错误',
      stack: error.stack
    }
    test.result = {
      duration: Date.now() - startTime,
      assertions: { passed: 0, total: 1 }
    }
  }
}

const executeTest = async (test: TestCase): Promise<any> => {
  switch (test.id) {
    case 'iframe-load':
      return await testIframeLoad()
    case 'communication-setup':
      return await testCommunicationSetup()
    case 'schema-import':
      return await testSchemaImport()
    case 'sql-export':
      return await testSQLExport()
    case 'settings-apply':
      return await testSettingsApply()
    case 'validation':
      return await testValidation()
    case 'staging-deploy':
      return await testStagingDeploy()
    case 'error-handling':
      return await testErrorHandling()
    default:
      throw new Error(`未实现的测试: ${test.id}`)
  }
}

// 具体测试实现
const testIframeLoad = async () => {
  return new Promise((resolve, reject) => {
    if (!showDemo.value) {
      showDemo.value = true
    }
    
    setTimeout(() => {
      if (drawdbRef.value) {
        resolve({
          assertions: { passed: 1, total: 1 },
          details: { iframeLoaded: true }
        })
      } else {
        reject(new Error('DrawDB iframe 未能加载'))
      }
    }, 2000)
  })
}

const testCommunicationSetup = async () => {
  // 模拟通信测试
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        assertions: { passed: 1, total: 1 },
        details: { communicationEstablished: true }
      })
    }, 1000)
  })
}

const testSchemaImport = async () => {
  if (!drawdbRef.value) {
    throw new Error('DrawDB组件未就绪')
  }
  
  try {
    await drawdbRef.value.loadFromAI()
    return {
      assertions: { passed: 1, total: 1 },
      details: { schemaImported: true, tableCount: 1 }
    }
  } catch (error) {
    throw new Error('模式导入失败: ' + error)
  }
}

const testSQLExport = async () => {
  if (!drawdbRef.value) {
    throw new Error('DrawDB组件未就绪')
  }
  
  try {
    await drawdbRef.value.previewSQL()
    return {
      assertions: { passed: 1, total: 1 },
      details: { sqlExported: true }
    }
  } catch (error) {
    throw new Error('SQL导出失败: ' + error)
  }
}

const testSettingsApply = async () => {
  // 模拟设置测试
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        assertions: { passed: 1, total: 1 },
        details: { settingsApplied: true }
      })
    }, 800)
  })
}

const testValidation = async () => {
  // 模拟验证测试
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        assertions: { passed: 1, total: 1 },
        details: { validationPassed: true, score: 85 }
      })
    }, 1200)
  })
}

const testStagingDeploy = async () => {
  // 模拟部署测试
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟偶尔失败
      if (Math.random() > 0.8) {
        reject(new Error('部署到临时库失败：连接超时'))
      } else {
        resolve({
          assertions: { passed: 1, total: 1 },
          details: { deployedToStaging: true, tableName: 'test_table' }
        })
      }
    }, 1500)
  })
}

const testErrorHandling = async () => {
  // 模拟错误处理测试
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        assertions: { passed: 3, total: 3 },
        details: { 
          invalidSQLHandled: true,
          communicationErrorHandled: true,
          timeoutHandled: true
        }
      })
    }, 1000)
  })
}

const resetTests = () => {
  testResults.value.forEach(test => {
    test.status = 'pending'
    test.result = undefined
    test.error = undefined
  })
  statusFilter.value = 'all'
  ElMessage.info('测试已重置')
}

const exportTestReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: testResults.value.length,
      passed: passedCount.value,
      failed: failedCount.value,
      skipped: skippedCount.value,
      passRate: Math.round((passedCount.value / testResults.value.length) * 100)
    },
    tests: testResults.value.map(test => ({
      id: test.id,
      name: test.name,
      status: test.status,
      duration: test.result?.duration,
      error: test.error?.message
    }))
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `drawdb-test-report-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('测试报告已导出')
}

const showTestError = (test: TestCase) => {
  selectedTestError.value = test.error || null
  showErrorDialog.value = true
}

const getTestIcon = (status: string) => {
  switch (status) {
    case 'passed':
      return CircleCheck
    case 'failed':
      return CircleClose
    case 'running':
      return Loading
    case 'skipped':
      return Warning
    default:
      return Clock
  }
}

const getTestItemClass = (test: TestCase) => {
  return [
    'test-item-status',
    `test-${test.status}`,
    `priority-${test.priority}`
  ]
}

const getOverallStatus = () => {
  if (running.value) return 'warning'
  if (failedCount.value > 0) return 'danger'
  if (progressPercentage.value === 100) return 'success'
  return 'info'
}

const getOverallStatusText = () => {
  if (running.value) return '执行中'
  if (failedCount.value > 0) return '有失败'
  if (progressPercentage.value === 100) return '全部通过'
  return '待执行'
}

// DrawDB事件处理
const handleSQLGenerated = (sql: string) => {
  console.log('SQL Generated:', sql)
}

const handleSchemaChanged = (schema: any) => {
  console.log('Schema Changed:', schema)
}

const handleDeployRequested = (sql: string) => {
  console.log('Deploy Requested:', sql)
  ElMessage.info('部署请求已发送')
}

// 生命周期
onMounted(() => {
  ElMessage.info('DrawDB集成测试套件已加载')
})
</script>

<style scoped>
.drawdb-test-suite {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.test-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.test-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.test-progress-card,
.test-list-card,
.demo-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-filters {
  display: flex;
  gap: 8px;
}

.progress-stats {
  margin-top: 16px;
}

.test-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.test-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-item.test-passed {
  border-left: 4px solid #67c23a;
}

.test-item.test-failed {
  border-left: 4px solid #f56c6c;
}

.test-item.test-running {
  border-left: 4px solid #409eff;
  background-color: #ecf5ff;
}

.test-item.test-skipped {
  border-left: 4px solid #e6a23c;
}

.test-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.test-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.test-icon {
  font-size: 20px;
}

.test-details {
  flex: 1;
}

.test-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.test-description {
  color: #666;
  font-size: 14px;
}

.test-actions {
  display: flex;
  gap: 8px;
}

.test-result {
  padding: 0 16px 16px;
  border-top: 1px solid #f0f0f0;
}

.result-summary {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.result-label {
  font-weight: 500;
}

.result-value {
  color: #409eff;
}

.test-running {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.running-text {
  text-align: center;
  margin-top: 8px;
  color: #409eff;
  font-size: 14px;
}

.demo-container {
  height: 500px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.error-details {
  max-height: 400px;
  overflow-y: auto;
}

.error-stack {
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.priority-high {
  border-left-width: 6px;
}

.priority-medium {
  border-left-width: 4px;
}

.priority-low {
  border-left-width: 2px;
}

:deep(.el-progress-bar__outer) {
  border-radius: 6px;
}

:deep(.el-statistic__content) {
  font-size: 20px;
}
</style>