import axios, { AxiosInstance } from 'axios';
import { ElMessage } from 'element-plus';

// 创建用于公共API的axios实例（不需要认证）
const publicRequest: AxiosInstance = axios.create({
  baseURL: '', // 使用相对路径，依赖 Vite 代理
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 响应拦截器
publicRequest.interceptors.response.use(
  (response) => {
    // 如果返回的数据有 data 字段，直接返回 data
    return response.data;
  },
  (error) => {
    const { response } = error;
    
    if (response) {
      const { status, data } = response;
      
      // 处理错误，但不触发登录跳转
      switch (status) {
        case 404:
          console.warn('请求的资源不存在');
          break;
        case 500:
          console.error('服务器内部错误');
          break;
        default:
          // 只在需要时显示错误信息
          const errorMessage = data?.message || data?.error || '请求失败';
          console.error('公共API错误:', errorMessage);
      }
    } else if (error.request) {
      console.error('网络连接异常');
    } else {
      console.error('请求配置错误');
    }

    return Promise.reject(error);
  }
);

export default publicRequest;