<template>
  <div class="database-design-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Grid /></el-icon>
          数据库表结构设计
        </h1>
        <p class="page-description">
          使用可视化工具设计数据库表结构，支持从AI分析结果导入，一键生成并部署到测试环境
        </p>
      </div>
      
      <div class="header-actions">
        <el-button-group>
          <el-button @click="showImportDialog = true">
            <el-icon><FolderOpened /></el-icon>
            导入设计
          </el-button>
          <el-button @click="showHistoryDialog = true">
            <el-icon><Clock /></el-icon>
            历史记录
          </el-button>
          <el-button type="primary" @click="createNewDesign">
            <el-icon><Plus /></el-icon>
            新建设计
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 工作区域 -->
    <div class="workspace">
      <!-- 左侧面板 -->
      <div class="left-panel" v-if="showLeftPanel">
        <el-tabs v-model="activeTab" tab-position="left">
          <!-- AI分析标签 -->
          <el-tab-pane label="AI分析" name="ai-analysis">
            <div class="tab-content">
              <h3>量表AI分析结果</h3>
              
              <div v-if="aiAnalysisResult" class="analysis-result">
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="表名">
                    {{ aiAnalysisResult.tableName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="表注释">
                    {{ aiAnalysisResult.tableComment }}
                  </el-descriptions-item>
                  <el-descriptions-item label="字段数量">
                    {{ aiAnalysisResult.fields?.length || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="置信度">
                    <el-progress 
                      :percentage="aiAnalysisResult.confidence || 0" 
                      :color="getConfidenceColor(aiAnalysisResult.confidence)"
                    />
                  </el-descriptions-item>
                </el-descriptions>
                
                <div class="fields-list">
                  <h4>字段列表</h4>
                  <el-table :data="aiAnalysisResult.fields" size="small">
                    <el-table-column prop="name" label="字段名" width="120" />
                    <el-table-column prop="type" label="类型" width="100" />
                    <el-table-column prop="nullable" label="可空" width="60">
                      <template #default="{ row }">
                        <el-tag :type="row.nullable ? 'info' : 'success'" size="small">
                          {{ row.nullable ? '是' : '否' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="comment" label="备注" />
                  </el-table>
                </div>
                
                <div class="analysis-actions">
                  <el-button 
                    type="primary" 
                    @click="loadAIAnalysis"
                    :loading="loading.loadAI"
                  >
                    加载到设计器
                  </el-button>
                  <el-button @click="showAnalysisSQL">
                    查看AI生成的SQL
                  </el-button>
                </div>
              </div>
              
              <el-empty v-else description="暂无AI分析结果">
                <el-button type="primary" @click="uploadForAnalysis">
                  上传文档进行AI分析
                </el-button>
              </el-empty>
            </div>
          </el-tab-pane>
          
          <!-- 模板库标签 -->
          <el-tab-pane label="模板库" name="templates">
            <div class="tab-content">
              <h3>数据库模板</h3>
              
              <div class="template-list">
                <div 
                  v-for="template in templates" 
                  :key="template.id"
                  class="template-item"
                  @click="loadTemplate(template)"
                >
                  <div class="template-icon">
                    <el-icon><Grid /></el-icon>
                  </div>
                  <div class="template-info">
                    <h4>{{ template.name }}</h4>
                    <p>{{ template.description }}</p>
                    <div class="template-meta">
                      <el-tag size="small">{{ template.complexity }}</el-tag>
                      <span class="table-count">{{ template.tableCount }} 表</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 部署记录标签 -->
          <el-tab-pane label="部署记录" name="deployments">
            <div class="tab-content">
              <h3>部署历史</h3>
              
              <div class="deployment-list">
                <div 
                  v-for="deployment in deployments" 
                  :key="deployment.id"
                  class="deployment-item"
                >
                  <div class="deployment-info">
                    <h4>{{ deployment.tableName }}</h4>
                    <p>{{ deployment.environment }} - {{ deployment.status }}</p>
                    <time>{{ formatTime(deployment.createdAt) }}</time>
                  </div>
                  <div class="deployment-actions">
                    <el-button size="small" @click="rollbackDeployment(deployment)">
                      回滚
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 主设计区域 -->
      <div class="main-content">
        <DrawDBDesigner
          ref="designerRef"
          :ai-analysis-data="aiAnalysisResult"
          :initial-sql="initialSQL"
          @sql-generated="handleSQLGenerated"
          @schema-changed="handleSchemaChanged"
          @deploy-requested="handleDeployRequested"
        />
      </div>

      <!-- 右侧面板（可折叠） -->
      <div class="right-panel" v-if="showRightPanel">
        <div class="panel-header">
          <h3>设计信息</h3>
          <el-button 
            text 
            @click="showRightPanel = false"
            :icon="Close"
          />
        </div>
        
        <div class="panel-content">
          <!-- 当前设计统计 -->
          <el-card class="info-card" shadow="never">
            <template #header>
              <span>设计统计</span>
            </template>
            <el-statistic title="表数量" :value="designStats.tableCount" />
            <el-statistic title="关系数量" :value="designStats.relationCount" />
            <el-statistic title="字段总数" :value="designStats.fieldCount" />
          </el-card>
          
          <!-- 验证结果 -->
          <el-card class="info-card" shadow="never" v-if="validationResult">
            <template #header>
              <span>验证结果</span>
            </template>
            <div class="validation-score">
              <el-progress 
                type="circle" 
                :percentage="validationResult.score"
                :color="getScoreColor(validationResult.score)"
              />
            </div>
            <div class="validation-details">
              <div v-if="validationResult.errors.length > 0">
                <h4>错误</h4>
                <ul>
                  <li v-for="error in validationResult.errors" :key="error">
                    {{ error }}
                  </li>
                </ul>
              </div>
              <div v-if="validationResult.warnings.length > 0">
                <h4>警告</h4>
                <ul>
                  <li v-for="warning in validationResult.warnings" :key="warning">
                    {{ warning }}
                  </li>
                </ul>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 导入对话框 -->
    <el-dialog v-model="showImportDialog" title="导入设计" width="600px">
      <el-tabs v-model="importType">
        <el-tab-pane label="从SQL导入" name="sql">
          <el-input
            v-model="importSQL"
            type="textarea"
            :rows="10"
            placeholder="粘贴您的SQL CREATE TABLE语句..."
          />
        </el-tab-pane>
        <el-tab-pane label="从JSON导入" name="json">
          <el-input
            v-model="importJSON"
            type="textarea"
            :rows="10"
            placeholder="粘贴JSON格式的表结构..."
          />
        </el-tab-pane>
        <el-tab-pane label="从文件导入" name="file">
          <el-upload
            class="upload-area"
            drag
            :auto-upload="false"
            :on-change="handleFileImport"
            accept=".sql,.json"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .sql 和 .json 格式文件
              </div>
            </template>
          </el-upload>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button type="primary" @click="executeImport">导入</el-button>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="showHistoryDialog" title="设计历史" width="800px">
      <el-table :data="designHistory">
        <el-table-column prop="name" label="设计名称" />
        <el-table-column prop="tableName" label="表名" />
        <el-table-column prop="tableCount" label="表数量" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="loadDesignHistory(row)">
              加载
            </el-button>
            <el-button size="small" type="danger" @click="deleteDesignHistory(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  Grid, FolderOpened, Clock, Plus, Close, UploadFilled
} from '@element-plus/icons-vue'
import DrawDBDesigner from '@/components/DatabaseDesigner/DrawDBDesigner.vue'

// 类型定义
interface FieldInfo {
  name: string
  type: string
  nullable: boolean
  comment?: string
}

interface AIAnalysisResult {
  tableName: string
  tableComment: string
  fields: FieldInfo[]
  confidence: number
  generatedSql?: string
}

interface ValidationResult {
  score: number
  errors: string[]
  warnings: string[]
}

// 路由
const router = useRouter()

// 引用
const designerRef = ref()

// 状态管理
const showLeftPanel = ref(true)
const showRightPanel = ref(true)
const showImportDialog = ref(false)
const showHistoryDialog = ref(false)
const activeTab = ref('ai-analysis')

// 加载状态
const loading = reactive({
  loadAI: false,
  import: false
})

// AI分析结果
const aiAnalysisResult = ref<AIAnalysisResult | null>(null)
const initialSQL = ref('')

// 设计统计
const designStats = reactive({
  tableCount: 0,
  relationCount: 0,
  fieldCount: 0
})

// 验证结果
const validationResult = ref<ValidationResult | null>(null)

// 导入相关
const importType = ref('sql')
const importSQL = ref('')
const importJSON = ref('')

// 模板数据
const templates = ref([
  {
    id: 1,
    name: '标准评估量表',
    description: '适用于一般的能力评估和健康评估',
    complexity: '简单',
    tableCount: 1
  },
  {
    id: 2,
    name: '多维度评估体系',
    description: '支持多维度、多层级的复杂评估',
    complexity: '中等',
    tableCount: 3
  },
  {
    id: 3,
    name: '完整评估平台',
    description: '包含用户、权限、评估、报告的完整体系',
    complexity: '复杂',
    tableCount: 8
  }
])

// 部署记录
const deployments = ref([
  {
    id: 1,
    tableName: 'elderly_assessment',
    environment: '临时环境',
    status: '运行中',
    createdAt: new Date()
  }
])

// 设计历史
const designHistory = ref([
  {
    id: 1,
    name: '老年人能力评估量表',
    tableName: 'elderly_assessment',
    tableCount: 1,
    status: '已部署',
    createdAt: new Date()
  }
])

// 生命周期
onMounted(() => {
  // 检查是否有来自路由的数据
  const routeData = router.currentRoute.value.params.aiData
  if (routeData) {
    try {
      aiAnalysisResult.value = JSON.parse(routeData as string)
    } catch (error) {
      console.error('解析路由数据失败:', error)
    }
  }
  
  // 加载历史数据
  loadHistoryData()
})

// 计算属性
const getConfidenceColor = (confidence: number) => {
  if (confidence >= 80) return '#67c23a'
  if (confidence >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getScoreColor = (score: number) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 方法
const createNewDesign = () => {
  aiAnalysisResult.value = null
  initialSQL.value = ''
  designerRef.value?.resetDesigner()
}

const loadAIAnalysis = () => {
  if (aiAnalysisResult.value) {
    designerRef.value?.loadFromAI()
  }
}

const showAnalysisSQL = () => {
  if (aiAnalysisResult.value?.generatedSql) {
    ElMessageBox.alert(
      aiAnalysisResult.value.generatedSql,
      'AI生成的SQL',
      { customClass: 'sql-dialog' }
    )
  }
}

const uploadForAnalysis = () => {
  router.push('/pdf-upload')
}

const loadTemplate = (template: any) => {
  // 加载模板逻辑
  ElMessage.info(`正在加载模板: ${template.name}`)
}

const rollbackDeployment = (deployment: any) => {
  ElMessageBox.confirm(
    `确定要回滚部署 "${deployment.tableName}" 吗？`,
    '确认回滚',
    { type: 'warning' }
  ).then(() => {
    ElMessage.success('回滚操作已提交')
  })
}

const formatTime = (time: Date) => {
  return time.toLocaleString()
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    '已部署': 'success',
    '部署中': 'warning',
    '失败': 'danger',
    '草稿': 'info'
  }
  return types[status] || 'info'
}

const handleSQLGenerated = (sql: string) => {
  console.log('SQL生成:', sql)
}

const handleSchemaChanged = (schema: any) => {
  // 更新设计统计
  designStats.tableCount = schema.tables?.length || 0
  designStats.relationCount = schema.relations?.length || 0
  designStats.fieldCount = schema.tables?.reduce((total: number, table: any) => 
    total + (table.columns?.length || 0), 0) || 0
}

const handleDeployRequested = async (sql: string) => {
  try {
    const response = await fetch('/api/scale-management/staging/create-table', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql,
        tableName: `design_${Date.now()}`,
        overwriteExisting: true
      })
    })
    
    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('部署到临时环境成功')
      
      // 添加到部署记录
      deployments.value.unshift({
        id: Date.now(),
        tableName: result.tableName,
        environment: '临时环境',
        status: '运行中',
        createdAt: new Date()
      })
    } else {
      ElMessage.error(`部署失败: ${result.message}`)
    }
  } catch (error) {
    console.error('部署失败:', error)
    ElMessage.error('部署失败')
  }
}

const executeImport = () => {
  let importData = ''
  
  switch (importType.value) {
    case 'sql':
      importData = importSQL.value
      break
    case 'json':
      importData = importJSON.value
      break
  }
  
  if (importData.trim()) {
    // 执行导入逻辑
    if (importType.value === 'sql') {
      initialSQL.value = importData
    }
    
    showImportDialog.value = false
    ElMessage.success('导入成功')
  } else {
    ElMessage.warning('请输入要导入的内容')
  }
}

const handleFileImport = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target?.result as string
    if (file.name.endsWith('.sql')) {
      importSQL.value = content
      importType.value = 'sql'
    } else if (file.name.endsWith('.json')) {
      importJSON.value = content
      importType.value = 'json'
    }
  }
  reader.readAsText(file.raw)
}

const loadDesignHistory = (design: any) => {
  // 加载历史设计
  ElMessage.info(`正在加载设计: ${design.name}`)
  showHistoryDialog.value = false
}

const deleteDesignHistory = (design: any) => {
  ElMessageBox.confirm(
    `确定要删除设计 "${design.name}" 吗？`,
    '确认删除',
    { type: 'warning' }
  ).then(() => {
    const index = designHistory.value.findIndex(item => item.id === design.id)
    if (index > -1) {
      designHistory.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  })
}

const loadHistoryData = () => {
  // 从本地存储或API加载历史数据
  const saved = localStorage.getItem('design-history')
  if (saved) {
    try {
      designHistory.value = JSON.parse(saved)
    } catch (error) {
      console.error('加载历史数据失败:', error)
    }
  }
}
</script>

<style scoped>
.database-design-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  color: #303133;
}

.page-description {
  margin: 4px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.workspace {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.main-content {
  flex: 1;
  overflow: hidden;
}

.right-panel {
  width: 280px;
  background: white;
  border-left: 1px solid #e4e7ed;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.panel-content {
  padding: 16px;
}

.info-card {
  margin-bottom: 16px;
}

.tab-content {
  padding: 16px;
}

.analysis-result {
  margin-top: 16px;
}

.fields-list {
  margin-top: 16px;
}

.fields-list h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.analysis-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-item {
  display: flex;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.template-icon {
  margin-right: 12px;
  color: #409eff;
}

.template-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
}

.template-info p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-count {
  font-size: 12px;
  color: #909399;
}

.deployment-list,
.deployment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.deployment-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
}

.deployment-info p {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #909399;
}

.deployment-info time {
  font-size: 12px;
  color: #c0c4cc;
}

.validation-score {
  text-align: center;
  margin-bottom: 16px;
}

.validation-details h4 {
  margin: 16px 0 8px 0;
  font-size: 14px;
}

.validation-details ul {
  margin: 0;
  padding-left: 16px;
}

.validation-details li {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.upload-area {
  width: 100%;
}

:deep(.el-tabs--left .el-tabs__content) {
  padding-left: 0;
}

:deep(.sql-dialog) {
  width: 80%;
}

:deep(.sql-dialog .el-message-box__message) {
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  white-space: pre-wrap;
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-size: 12px;
}
</style>