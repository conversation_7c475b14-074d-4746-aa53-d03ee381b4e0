<template>
  <div class="drawdb-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button 
            type="primary" 
            :icon="MagicStick" 
            @click="loadFromAI"
            :loading="loading.ai"
          >
            从AI分析加载
          </el-button>
          <el-button 
            :icon="Upload" 
            @click="exportToStaging"
            :loading="loading.export"
          >
            部署到临时库
          </el-button>
          <el-button 
            :icon="View" 
            @click="previewSQL"
          >
            预览SQL
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-right">
        <el-button-group>
          <el-button 
            :icon="CircleCheck" 
            @click="validateDesign"
            :loading="loading.validate"
          >
            验证设计
          </el-button>
          <el-button 
            :icon="Refresh" 
            @click="resetDesigner"
          >
            重置
          </el-button>
          <el-button 
            :icon="Setting" 
            @click="showSettings = true"
          >
            设置
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- DrawDB iframe -->
    <div class="designer-container">
      <iframe
        ref="drawdbFrame"
        :src="drawdbUrl"
        class="drawdb-iframe"
        frameborder="0"
        @load="handleIframeLoad"
      />
      
      <!-- 加载遮罩 -->
      <div v-if="loading.iframe" class="loading-overlay">
        <el-loading 
          text="正在加载数据库设计器..."
          svg-view-box="-10, -10, 50, 50"
          svg="<path class='circular' d='M 25,25 m -23,0 a 23,23 0 1,1 46,0 a 23,23 0 1,1 -46,0'/>"
        />
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="designer-statusbar">
      <div class="status-left">
        <span class="status-item">
          <el-icon><Grid /></el-icon>
          表数量: {{ designStats.tableCount }}
        </span>
        <span class="status-item">
          <el-icon><Connection /></el-icon>
          关系数量: {{ designStats.relationCount }}
        </span>
        <span class="status-item">
          <el-icon><Clock /></el-icon>
          最后更新: {{ lastUpdateTime }}
        </span>
      </div>
      
      <div class="status-right">
        <el-button 
          link 
          type="primary" 
          @click="showSQLDialog = true"
        >
          查看生成的SQL
        </el-button>
      </div>
    </div>

    <!-- SQL预览对话框 -->
    <el-dialog
      v-model="showSQLDialog"
      title="生成的SQL预览"
      width="80%"
      append-to-body
    >
      <div class="sql-preview">
        <div class="sql-actions">
          <el-button-group>
            <el-button @click="copySQLToClipboard">
              <el-icon><CopyDocument /></el-icon>
              复制SQL
            </el-button>
            <el-button @click="downloadSQL">
              <el-icon><Download /></el-icon>
              下载SQL文件
            </el-button>
            <el-button 
              type="primary" 
              @click="executeInStaging"
              :loading="loading.staging"
            >
              <el-icon><Lightning /></el-icon>
              在临时库执行
            </el-button>
          </el-button-group>
        </div>
        
        <div class="sql-content">
          <pre><code class="sql">{{ generatedSQL }}</code></pre>
        </div>
      </div>
    </el-dialog>

    <!-- 设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="设计器设置"
      width="500px"
    >
      <el-form :model="settings" label-width="120px">
        <el-form-item label="主题色彩">
          <el-color-picker v-model="settings.primaryColor" />
        </el-form-item>
        <el-form-item label="自动保存">
          <el-switch v-model="settings.autoSave" />
        </el-form-item>
        <el-form-item label="保存间隔">
          <el-input-number 
            v-model="settings.saveInterval" 
            :min="10" 
            :max="300" 
            :step="10"
          />
          <span class="form-text">秒</span>
        </el-form-item>
        <el-form-item label="网格对齐">
          <el-switch v-model="settings.snapToGrid" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  MagicStick, Upload, View, CircleCheck, Refresh, Setting,
  Grid, Connection, Clock, CopyDocument, Download, Lightning
} from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'

// Props
interface Props {
  aiAnalysisData?: any
  initialSQL?: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits
const emit = defineEmits<{
  sqlGenerated: [sql: string]
  schemaChanged: [schema: any]
  deployRequested: [sql: string]
}>()

// 响应式数据
const drawdbFrame = ref<HTMLIFrameElement>()
const showSQLDialog = ref(false)
const showSettings = ref(false)
const generatedSQL = ref('')

// 加载状态
const loading = reactive({
  iframe: true,
  ai: false,
  export: false,
  validate: false,
  staging: false
})

// 设计统计
const designStats = reactive({
  tableCount: 0,
  relationCount: 0
})

// 设置
const settings = reactive({
  primaryColor: '#409eff',
  autoSave: true,
  saveInterval: 30,
  snapToGrid: true
})

// 计算属性
const drawdbUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_DRAWDB_URL || 'http://localhost:3001'
  const params = new URLSearchParams({
    theme: 'light',
    readonly: props.readonly.toString(),
    lang: 'zh'
  })
  return `${baseUrl}?${params}`
})

const lastUpdateTime = ref<string>('')

// 剪贴板工具
const { copy } = useClipboard()

// iframe通信管理
class DrawDBCommunication {
  private messageHandlers = new Map<string, (data: any) => void>()
  private requestId = 0

  constructor(private iframe: HTMLIFrameElement) {
    window.addEventListener('message', this.handleMessage.bind(this))
  }

  private handleMessage(event: MessageEvent) {
    // 验证来源
    if (event.origin !== new URL(drawdbUrl.value).origin) {
      return
    }

    const { type, id, data } = event.data

    switch (type) {
      case 'schema-changed':
        this.handleSchemaChanged(data)
        break
      case 'sql-generated':
        this.handleSQLGenerated(data)
        break
      case 'stats-updated':
        this.handleStatsUpdated(data)
        break
      case 'response':
        if (id && this.messageHandlers.has(id)) {
          this.messageHandlers.get(id)!(data)
          this.messageHandlers.delete(id)
        }
        break
    }
  }

  private handleSchemaChanged(schema: any) {
    emit('schemaChanged', schema)
    lastUpdateTime.value = new Date().toLocaleTimeString()
  }

  private handleSQLGenerated(sql: string) {
    generatedSQL.value = sql
    emit('sqlGenerated', sql)
  }

  private handleStatsUpdated(stats: any) {
    designStats.tableCount = stats.tables || 0
    designStats.relationCount = stats.relations || 0
  }

  // 发送命令到DrawDB
  async sendCommand(command: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = `req_${++this.requestId}`
      
      this.messageHandlers.set(id, resolve)
      
      this.iframe.contentWindow?.postMessage({
        type: 'command',
        id,
        command,
        data
      }, '*')

      // 15秒超时
      setTimeout(() => {
        if (this.messageHandlers.has(id)) {
          this.messageHandlers.delete(id)
          reject(new Error('Command timeout'))
        }
      }, 15000)
    })
  }

  destroy() {
    window.removeEventListener('message', this.handleMessage.bind(this))
    this.messageHandlers.clear()
  }
}

let communication: DrawDBCommunication

// 生命周期
onMounted(() => {
  // 初始化设置
  loadSettings()
})

onBeforeUnmount(() => {
  communication?.destroy()
})

// iframe加载完成
const handleIframeLoad = () => {
  loading.iframe = false
  
  if (drawdbFrame.value) {
    communication = new DrawDBCommunication(drawdbFrame.value)
    
    // 应用设置
    applySettings()
    
    // 如果有初始数据，加载它
    if (props.aiAnalysisData) {
      loadFromAI()
    } else if (props.initialSQL) {
      loadSQLIntoDesigner(props.initialSQL)
    }
  }
}

// 从AI分析加载
const loadFromAI = async () => {
  if (!props.aiAnalysisData) {
    ElMessage.warning('没有AI分析数据可加载')
    return
  }

  loading.ai = true
  
  try {
    // 转换AI分析结果为DrawDB schema格式
    const schema = convertAIAnalysisToSchema(props.aiAnalysisData)
    
    await communication.sendCommand('load-schema', schema)
    
    ElMessage.success('AI分析数据加载成功')
  } catch (error) {
    console.error('加载AI分析数据失败:', error)
    ElMessage.error('加载AI分析数据失败')
  } finally {
    loading.ai = false
  }
}

// 导出到临时库
const exportToStaging = async () => {
  loading.export = true
  
  try {
    const sql = await communication.sendCommand('export-sql')
    
    if (sql) {
      emit('deployRequested', sql)
      ElMessage.success('SQL导出成功，准备部署到临时库')
    } else {
      ElMessage.warning('没有可导出的SQL')
    }
  } catch (error) {
    console.error('导出SQL失败:', error)
    ElMessage.error('导出SQL失败')
  } finally {
    loading.export = false
  }
}

// 预览SQL
const previewSQL = async () => {
  try {
    const sql = await communication.sendCommand('export-sql')
    generatedSQL.value = sql || '-- 暂无生成的SQL'
    showSQLDialog.value = true
  } catch (error) {
    console.error('获取SQL失败:', error)
    ElMessage.error('获取SQL失败')
  }
}

// 验证设计
const validateDesign = async () => {
  loading.validate = true
  
  try {
    const validation = await communication.sendCommand('validate-schema')
    
    if (validation.valid) {
      ElMessage.success(`设计验证通过 (得分: ${validation.score}/100)`)
    } else {
      ElMessageBox.alert(
        validation.errors.join('\n'),
        '设计验证失败',
        { type: 'warning' }
      )
    }
  } catch (error) {
    console.error('验证设计失败:', error)
    ElMessage.error('验证设计失败')
  } finally {
    loading.validate = false
  }
}

// 重置设计器
const resetDesigner = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置设计器吗？所有未保存的更改将丢失。',
      '确认重置',
      { type: 'warning' }
    )
    
    await communication.sendCommand('reset')
    
    designStats.tableCount = 0
    designStats.relationCount = 0
    generatedSQL.value = ''
    
    ElMessage.success('设计器已重置')
  } catch (error) {
    // 用户取消操作
  }
}

// 复制SQL到剪贴板
const copySQLToClipboard = async () => {
  try {
    await copy(generatedSQL.value)
    ElMessage.success('SQL已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下载SQL文件
const downloadSQL = () => {
  const blob = new Blob([generatedSQL.value], { type: 'text/sql' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `database_schema_${new Date().toISOString().split('T')[0]}.sql`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 在临时库执行
const executeInStaging = async () => {
  loading.staging = true
  
  try {
    // 这里调用后端API在临时库执行SQL
    const response = await fetch('/api/scale-management/staging/create-table', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: generatedSQL.value,
        tableName: `design_${Date.now()}`,
        overwriteExisting: true
      })
    })
    
    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('SQL在临时库执行成功')
      showSQLDialog.value = false
    } else {
      ElMessage.error(`执行失败: ${result.message}`)
    }
  } catch (error) {
    console.error('执行SQL失败:', error)
    ElMessage.error('执行SQL失败')
  } finally {
    loading.staging = false
  }
}

// 应用设置
const applySettings = async () => {
  try {
    await communication.sendCommand('apply-settings', {
      theme: {
        primaryColor: settings.primaryColor
      },
      editor: {
        snapToGrid: settings.snapToGrid
      }
    })
  } catch (error) {
    console.error('应用设置失败:', error)
  }
}

// 保存设置
const saveSettings = () => {
  localStorage.setItem('drawdb-settings', JSON.stringify(settings))
  applySettings()
  showSettings.value = false
  ElMessage.success('设置已保存')
}

// 加载设置
const loadSettings = () => {
  const saved = localStorage.getItem('drawdb-settings')
  if (saved) {
    Object.assign(settings, JSON.parse(saved))
  }
}

// 将SQL加载到设计器
const loadSQLIntoDesigner = async (sql: string) => {
  try {
    await communication.sendCommand('import-sql', { sql })
    ElMessage.success('SQL导入成功')
  } catch (error) {
    console.error('导入SQL失败:', error)
    ElMessage.error('导入SQL失败')
  }
}

// AI分析结果转换为DrawDB schema
const convertAIAnalysisToSchema = (analysisData: any) => {
  return {
    tables: [{
      name: analysisData.tableName || 'new_table',
      columns: analysisData.fields?.map((field: any) => ({
        name: field.name,
        type: field.type,
        nullable: field.nullable,
        primaryKey: field.isPrimaryKey,
        comment: field.comment
      })) || []
    }]
  }
}

// 暴露方法给父组件
defineExpose({
  loadFromAI,
  exportToStaging,
  previewSQL,
  resetDesigner
})
</script>

<style scoped>
.drawdb-designer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.designer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.designer-container {
  position: relative;
  flex: 1;
  overflow: hidden;
}

.drawdb-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.designer-statusbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-top: 1px solid #e4e7ed;
  font-size: 12px;
  color: #666;
}

.status-left {
  display: flex;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.sql-preview {
  max-height: 500px;
}

.sql-actions {
  margin-bottom: 16px;
}

.sql-content {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.sql-content pre {
  margin: 0;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.form-text {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}
</style>