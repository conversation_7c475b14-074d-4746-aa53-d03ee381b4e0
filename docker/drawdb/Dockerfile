# DrawDB Docker配置
FROM node:18-alpine as builder

# 设置工作目录
WORKDIR /app

# 克隆DrawDB源码
RUN apk add --no-cache git
RUN git clone https://github.com/drawdb-io/drawdb.git .

# 安装依赖并构建
RUN npm install
RUN npm run build

# 生产环境
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 自定义nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]