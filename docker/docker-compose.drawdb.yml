# DrawDB独立部署配置
version: '3.8'

services:
  drawdb:
    build:
      context: ./drawdb
      dockerfile: Dockerfile
    ports:
      - "3001:80"
    environment:
      - NODE_ENV=production
      - API_BASE_URL=http://localhost:8181
    volumes:
      - drawdb_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - assessment_network

  # 如果需要数据持久化服务
  drawdb-storage:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - drawdb_redis:/data
    restart: unless-stopped
    networks:
      - assessment_network

volumes:
  drawdb_data:
    driver: local
  drawdb_redis:
    driver: local

networks:
  assessment_network:
    external: true  # 使用已存在的网络